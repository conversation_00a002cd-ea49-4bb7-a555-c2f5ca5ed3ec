/* User Licenses List Component Styles - Modern Design System */

/* Filter Card Styles */
.filter-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.filter-card:hover {
  transform: translateY(-2px);
}

.filter-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-title {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* Modern Form Elements */
.modern-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.modern-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-input-group {
  position: relative;
}

.modern-input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  z-index: 2;
}

.modern-input-group .modern-input {
  padding-left: 2.5rem;
}

.modern-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.modern-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-select-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.8125rem;
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

/* Table Row Hover Effects */
.modern-table tbody tr {
  transition: all 0.3s ease;
  cursor: pointer;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Company Info with Avatar */
.company-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* User Info */
.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* Date Info */
.date-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Remaining Days Styling */
.remaining-days {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

/* Modern Button Hover Effects */
.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modern-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
  min-width: 2.5rem;
  height: 2.5rem;
}

/* Button Loading State */
.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Modern Avatar */
.modern-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

/* Modern Badges */
.modern-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--border-radius-pill);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.modern-badge-primary {
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid var(--primary);
}

.modern-badge-secondary {
  background-color: var(--secondary-light);
  color: var(--secondary);
  border: 1px solid var(--secondary);
}

.modern-badge-success {
  background-color: var(--success-light);
  color: var(--success);
  border: 1px solid var(--success);
}

.modern-badge-warning {
  background-color: var(--warning-light);
  color: var(--warning);
  border: 1px solid var(--warning);
}

.modern-badge-danger {
  background-color: var(--danger-light);
  color: var(--danger);
  border: 1px solid var(--danger);
}

.modern-badge-info {
  background-color: var(--info-light);
  color: var(--info);
  border: 1px solid var(--info);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state-icon {
  font-size: 4rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.empty-state-title {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.empty-state-text {
  color: var(--text-secondary);
  margin-bottom: 0;
  opacity: 0.8;
}

/* Modern Pagination */
.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.modern-pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 0.25rem;
}

.modern-page-item {
  display: flex;
}

.modern-page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  min-width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
}

.modern-page-link:hover:not(:disabled) {
  background-color: var(--primary-light);
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-1px);
}

.modern-page-item.active .modern-page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.modern-page-item.disabled .modern-page-link {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.pagination-info {
  text-align: center;
}

.pagination-info small {
  color: var(--text-secondary);
  font-size: 0.8125rem;
}

/* Animation Classes */
.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .modern-card-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  .modern-card-header .modern-btn {
    width: 100%;
  }

  .table-container {
    font-size: 0.875rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .modern-btn-sm {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }

  .company-info,
  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .modern-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .modern-pagination {
    flex-wrap: wrap;
    justify-content: center;
  }

  .modern-page-link {
    min-width: 2rem;
    height: 2rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* Dark Mode Support - Automatic Detection */
@media (prefers-color-scheme: dark) {
  .modern-input,
  .modern-select {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }

  .modern-input:focus,
  .modern-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
  }

  .modern-input-icon {
    color: var(--text-secondary);
  }

  .empty-state-icon {
    color: var(--text-secondary);
  }

  .empty-state-title,
  .empty-state-text {
    color: var(--text-secondary);
  }

  .modern-page-link {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }

  .modern-page-link:hover:not(:disabled) {
    background-color: var(--primary-light);
    border-color: var(--primary);
    color: var(--primary);
  }

  .modern-page-item.active .modern-page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
  }
}

/* Dark Mode Support - Manual Theme Toggle */
[data-theme="dark"] {
  .modern-input,
  .modern-select {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }

  .modern-input:focus,
  .modern-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
  }

  .modern-input-icon {
    color: var(--text-secondary);
  }

  .empty-state-icon {
    color: var(--text-secondary);
  }

  .empty-state-title,
  .empty-state-text {
    color: var(--text-secondary);
  }

  .modern-page-link {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }

  .modern-page-link:hover:not(:disabled) {
    background-color: var(--primary-light);
    border-color: var(--primary);
    color: var(--primary);
  }

  .modern-page-item.active .modern-page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
  }
}