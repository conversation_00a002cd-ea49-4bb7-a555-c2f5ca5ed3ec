/* ===== MODERN DELETED COMPANIES STYLES ===== */

/* Component Variables - Mevcut tema değ<PERSON> kullan */
.deleted-companies-container {
  --component-primary: var(--primary-color);
  --component-secondary: var(--secondary-color);
  --component-success: var(--success, #28a745);
  --component-danger: var(--danger, #dc3545);
  --component-warning: var(--warning, #ffc107);
  --component-info: var(--info, #4cc9f0);
  --component-bg: var(--card-bg-color);
  --component-text: var(--text-color);
  --component-text-muted: var(--text-muted);
  --component-border: var(--border-color);
  --component-shadow: var(--shadow-color);
  --component-border-radius: 12px;
  --component-transition: all 0.3s ease;
}

/* ===== HEADER SECTION ===== */
.deleted-companies-header {
  background: var(--component-bg);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px var(--component-shadow);
}

.header-content {
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: var(--component-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.header-icon i {
  font-size: 1.8rem;
}

.header-text {
  color: var(--component-text);
}

.header-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  color: var(--component-text);
}

.header-subtitle {
  font-size: 0.95rem;
  margin: 0.25rem 0 0 0;
  color: var(--component-text-muted);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stats-card {
  background: var(--component-primary);
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: var(--component-border-radius);
  text-align: center;
  box-shadow: 0 2px 8px rgba(67, 97, 238, 0.2);
}

.stats-number {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.stats-label {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: 0.25rem;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.btn-action {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  background: var(--component-bg);
  color: var(--component-text);
  font-weight: 500;
  text-decoration: none;
  transition: var(--component-transition);
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-action-primary {
  background: var(--component-primary);
  color: white;
  border-color: var(--component-primary);
}

.btn-action-secondary {
  background: transparent;
  color: var(--component-text);
  border-color: var(--component-border);
}

.btn-action:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--component-shadow);
}

.btn-action-primary:hover {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-action-secondary:hover {
  background: var(--component-border);
}

/* ===== CONTENT SECTION ===== */
.deleted-companies-content {
  padding: 0 2rem;
}

.content-wrapper {
  background: var(--component-bg);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  box-shadow: 0 2px 8px var(--component-shadow);
  overflow: hidden;
  min-height: 400px;
}

/* ===== LOADING STATE ===== */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: var(--component-bg);
  border-radius: var(--component-border-radius);
}

.loading-content {
  text-align: center;
  padding: 3rem;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto 2rem;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: var(--component-primary);
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: var(--component-info);
  animation-delay: 0.3s;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: var(--component-success);
  animation-delay: 0.6s;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--component-text);
  margin-bottom: 0.5rem;
}

.loading-subtitle {
  color: var(--component-text-muted);
  font-size: 0.95rem;
}

/* ===== EMPTY STATE ===== */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: var(--component-bg);
  border-radius: var(--component-border-radius);
}

.empty-content {
  text-align: center;
  padding: 3rem;
  max-width: 400px;
}

.empty-icon {
  width: 100px;
  height: 100px;
  margin: 0 auto 2rem;
  background: var(--component-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(67, 97, 238, 0.3);
}

.empty-icon i {
  font-size: 2.5rem;
  color: white;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--component-text);
  margin-bottom: 1rem;
}

.empty-subtitle {
  color: var(--component-text-muted);
  font-size: 1rem;
  line-height: 1.6;
}

/* ===== TABLE VIEW ===== */
.table-container {
  background: var(--component-bg);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px var(--component-shadow);
}

.table-wrapper {
  overflow-x: auto;
}

.deleted-companies-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--component-bg);
}

.deleted-companies-table thead {
  background: var(--component-primary);
}

.deleted-companies-table th {
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: white;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
}

.deleted-companies-table tbody tr {
  border-bottom: 1px solid var(--component-border);
  transition: var(--component-transition);
}

.deleted-companies-table tbody tr:hover {
  background: var(--bg-secondary, #f8f9fa);
}

.deleted-companies-table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border: none;
  color: var(--component-text);
}

.table-row {
  animation: slideInUp 0.6s ease-out both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--component-text);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.user-location {
  color: var(--component-text-muted);
  font-size: 0.8rem;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--component-text);
}

.contact-item i {
  width: 14px;
  color: var(--component-text-muted);
  font-size: 0.8rem;
}

/* Company Info */
.company-info {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}

.company-name {
  font-weight: 600;
  color: var(--component-text);
  font-size: 0.9rem;
}

.company-phone {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--component-text-muted);
  font-size: 0.8rem;
}

.no-data {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--component-text-muted);
  font-style: italic;
  font-size: 0.85rem;
}

/* Stats Info */
.stats-info {
  display: flex;
  justify-content: center;
}

.stat-item {
  text-align: center;
  padding: 0.5rem 0.75rem;
  background: var(--component-primary);
  border-radius: 8px;
  color: white;
  min-width: 50px;
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.2);
}

.stat-number {
  font-size: 1.1rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  opacity: 0.9;
  margin-top: 0.25rem;
}

/* Date Info */
.date-info {
  text-align: center;
}

.date-value {
  font-weight: 600;
  color: var(--component-text);
  font-size: 0.85rem;
}

.date-label {
  color: var(--component-text-muted);
  font-size: 0.7rem;
  margin-top: 0.25rem;
}

/* Action Buttons */
.action-buttons-table {
  display: flex;
  justify-content: center;
}

.btn-restore {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  background: var(--component-success);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--component-transition);
  font-size: 0.85rem;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.btn-restore:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  background: #218838;
}

.btn-restore:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* ===== CARD VIEW ===== */
.cards-container {
  background: var(--component-bg);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  padding: 1.5rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.company-card {
  background: var(--component-bg);
  border: 1px solid var(--component-border);
  border-radius: var(--component-border-radius);
  box-shadow: 0 2px 8px var(--component-shadow);
  overflow: hidden;
  transition: var(--component-transition);
  animation: slideInUp 0.6s ease-out both;
}

.company-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px var(--component-shadow);
}

.card-header-section {
  background: var(--component-primary);
  padding: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.company-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.3rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.card-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.4rem 0.8rem;
  border-radius: 16px;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--component-danger);
  animation: pulse 2s infinite;
}

.status-text {
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.card-content {
  padding: 1.5rem;
}

.company-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--component-text);
  margin-bottom: 0.5rem;
}

.company-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--component-text-muted);
  margin-bottom: 1.25rem;
  font-size: 0.9rem;
}

.contact-section {
  margin-bottom: 1.25rem;
}

.contact-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.6rem;
  padding: 0.5rem;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 6px;
  font-size: 0.85rem;
  color: var(--component-text);
}

.contact-row i {
  width: 16px;
  color: var(--component-text-muted);
}

.company-section {
  margin-bottom: 1.25rem;
  padding: 1rem;
  background: var(--bg-secondary, #f8f9fa);
  border: 1px solid var(--component-border);
  border-radius: 8px;
}

.section-title {
  font-weight: 600;
  color: var(--component-text);
  margin-bottom: 0.75rem;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.company-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  color: var(--component-text);
}

.company-detail i {
  width: 14px;
  color: var(--component-text-muted);
}

.stats-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
}

.stat-card {
  text-align: center;
  padding: 0.75rem;
  background: var(--component-primary);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.2);
}

.stat-number {
  font-size: 1.3rem;
  font-weight: 700;
  line-height: 1;
}

.stat-date {
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  opacity: 0.9;
  margin-top: 0.4rem;
}

.card-footer {
  padding: 1.25rem 1.5rem;
  background: var(--bg-secondary, #f8f9fa);
  border-top: 1px solid var(--component-border);
}

.btn-restore-card {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
  padding: 0.9rem;
  background: var(--component-success);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--component-transition);
  font-size: 0.9rem;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.btn-restore-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  background: #218838;
}

.btn-restore-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] .deleted-companies-table tbody tr:hover {
  background: var(--bg-secondary);
}

[data-theme="dark"] .contact-row,
[data-theme="dark"] .company-section,
[data-theme="dark"] .card-footer {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .deleted-companies-content {
    padding: 0 1.5rem;
  }

  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1rem 1.5rem;
  }

  .header-left {
    flex-direction: column;
    gap: 0.75rem;
  }

  .header-icon {
    width: 50px;
    height: 50px;
  }

  .header-icon i {
    font-size: 1.5rem;
  }

  .header-title {
    font-size: 1.5rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }

  .action-buttons {
    justify-content: center;
  }

  .deleted-companies-content {
    padding: 0 1rem;
  }

  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-content {
    padding: 1.25rem;
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .table-wrapper {
    font-size: 0.8rem;
  }

  .deleted-companies-table th,
  .deleted-companies-table td {
    padding: 0.75rem 0.5rem;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .contact-info {
    gap: 0.25rem;
  }

  .contact-item {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 1rem;
  }

  .header-title {
    font-size: 1.3rem;
  }

  .btn-action {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .company-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.1rem;
  }

  .company-name {
    font-size: 1.1rem;
  }

  .card-header-section {
    padding: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .card-footer {
    padding: 1rem;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.company-card {
  animation: fadeInScale 0.5s ease-out both;
}

/* Stagger animation for cards */
.company-card:nth-child(1) { animation-delay: 0.05s; }
.company-card:nth-child(2) { animation-delay: 0.1s; }
.company-card:nth-child(3) { animation-delay: 0.15s; }
.company-card:nth-child(4) { animation-delay: 0.2s; }
.company-card:nth-child(5) { animation-delay: 0.25s; }
.company-card:nth-child(6) { animation-delay: 0.3s; }

/* Focus states for accessibility */
.btn-action:focus,
.btn-restore:focus,
.btn-restore-card:focus {
  outline: 2px solid var(--component-primary);
  outline-offset: 2px;
}

/* Utility classes */
.fa-spin {
  animation: spin 1s linear infinite;
}
