﻿﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipTypeDal : EfCompanyEntityRepositoryBase<MembershipType, GymContext>, IMembershiptypeDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfMembershipTypeDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<PackageWithCountDto> GetPackagesByBranch(string branch)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                var packagesWithCount = from mt in context.MembershipTypes
                                       where mt.Branch == branch
                                       && mt.IsActive == true
                                       && mt.CompanyID == companyId
                                       select new PackageWithCountDto
                                       {
                                           MembershipTypeID = mt.MembershipTypeID,
                                           Branch = mt.Branch,
                                           TypeName = mt.TypeName,
                                           Day = mt.Day,
                                           Price = mt.Price,
                                           MemberCount = context.Memberships.Count(m =>
                                               m.MembershipTypeID == mt.MembershipTypeID
                                               && m.IsActive == true
                                               && m.EndDate > now
                                               && m.CompanyID == companyId)
                                       };

                // Sadece en az 1 üyesi olan paketleri döndür
                return packagesWithCount.Where(p => p.MemberCount > 0).ToList();
            }
        }

        public PaginatedResult<MembershipType> GetAllPaginated(MembershipTypePagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Base query - sadece aktif üyelik türleri
                var query = context.MembershipTypes
                    .Where(mt => mt.IsActive == true && mt.CompanyID == companyId);

                // Arama filtresi
                if (!string.IsNullOrEmpty(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    query = query.Where(mt =>
                        mt.Branch.ToLower().Contains(searchText) ||
                        mt.TypeName.ToLower().Contains(searchText));
                }

                // Branş filtresi
                if (!string.IsNullOrEmpty(parameters.Branch))
                {
                    query = query.Where(mt => mt.Branch == parameters.Branch);
                }

                // Fiyat aralığı filtresi
                if (parameters.MinPrice.HasValue)
                {
                    query = query.Where(mt => mt.Price >= parameters.MinPrice.Value);
                }
                if (parameters.MaxPrice.HasValue)
                {
                    query = query.Where(mt => mt.Price <= parameters.MaxPrice.Value);
                }

                // Süre aralığı filtresi
                if (parameters.MinDuration.HasValue)
                {
                    query = query.Where(mt => mt.Day >= parameters.MinDuration.Value);
                }
                if (parameters.MaxDuration.HasValue)
                {
                    query = query.Where(mt => mt.Day <= parameters.MaxDuration.Value);
                }

                // Sıralama - En son eklenen en üstte (ID'ye göre azalan)
                var orderedQuery = query.OrderByDescending(mt => mt.MembershipTypeID);

                // Toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MembershipType>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
        }
    }
}
