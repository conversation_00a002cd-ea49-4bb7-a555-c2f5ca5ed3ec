<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isSubmitting">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="modern-card slide-in-right">
        <div class="modern-card-header">
          <h5 class="mb-0">Yeni Üyelik Türü</h5>
        </div>
        
        <div class="modern-card-body">
          <form [formGroup]="membershipTypeAddForm">
            <div class="form-section">
              <h6 class="section-title">Branş Bilgileri</h6>
              
              <div class="modern-form-group">
                <label for="branch" class="modern-form-label">Branş Seçin</label>
                <select id="branch" formControlName="branch" class="modern-form-control">
                  <option value="">Seçiniz</option>
                  <option value="Fitness">Fitness</option>
                  <option value="Boks">Boks</option>
                  <option value="Muay Thai">Muay Thai</option>
                  <option value="MMA">MMA</option>
                  <option value="Karate">Karate</option>
                  <option value="Judo">Judo</option>
                  <option value="Güreş">Güreş</option>
                  <option value="Pilates">Pilates</option>
                  <option value="Yoga">Yoga</option>
                  <option value="KickBoks">KickBoks</option>
                  <option value="Aerobik">Aerobik</option>
                  <option value="CrossFit">CrossFit</option>
                </select>
                <small class="text-danger" *ngIf="membershipTypeAddForm.get('branch')?.invalid && membershipTypeAddForm.get('branch')?.touched">
                  Branş seçimi zorunludur
                </small>
              </div>

              <div class="modern-form-group">
                <label for="typeName" class="modern-form-label">Paket İsmi</label>
                <input
                  type="text"
                  id="typeName"
                  formControlName="typeName"
                  class="modern-form-control"
                  placeholder="1 AY, 1 AY ÖĞRENCİ vb."
                />
                <small class="text-danger" *ngIf="membershipTypeAddForm.get('typeName')?.invalid && membershipTypeAddForm.get('typeName')?.touched">
                  Paket ismi zorunludur
                </small>
              </div>
            </div>
            
            <div class="form-section">
              <h6 class="section-title">Süre ve Fiyat</h6>
              
              <div class="modern-form-group">
                <label class="modern-form-label">Geçerlilik Süresi</label>
                <div class="d-flex validity-selects">
                  <select id="year" formControlName="year" class="modern-form-control me-2">
                    <option *ngFor="let i of [].constructor(11); let year = index" [value]="year">
                      {{ year }} Yıl
                    </option>
                  </select>
                  
                  <select id="month" formControlName="month" class="modern-form-control me-2">
                    <option *ngFor="let i of [].constructor(13); let month = index" [value]="month">
                      {{ month }} Ay
                    </option>
                  </select>
                  
                  <select id="day" formControlName="day" class="modern-form-control">
                    <option *ngFor="let i of [].constructor(32); let day = index" [value]="day">
                      {{ day }} Gün
                    </option>
                  </select>
                </div>
                <small class="text-danger" *ngIf="(membershipTypeAddForm.get('year')?.value === 0 && membershipTypeAddForm.get('month')?.value === 0 && membershipTypeAddForm.get('day')?.value === 0) && (membershipTypeAddForm.get('year')?.touched || membershipTypeAddForm.get('month')?.touched || membershipTypeAddForm.get('day')?.touched)">
                  Geçerlilik süresi en az 1 gün olmalıdır
                </small>
              </div>

              <div class="modern-form-group">
                <label for="price" class="modern-form-label">Ücret</label>
                <div class="input-group">
                  <span class="input-group-text">₺</span>
                  <input
                    type="number"
                    id="price"
                    formControlName="price"
                    class="modern-form-control"
                    min="0"
                    placeholder="Ücret"
                  />
                </div>
                <small class="text-danger" *ngIf="membershipTypeAddForm.get('price')?.invalid && membershipTypeAddForm.get('price')?.touched">
                  Ücret bilgisi zorunludur
                </small>
              </div>
            </div>
          </form>
        </div>
        
        <div class="modern-card-footer">
          <button 
            class="modern-btn modern-btn-primary w-100" 
            (click)="add()" 
            [disabled]="!membershipTypeAddForm.valid || isSubmitting"
          >
            {{ isSubmitting ? 'Paket Kaydediliyor...' : 'Paketi Kaydet' }}
          </button>
        </div>
      </div>
    </div>

    <div class="col-md-8">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">Üyelik Türleri</h5>
        </div>
        
        <div class="modern-card-body">
          <app-membershiptype #membershipTypeList></app-membershiptype>
        </div>
      </div>
    </div>
  </div>
</div>
