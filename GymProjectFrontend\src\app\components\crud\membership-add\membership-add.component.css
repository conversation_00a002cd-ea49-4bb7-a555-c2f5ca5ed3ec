.last-membership-info {
    margin-top: 5px;
    font-size: 0.85em;
    color: #fc4c4c;
  }

  .form-group {
    margin-bottom: 0;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }

  /* Zorunlu alanlar için hata durumu */
  .form-control.ng-invalid.ng-touched {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }

  /* Titreşim animasyonu için sınıf */
  .shake-animation {
    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }
    20%, 40%, 60%, 80% { transform: translateX(6px); }
  }

  /* Loading spinner artık generic component'te yönetiliyor */

  /* Content Blur */
  .content-blur {
    filter: blur(3px);
    pointer-events: none;
    transition: filter 0.3s ease;
  }

  /* Fade In Animation */
  .fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Dark Mode Support */
  [data-theme="dark"] .loading-overlay {
    background-color: rgba(18, 18, 18, 0.8);
  }

  [data-theme="dark"] .content-blur {
    filter: blur(3px) brightness(0.7);
  }