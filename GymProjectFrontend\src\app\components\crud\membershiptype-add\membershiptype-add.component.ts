import { Component, OnInit, Output, EventEmitter, Inject, Optional, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MembershipTypeService } from '../../../services/membership-type.service';
import { ToastrService } from 'ngx-toastr';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MembershiptypeComponent } from '../../membershiptype/membershiptype.component';

@Component({
    selector: 'app-membershiptype-add',
    templateUrl: './membershiptype-add.component.html',
    styleUrls: ['./membershiptype-add.component.css'],
    standalone: false
})
export class MembershiptypeAddComponent implements OnInit {
  membershipTypeAddForm: FormGroup;
  isSubmitting = false;

  @Output() membershipTypeAdded = new EventEmitter<void>();
  @ViewChild('membershipTypeList') membershipTypeList: MembershiptypeComponent;

  constructor(
    private formBuilder: FormBuilder,
    private membershipTypeService: MembershipTypeService,
    private toastrService: ToastrService,
    @Optional() private dialogRef: MatDialogRef<MembershiptypeAddComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    this.createMembershipTypeAddForm();
  }

  createMembershipTypeAddForm() {
    this.membershipTypeAddForm = this.formBuilder.group({
      branch: ['', Validators.required],
      typeName: ['', Validators.required],
      year: ['0', Validators.required],
      month: ['0', Validators.required],
      day: ['0', Validators.required],
      price: ['', Validators.required],
    });
  }

  add() {
    if (this.membershipTypeAddForm.valid) {
      this.isSubmitting = true; // Form gönderimi başladığında
      let membershipTypeModel = Object.assign(
        {},
        this.membershipTypeAddForm.value
      );

      const totalDays =
        parseInt(membershipTypeModel.year) * 365 +
        parseInt(membershipTypeModel.month) * 30 +
        parseInt(membershipTypeModel.day);

      membershipTypeModel.day = totalDays.toString();

      delete membershipTypeModel.year;
      delete membershipTypeModel.month;

      this.membershipTypeService.add(membershipTypeModel).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.toastrService.success(response.message, 'Başarılı');

          // Dialog olarak kullanılıyorsa dialog'u kapat
          if (this.dialogRef) {
            this.dialogRef.close(true);
          } else {
            // Normal component olarak kullanılıyorsa form'u resetle ve listeyi güncelle
            this.resetForm();
            this.membershipTypeAdded.emit();
            // Üyelik türleri listesini güncelle
            if (this.membershipTypeList) {
              this.membershipTypeList.getMembershipTypes();
            }
          }
        },
        error: (responseError) => {
          this.isSubmitting = false;
          if (
            responseError.error.Errors &&
            responseError.error.Errors.length > 0
          ) {
            for (let i = 0; i < responseError.error.Errors.length; i++) {
              this.toastrService.error(
                responseError.error.Errors[i].ErrorMessage,
                'Hata'
              );
            }
          } else {
            this.toastrService.error('Bir hata oluştu', 'Hata');
          }
        }
      });
    } else {
      this.toastrService.error('Formu eksiksiz doldurunuz', 'Hata');
    }
  }

  resetForm() {
    this.membershipTypeAddForm.reset({
      branch: '',
      typeName: '',
      year: '0',
      month: '0',
      day: '0',
      price: ''
    });
    // Form kontrollerinin touched ve dirty durumlarını temizle
    Object.keys(this.membershipTypeAddForm.controls).forEach(key => {
      const control = this.membershipTypeAddForm.get(key);
      if (control) {
        control.markAsUntouched();
        control.markAsPristine();
      }
    });
  }
}