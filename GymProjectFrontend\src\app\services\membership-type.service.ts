import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MembershipType } from '../models/membershipType';
import { ListResponseModel } from '../models/listResponseModel';
import { Observable } from 'rxjs';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';
import { MembershipTypePagingParameters } from '../models/membershipTypePagingParameters';
import { PaginatedResult } from '../models/pagination';
import { SingleResponseModel } from '../models/singleResponseModel';

@Injectable({
  providedIn: 'root',
})
export class MembershipTypeService extends BaseApiService{
  constructor(private httpClient:HttpClient) { 
    super();
  }
  getMembershipTypes(): Observable<ListResponseModel<MembershipType>> {
    let newPath = this.apiUrl + 'membershipType/getall';

    return this.httpClient.get<ListResponseModel<MembershipType>>(newPath);
  }
  add(membershiptype: MembershipType): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'membershiptype/add',
      membershiptype
    );
  }
  delete(membershipTypeId: number): Observable<ResponseModel> {
    let deletePath = `${this.apiUrl}membershiptype/delete/?id=${membershipTypeId}`;
    return this.httpClient.delete<ResponseModel>(deletePath);
  }
  getBranches(): Observable<ListResponseModel<MembershipType>> {
    let newPath = this.apiUrl + 'MembershipType/getallbranches';
    return this.httpClient.get<ListResponseModel<MembershipType>>(newPath);
  }
  update(membershipType: MembershipType): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'membershiptype/update',
      membershipType
    );
  }

  getPackagesByBranch(branch: string): Observable<ListResponseModel<any>> {
    return this.httpClient.get<ListResponseModel<any>>(
      `${this.apiUrl}membershiptype/getpackagesbybranch?branch=${encodeURIComponent(branch)}`
    );
  }

  getMembershipTypesPaginated(parameters: MembershipTypePagingParameters): Observable<SingleResponseModel<PaginatedResult<MembershipType>>> {
    let params = new HttpParams()
      .set('pageNumber', parameters.pageNumber.toString())
      .set('pageSize', parameters.pageSize.toString());

    if (parameters.searchText) {
      params = params.set('searchText', parameters.searchText);
    }
    if (parameters.branch) {
      params = params.set('branch', parameters.branch);
    }
    if (parameters.minPrice !== undefined && parameters.minPrice !== null) {
      params = params.set('minPrice', parameters.minPrice.toString());
    }
    if (parameters.maxPrice !== undefined && parameters.maxPrice !== null) {
      params = params.set('maxPrice', parameters.maxPrice.toString());
    }
    if (parameters.minDuration !== undefined && parameters.minDuration !== null) {
      params = params.set('minDuration', parameters.minDuration.toString());
    }
    if (parameters.maxDuration !== undefined && parameters.maxDuration !== null) {
      params = params.set('maxDuration', parameters.maxDuration.toString());
    }

    return this.httpClient.get<SingleResponseModel<PaginatedResult<MembershipType>>>(
      `${this.apiUrl}membershiptype/getallpaginated`,
      { params }
    );
  }
}
