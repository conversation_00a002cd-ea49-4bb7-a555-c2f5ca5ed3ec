import 'package:flutter/material.dart';

/// App Colors - GymKod Pro Mobile
///
/// Bu renk paleti Angular frontend'deki tasarım sisteminden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/styles.css ve modern-components.css
///
/// Angular'daki CSS custom properties'leri Flutter Color'larına çevrilmiştir.

class AppColors {
  // Primary Colors (Angular: --primary-color: #4361ee)
  static const Color primary = Color(0xFF4361EE);
  static const Color primaryLight = Color(0x1A4361EE); // rgba(67, 97, 238, 0.1)
  static const Color primaryDark = Color(0xFF3A0CA3);
  static const Color onPrimary = white;

  // Secondary Colors (Angular: --secondary-color: #3f37c9)
  static const Color secondary = Color(0xFF3F37C9);
  static const Color secondaryLight = Color(0x1A3F37C9); // rgba(63, 55, 201, 0.1)
  static const Color secondaryDark = Color(0xFF3A56D4);

  // Accent Colors (Angular: --accent-color: #4895ef)
  static const Color accent = Color(0xFF4895EF);
  static const Color accentLight = Color(0x1A4895EF); // rgba(72, 149, 239, 0.1)

  // Success Colors (Angular: --success: #28a745)
  static const Color success = Color(0xFF28A745);
  static const Color successLight = Color(0x1A28A745); // rgba(40, 167, 69, 0.1)

  // Info Colors (Angular: --info: #4cc9f0)
  static const Color info = Color(0xFF4CC9F0);
  static const Color infoLight = Color(0x1A4CC9F0); // rgba(76, 201, 240, 0.1)

  // Warning Colors (Angular: --warning: #ffc107)
  static const Color warning = Color(0xFFFFC107);
  static const Color warningLight = Color(0x1AFFC107); // rgba(255, 193, 7, 0.1)

  // Danger Colors (Angular: --danger: #dc3545)
  static const Color danger = Color(0xFFDC3545);
  static const Color dangerLight = Color(0x1ADC3545); // rgba(220, 53, 69, 0.1)

  // Error Colors (alias for danger)
  static const Color error = danger;
  static const Color onError = white;

  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color light = Color(0xFFF8F9FA);
  static const Color dark = Color(0xFF343A40);

  // Gray Colors (Profile image için)
  static const Color lightGray = Color(0xFFF1F3F5);
  static const Color darkGray = Color(0xFF6C757D);

  // Light Theme Colors (Angular: :root)
  static const Color lightBackground = Color(0xFFF8F9FA); // --background-color
  static const Color lightCardBackground = Color(0xFFFFFFFF); // --card-bg-color
  static const Color lightTextPrimary = Color(0xFF212529); // --text-color
  static const Color lightTextSecondary = Color(0xFF6C757D); // --text-muted
  static const Color lightBorder = Color(0xFFDEE2E6); // --border-color
  static const Color lightInputBackground = Color(0xFFFFFFFF); // --input-bg
  static const Color lightInputBorder = Color(0xFFCED4DA); // --input-border
  static const Color lightInputText = Color(0xFF212529); // --input-text
  static const Color lightShadow = Color(0x1A000000); // rgba(0, 0, 0, 0.1)

  // Dark Theme Colors (Angular: [data-theme="dark"])
  static const Color darkBackground = Color(0xFF121212); // --background-color
  static const Color darkCardBackground = Color(0xFF1E1E1E); // --card-bg-color
  static const Color darkTextPrimary = Color(0xFFE9ECEF); // --text-color
  static const Color darkTextSecondary = Color(0xFFADB5BD); // --text-muted
  static const Color darkBorder = Color(0xFF343A40); // --border-color
  static const Color darkInputBackground = Color(0xFF2D2D2D); // --input-bg
  static const Color darkInputBorder = Color(0xFF495057); // --input-border
  static const Color darkInputText = Color(0xFFE9ECEF); // --input-text
  static const Color darkShadow = Color(0x4D000000); // rgba(0, 0, 0, 0.3)

  // Sidebar Colors (Light Theme)
  static const Color lightSidebarBackground = Color(0xFFFFFFFF); // --sidebar-bg
  static const Color lightSidebarText = Color(0xFF212529); // --sidebar-text
  static const Color lightSidebarHover = Color(0xFFF1F3F5); // --sidebar-hover
  static const Color lightSidebarActive = Color(0xFFE9ECEF); // --sidebar-active

  // Sidebar Colors (Dark Theme)
  static const Color darkSidebarBackground = Color(0xFF1E1E1E); // --sidebar-bg
  static const Color darkSidebarText = Color(0xFFE9ECEF); // --sidebar-text
  static const Color darkSidebarHover = Color(0xFF2D2D2D); // --sidebar-hover
  static const Color darkSidebarActive = Color(0xFF343A40); // --sidebar-active

  // Button Colors
  static const Color buttonPrimaryBackground = Color(0xFF4361EE); // --btn-primary-bg
  static const Color buttonPrimaryText = Color(0xFFFFFFFF); // --btn-primary-text

  // Spinner Colors
  static const Color lightSpinner = Color(0xFF4361EE); // --spinner-color
  static const Color darkSpinner = Color(0xFF4895EF); // --spinner-color (dark mode)

  // Gradient Colors (Angular login/register component'lerinden)
  static const List<Color> primaryGradient = [
    Color(0xFF4361EE), // primary
    Color(0xFF3A0CA3), // primary-dark
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF4895EF), // secondary
    Color(0xFF277DA1), // secondary-dark
  ];

  static const List<Color> warningGradient = [
    Color(0xFFFFC107), // warning
    Color(0xFFFF8F00), // warning-dark
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFFF5F7FA),
    Color(0xFFE4E8F0),
  ];

  static const List<Color> darkBackgroundGradient = [
    Color(0xFF121212),
    Color(0xFF1A1A1A),
  ];

  // Overlay Colors (Angular login/register overlay'lerinden)
  static const Color overlayLight = Color(0xD94361EE); // rgba(67, 97, 238, 0.85)
  static const Color overlayDark = Color(0xD93A0CA3); // rgba(58, 12, 163, 0.85)

  // Transparent Colors
  static const Color transparent = Color(0x00000000);

  // Disabled Colors
  static const Color disabled = Color(0xFFA0A0A0);
  static const Color disabledText = Color(0xFF9E9E9E);

  // Focus Colors
  static const Color focusColor = Color(0x1A4361EE); // primary with opacity

  // Divider Colors
  static const Color lightDivider = Color(0xFFE0E0E0);
  static const Color darkDivider = Color(0xFF424242);

  // Surface Colors
  static const Color onSurface = lightTextPrimary;
  static const Color onSurfaceVariant = lightTextSecondary;
}
