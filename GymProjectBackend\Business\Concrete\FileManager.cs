using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Microsoft.AspNetCore.Http;
using System;
using System.IO;
using System.Linq;

namespace Business.Concrete
{
    public class FileManager : IFileService
    {
        private readonly string _imagesPath;
        private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png" };
        private readonly long _maxFileSize = 5 * 1024 * 1024; // 5MB

        public FileManager()
        {
            // C:\GymProject\Images klasörünü kullan
            _imagesPath = @"C:\GymProject\Images";

            // Images klasörü yoksa oluştur
            if (!Directory.Exists(_imagesPath))
            {
                Directory.CreateDirectory(_imagesPath);
            }
        }

        public IResult ValidateImageFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return new ErrorResult("Dosya seçilmedi.");
            }

            if (file.Length > _maxFileSize)
            {
                return new ErrorResult("Dosya boyutu 5MB'dan büyük olamaz.");
            }

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!_allowedExtensions.Contains(extension))
            {
                return new ErrorResult("Sadece JPG, JPEG ve PNG dosyaları yüklenebilir.");
            }

            return new SuccessResult();
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<string> UploadProfileImage(IFormFile file, int userId)
        {
            var validationResult = ValidateImageFile(file);
            if (!validationResult.Success)
            {
                return new ErrorDataResult<string>(validationResult.Message);
            }

            try
            {
                // Mevcut profil fotoğrafını sil
                DeleteExistingProfileImage(userId);

                // Yeni dosya adı oluştur
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                var fileName = $"{userId}{extension}";
                var filePath = Path.Combine(_imagesPath, fileName);

                // Dosyayı kaydet
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                // Relative path döndür
                var relativePath = $"images/{fileName}";
                return new SuccessDataResult<string>(relativePath, "Profil fotoğrafı başarıyla yüklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string>($"Dosya yüklenirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        public IResult DeleteProfileImage(int userId)
        {
            try
            {
                DeleteExistingProfileImage(userId);
                return new SuccessResult("Profil fotoğrafı başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil fotoğrafı silinirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner,admin,member")]
        public IDataResult<string> GetProfileImagePath(int userId)
        {
            try
            {
                foreach (var extension in _allowedExtensions)
                {
                    var fileName = $"{userId}{extension}";
                    var filePath = Path.Combine(_imagesPath, fileName);

                    if (File.Exists(filePath))
                    {
                        var relativePath = $"Images/{fileName}";
                        return new SuccessDataResult<string>(relativePath);
                    }
                }

                return new ErrorDataResult<string>("Profil fotoğrafı bulunamadı.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string>($"Profil fotoğrafı kontrol edilirken hata oluştu: {ex.Message}");
            }
        }

        private void DeleteExistingProfileImage(int userId)
        {
            foreach (var extension in _allowedExtensions)
            {
                var fileName = $"{userId}{extension}";
                var filePath = Path.Combine(_imagesPath, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }
    }
}
