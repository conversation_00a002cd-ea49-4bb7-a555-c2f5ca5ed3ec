using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UnifiedCompanyController : ControllerBase
    {
        private readonly IUnifiedCompanyService _unifiedCompanyService;

        public UnifiedCompanyController(IUnifiedCompanyService unifiedCompanyService)
        {
            _unifiedCompanyService = unifiedCompanyService;
        }

        [HttpPost("add")]
        public IActionResult Add(UnifiedCompanyAddDto unifiedCompanyDto)
        {
            var result = _unifiedCompanyService.AddUnifiedCompany(unifiedCompanyDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
