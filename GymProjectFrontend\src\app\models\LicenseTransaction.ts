export interface LicenseTransaction {
    licenseTransactionID: number;
    userID: number;
    licensePackageID: number;
    userLicenseID?: number;
    amount: number;
    paymentMethod: string;
    transactionDate: Date;
    isActive: boolean;
    creationDate: Date;
    updatedDate?: Date;
    deletedDate?: Date;
    // Dashboard için eklenen opsiyonel alanlar
    userName?: string;
    companyName?: string;
  }
  