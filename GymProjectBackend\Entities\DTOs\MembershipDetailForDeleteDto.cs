using Core.Entities;
using System;

namespace Entities.DTOs
{
    public class MembershipDetailForDeleteDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string PackageName { get; set; }
        public int RemainingDays { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
        public bool IsFrozen { get; set; }
    }
}
