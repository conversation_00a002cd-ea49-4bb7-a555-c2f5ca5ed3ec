import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { faPlus, faMinus, faEdit, faSave, faArrowLeft, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';

import { WorkoutProgramService } from '../../services/workout-program.service';
import { WorkoutProgramDayModalComponent } from './workout-program-day-modal.component';
import { 
  WorkoutProgramTemplate,
  WorkoutProgramTemplateUpdate, 
  WorkoutProgramDayUpdate, 
  POPULAR_DAY_NAMES, 
  EXPERIENCE_LEVELS, 
  TARGET_GOALS 
} from '../../models/workout-program.models';

@Component({
  selector: 'app-workout-program-edit',
  templateUrl: './workout-program-edit.component.html',
  styleUrls: ['./workout-program-edit.component.css'],
  standalone: false
})
export class WorkoutProgramEditComponent implements OnInit {
  // Icons
  faPlus = faPlus;
  faMinus = faMinus;
  faEdit = faEdit;
  faSave = faSave;
  faArrowLeft = faArrowLeft;
  faCalendarAlt = faCalendarAlt;

  // Form
  programForm!: FormGroup;
  isSubmitting = false;
  isLoading = true;

  // Data
  programId!: number;
  originalProgram?: WorkoutProgramTemplate;

  // Constants
  popularDayNames = POPULAR_DAY_NAMES;
  experienceLevels = EXPERIENCE_LEVELS;
  targetGoals = TARGET_GOALS;

  constructor(
    private fb: FormBuilder,
    private workoutProgramService: WorkoutProgramService,
    private router: Router,
    private route: ActivatedRoute,
    private toastrService: ToastrService,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.programId = Number(this.route.snapshot.paramMap.get('id'));
    if (!this.programId) {
      this.toastrService.error('Geçersiz program ID', 'Hata');
      this.router.navigate(['/workout-programs']);
      return;
    }

    this.initializeForm();
    this.loadProgram();
  }

  initializeForm(): void {
    this.programForm = this.fb.group({
      programName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]],
      experienceLevel: ['', [Validators.required]],
      targetGoal: ['', [Validators.required]],
      isActive: [true],
      days: this.fb.array([])
    });
  }

  get days(): FormArray {
    return this.programForm.get('days') as FormArray;
  }

  loadProgram(): void {
    this.isLoading = true;

    this.workoutProgramService.getById(this.programId).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.originalProgram = response.data;
          this.populateForm();
        } else {
          this.toastrService.error('Program bulunamadı', 'Hata');
          this.router.navigate(['/workout-programs']);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading program:', error);
        this.toastrService.error('Program yüklenirken hata oluştu', 'Hata');
        this.router.navigate(['/workout-programs']);
        this.isLoading = false;
      }
    });
  }

  populateForm(): void {
    if (!this.originalProgram) return;

    // Program temel bilgilerini doldur
    this.programForm.patchValue({
      programName: this.originalProgram.programName,
      description: this.originalProgram.description,
      experienceLevel: this.originalProgram.experienceLevel,
      targetGoal: this.originalProgram.targetGoal,
      isActive: this.originalProgram.isActive ?? true
    });

    // Günleri doldur
    this.days.clear();
    if (this.originalProgram.days && this.originalProgram.days.length > 0) {
      this.originalProgram.days.forEach(day => {
        const dayGroup = this.createDayFormGroup(day.dayNumber);
        dayGroup.patchValue({
          workoutProgramDayID: day.workoutProgramDayID,
          dayNumber: day.dayNumber,
          dayName: day.dayName,
          isRestDay: day.isRestDay
        });

        // Egzersizleri doldur
        if (day.exercises && day.exercises.length > 0) {
          const exercisesArray = dayGroup.get('exercises') as FormArray;
          day.exercises.forEach(exercise => {
            const exerciseGroup = this.fb.group({
              workoutProgramExerciseID: [exercise.workoutProgramExerciseID],
              exerciseType: [exercise.exerciseType],
              exerciseID: [exercise.exerciseID],
              exerciseName: [exercise.exerciseName],
              orderIndex: [exercise.orderIndex],
              sets: [exercise.sets],
              reps: [exercise.reps],
              notes: [exercise.notes]
            });
            exercisesArray.push(exerciseGroup);
          });
        }

        this.days.push(dayGroup);
      });
    }
  }

  createDayFormGroup(dayNumber: number = 1): FormGroup {
    return this.fb.group({
      workoutProgramDayID: [null], // Null ise yeni gün
      dayNumber: [dayNumber, [Validators.required, Validators.min(1), Validators.max(7)]],
      dayName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      isRestDay: [false],
      exercises: this.fb.array([])
    });
  }

  addDay(): void {
    const dayNumber = this.days.length + 1;
    if (dayNumber <= 7) {
      const dayGroup = this.createDayFormGroup(dayNumber);
      this.days.push(dayGroup);
    } else {
      this.toastrService.warning('Maksimum 7 gün ekleyebilirsiniz', 'Uyarı');
    }
  }

  removeDay(index: number): void {
    if (this.days.length > 1) {
      this.days.removeAt(index);
      this.updateDayNumbers();
    } else {
      this.toastrService.warning('En az bir gün olmalıdır', 'Uyarı');
    }
  }

  updateDayNumbers(): void {
    this.days.controls.forEach((dayControl, index) => {
      dayControl.get('dayNumber')?.setValue(index + 1);
    });
  }

  onPopularDayNameSelect(dayIndex: number, dayName: string): void {
    const dayControl = this.days.at(dayIndex);
    dayControl.get('dayName')?.setValue(dayName);

    // Eğer "Dinlenme Günü" seçildiyse, dinlenme günü switch'ini aktif et
    if (dayName === 'Dinlenme Günü') {
      dayControl.get('isRestDay')?.setValue(true);
      // Egzersizleri temizle
      const exercisesArray = dayControl.get('exercises') as FormArray;
      exercisesArray.clear();
    }
  }

  onRestDayChange(dayIndex: number): void {
    const dayControl = this.days.at(dayIndex);
    const isRestDay = dayControl.get('isRestDay')?.value;

    if (isRestDay) {
      // Tüm günlerin dinlenme günü olup olmadığını kontrol et
      const allRestDays = this.days.controls.every(control =>
        control.get('isRestDay')?.value === true
      );

      if (allRestDays) {
        // Eğer tüm günler dinlenme günü olacaksa, bu değişikliği geri al
        dayControl.get('isRestDay')?.setValue(false);
        this.toastrService.warning('En az bir gün egzersiz günü olmalıdır. Tüm günler dinlenme günü olamaz.', 'Uyarı');
        return;
      }

      const exercisesArray = dayControl.get('exercises') as FormArray;
      exercisesArray.clear();

      // Gün adını "Dinlenme Günü" olarak ayarla
      dayControl.get('dayName')?.setValue('Dinlenme Günü');
    } else {
      // Dinlenme günü kapatıldıysa ve gün adı "Dinlenme Günü" ise temizle
      const currentDayName = dayControl.get('dayName')?.value;
      if (currentDayName === 'Dinlenme Günü') {
        dayControl.get('dayName')?.setValue('');
      }
    }
  }

  openDayEditModal(dayIndex: number): void {
    const dayControl = this.days.at(dayIndex);
    const dayData = dayControl.value;

    const dialogRef = this.dialog.open(WorkoutProgramDayModalComponent, {
      width: '1000px',
      maxWidth: '95vw',
      height: '80vh',
      maxHeight: '80vh',
      data: {
        day: dayData,
        dayNumber: dayData.dayNumber,
        mode: 'edit'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Sadece egzersizleri güncelle
        const exercisesArray = dayControl.get('exercises') as FormArray;
        exercisesArray.clear();

        if (result.exercises && result.exercises.length > 0) {
          result.exercises.forEach((exercise: any) => {
            const exerciseGroup = this.fb.group({
              workoutProgramExerciseID: [exercise.workoutProgramExerciseID || null],
              exerciseType: [exercise.exerciseType],
              exerciseID: [exercise.exerciseID],
              exerciseName: [exercise.exerciseName],
              orderIndex: [exercise.orderIndex],
              sets: [exercise.sets],
              reps: [exercise.reps],
              notes: [exercise.notes]
            });
            exercisesArray.push(exerciseGroup);
          });
        }
      }
    });
  }

  onSubmit(): void {
    if (this.programForm.valid && this.validateAtLeastOneWorkoutDay()) {
      this.isSubmitting = true;

      const formValue = this.programForm.value;
      const programData: WorkoutProgramTemplateUpdate = {
        workoutProgramTemplateID: this.programId,
        programName: formValue.programName,
        description: formValue.description,
        experienceLevel: formValue.experienceLevel,
        targetGoal: formValue.targetGoal,
        isActive: formValue.isActive,
        days: formValue.days.map((day: any) => ({
          workoutProgramDayID: day.workoutProgramDayID,
          dayNumber: day.dayNumber,
          dayName: day.dayName,
          isRestDay: day.isRestDay,
          exercises: day.exercises?.map((exercise: any) => ({
            workoutProgramExerciseID: exercise.workoutProgramExerciseID,
            exerciseType: exercise.exerciseType,
            exerciseID: exercise.exerciseID,
            orderIndex: exercise.orderIndex,
            sets: exercise.sets,
            reps: exercise.reps,
            notes: exercise.notes
          })) || []
        }))
      };

      this.workoutProgramService.update(programData).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Antrenman programı başarıyla güncellendi', 'Başarılı');
            this.router.navigate(['/workout-programs']);
          } else {
            this.toastrService.error(response.message || 'Program güncellenirken hata oluştu', 'Hata');
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error updating workout program:', error);
          this.toastrService.error('Program güncellenirken hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    } else {
      this.markFormGroupTouched(this.programForm);
      this.toastrService.warning('Lütfen tüm gerekli alanları doldurun', 'Uyarı');
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          }
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/workout-programs']);
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.programForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  validateAtLeastOneWorkoutDay(): boolean {
    const hasAtLeastOneWorkoutDay = this.days.controls.some(dayControl =>
      !dayControl.get('isRestDay')?.value
    );

    if (!hasAtLeastOneWorkoutDay) {
      this.toastrService.error('En az bir gün egzersiz günü olmalıdır. Tüm günler dinlenme günü olamaz.', 'Hata');
      return false;
    }

    return true;
  }

  isDayFieldInvalid(dayIndex: number, fieldName: string): boolean {
    const dayControl = this.days.at(dayIndex);
    const field = dayControl.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.programForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
    }
    return '';
  }

  getDayFieldError(dayIndex: number, fieldName: string): string {
    const dayControl = this.days.at(dayIndex);
    const field = dayControl.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['min']) return `En az ${field.errors['min'].min} olmalıdır`;
      if (field.errors['max']) return `En fazla ${field.errors['max'].max} olmalıdır`;
    }
    return '';
  }
}
