﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserDeviceManager : IUserDeviceService
    {
        private readonly IUserDeviceDal _userDeviceDal;
        private const int MAX_ACTIVE_DEVICES = 5; // Web için
        private const int MAX_MOBILE_DEVICES = 1; // Mobil için sadece 1 cihaz
        public UserDeviceManager(IUserDeviceDal userDeviceDal)
        {
            _userDeviceDal = userDeviceDal;
        }

        public IResult Add(UserDevice device)
        {
            CleanExpiredTokens();

            var activeDevices = GetActiveDevicesByUserId(device.UserId);

            // Mobil cihaz kontrolü (DeviceInfo'da "Mobile" geçiyorsa mobil)
            bool isMobileDevice = device.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                                device.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                                device.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                                device.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true;

            if (isMobileDevice)
            {
                // Mobil için: Tüm eski mobil cihazları revoke et (sadece 1 mobil cihaz)
                var existingMobileDevices = activeDevices.Data.Where(d =>
                    d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                    d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                    d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                    d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true).ToList();

                foreach (var existingDevice in existingMobileDevices)
                {
                    RevokeDevice(existingDevice.Id);
                }
            }
            else
            {
                // Web için: Maksimum cihaz sayısı kontrolü
                var webDevices = activeDevices.Data.Where(d =>
                    !(d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                      d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                      d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                      d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true)).ToList();

                if (webDevices.Count >= MAX_ACTIVE_DEVICES)
                {
                    var oldestDevice = webDevices
                        .OrderBy(d => d.CreatedAt)
                        .First();
                    RevokeDevice(oldestDevice.Id);
                }
            }

            _userDeviceDal.Add(device);
            return new SuccessResult();
        }

        private void CleanExpiredTokens()
        {
            var expiredDevices = _userDeviceDal.GetAll(d =>
                d.IsActive &&
                d.RefreshTokenExpiration.HasValue &&
                d.RefreshTokenExpiration.Value < DateTime.Now);

            foreach (var device in expiredDevices)
            {
                device.IsActive = false;
                device.RefreshToken = null;
                device.RefreshTokenExpiration = null;
                _userDeviceDal.Update(device);
            }
        }
        public IResult Delete(int id)
        {
            var device = _userDeviceDal.Get(d => d.Id == id);
            if (device == null)
                return new ErrorResult(Messages.DeviceNotFound);

            _userDeviceDal.Delete(id);
            return new SuccessResult();
        }

        public IDataResult<List<UserDevice>> GetActiveDevicesByUserId(int userId)
        {
            var devices = _userDeviceDal.GetActiveDevicesByUserId(userId);
            return new SuccessDataResult<List<UserDevice>>(devices);
        }

        public IDataResult<UserDevice> GetByRefreshToken(string refreshToken)
        {
            var device = _userDeviceDal.GetByRefreshToken(refreshToken);
            if (device == null)
                return new ErrorDataResult<UserDevice>(Messages.InvalidRefreshToken);

            // Sadece kontrol edilen token'ın süresini kontrol et
            if (device.RefreshTokenExpiration.HasValue && device.RefreshTokenExpiration.Value < DateTime.Now)
            {
                device.IsActive = false;
                device.RefreshToken = null;
                device.RefreshTokenExpiration = null;
                _userDeviceDal.Update(device);
                return new ErrorDataResult<UserDevice>(Messages.ExpiredRefreshToken);
            }

            return new SuccessDataResult<UserDevice>(device);
        }
        public IResult RevokeDevice(int deviceId)
        {
            var device = _userDeviceDal.Get(d => d.Id == deviceId);
            if (device == null)
                return new ErrorResult(Messages.DeviceNotFound);

            device.IsActive = false;
            device.RefreshToken = null;
            device.RefreshTokenExpiration = null;
            _userDeviceDal.Update(device);

            return new SuccessResult();
        }

        //[SecuredOperation("owner,admin")]
        public IResult RevokeAllDevicesExceptCurrent(int userId, string currentRefreshToken)
        {
            var devices = _userDeviceDal.GetAll(d => d.UserId == userId && d.IsActive && d.RefreshToken != currentRefreshToken);
            foreach (var device in devices)
            {
                device.IsActive = false;
                device.RefreshToken = null;
                device.RefreshTokenExpiration = null;
                _userDeviceDal.Update(device);
            }

            return new SuccessResult();
        }

        //[SecuredOperation("owner,admin")]
        public IResult Update(UserDevice device)
        {
            _userDeviceDal.Update(device);
            return new SuccessResult();
        }
    }
}
