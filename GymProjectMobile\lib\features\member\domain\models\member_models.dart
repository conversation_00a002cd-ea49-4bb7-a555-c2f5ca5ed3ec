/// Member Models - GymKod Pro Mobile
///
/// Bu dosya Angular frontend'deki member model'lerinden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/app/models/member/
library;

import 'package:json_annotation/json_annotation.dart';

part 'member_models.g.dart';

/// Membership Info Model
/// Backend'deki MembershipInfo'dan uyarlanmıştır
@JsonSerializable()
class MembershipInfo {
  @JsonKey(name: 'branch')
  final String branch;

  @JsonKey(name: 'startDate')
  final DateTime startDate;

  @JsonKey(name: 'endDate')
  final DateTime endDate;

  @JsonKey(name: 'remainingDays')
  final int remainingDays;

  const MembershipInfo({
    required this.branch,
    required this.startDate,
    required this.endDate,
    required this.remainingDays,
  });

  factory MembershipInfo.fromJson(Map<String, dynamic> json) =>
      _$MembershipInfoFromJson(json);

  Map<String, dynamic> toJson() => _$MembershipInfoToJson(this);

  @override
  String toString() {
    return 'MembershipInfo(branch: $branch, startDate: $startDate, endDate: $endDate, remainingDays: $remainingDays)';
  }
}

/// Get Member QR By Phone Number DTO
/// Backend'deki GetMemberQRByPhoneNumberDto'dan uyarlanmıştır
@JsonSerializable()
class GetMemberQRByPhoneNumberDto {
  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'scanNumber')
  final String scanNumber;

  @JsonKey(name: 'remainingDays')
  final String remainingDays;

  @JsonKey(name: 'memberships')
  final List<MembershipInfo> memberships;

  @JsonKey(name: 'isFrozen')
  final bool isFrozen;

  @JsonKey(name: 'freezeEndDate')
  final DateTime? freezeEndDate;

  @JsonKey(name: 'phoneNumber')
  final String? phoneNumber;

  const GetMemberQRByPhoneNumberDto({
    required this.name,
    required this.scanNumber,
    required this.remainingDays,
    required this.memberships,
    required this.isFrozen,
    this.freezeEndDate,
    this.phoneNumber,
  });

  factory GetMemberQRByPhoneNumberDto.fromJson(Map<String, dynamic> json) =>
      _$GetMemberQRByPhoneNumberDtoFromJson(json);

  Map<String, dynamic> toJson() => _$GetMemberQRByPhoneNumberDtoToJson(this);

  /// Aktif üyelik var mı kontrol et
  bool get hasActiveMembership {
    if (isFrozen) return false;
    if (memberships.isEmpty) return false;
    if (isNotStartedYet) return false; // Henüz başlamamış üyelikler aktif değil
    if (isExpired) return false; // Süresi dolmuş üyelikler aktif değil

    final now = DateTime.now();
    return memberships.any((membership) {
      // Üyelik bugün veya daha önce başlamış ve henüz bitmemiş olmalı
      return membership.startDate.isBefore(now) &&
             membership.endDate.isAfter(now);
    });
  }

  /// Üyelik süresi dolmuş mu?
  bool get isExpired {
    if (memberships.isEmpty) return true;

    final now = DateTime.now();
    return memberships.every((membership) => membership.endDate.isBefore(now));
  }

  /// Üyelik henüz başlamamış mı?
  bool get isNotStartedYet {
    if (memberships.isEmpty) return false;

    final now = DateTime.now();
    return memberships.every((membership) => membership.startDate.isAfter(now));
  }

  /// Son gün mü? (bugün bitiyor)
  bool get isLastDay {
    if (isFrozen || memberships.isEmpty) return false;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return memberships.any((membership) {
      final endDate = DateTime(membership.endDate.year, membership.endDate.month, membership.endDate.day);
      final remainingDays = endDate.difference(today).inDays;

      // 0 veya 1 gün kaldıysa son gün sayılır
      return remainingDays <= 1 && remainingDays >= 0;
    });
  }

  /// Kalan gün sayısını hesapla (son gün durumu için özel)
  int get actualRemainingDays {
    if (isFrozen || memberships.isEmpty) return 0;

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Henüz başlamamış üyelik için başlama tarihine kadar olan günleri hesapla
    if (isNotStartedYet) {
      final earliestStart = memberships
          .map((m) => m.startDate)
          .reduce((a, b) => a.isBefore(b) ? a : b);
      final startDate = DateTime(earliestStart.year, earliestStart.month, earliestStart.day);
      return startDate.difference(today).inDays + 1; // +1 ekliyoruz çünkü bugün dahil değil
    }

    int maxDays = 0;

    for (final membership in memberships) {
      final endDate = DateTime(membership.endDate.year, membership.endDate.month, membership.endDate.day);
      if (endDate.isAfter(today) || endDate.isAtSameMomentAs(today)) {
        final remaining = endDate.difference(today).inDays;
        if (remaining > maxDays) {
          maxDays = remaining;
        }
      }
    }

    return maxDays;
  }

  /// Kalan gün mesajını formatla (son gün için özel)
  String get formattedRemainingDays {
    if (isFrozen) return 'Dondurulmuş';
    if (isExpired) return 'Süresi Dolmuş';
    if (isNotStartedYet) {
      final now = DateTime.now();
      final earliestStart = memberships
          .map((m) => m.startDate)
          .reduce((a, b) => a.isBefore(b) ? a : b);
      final daysUntilStart = earliestStart.difference(now).inDays;
      return '$daysUntilStart gün sonra başlayacak';
    }

    final remaining = actualRemainingDays;
    if (isLastDay) {
      return 'Son Gün';
    } else if (remaining == 1) {
      return '1 Gün';
    } else if (remaining == 0) {
      return 'Süresi Dolmuş';
    } else {
      return '$remaining Gün';
    }
  }

  @override
  String toString() {
    return 'GetMemberQRByPhoneNumberDto(name: $name, scanNumber: $scanNumber, remainingDays: $remainingDays, isFrozen: $isFrozen, phoneNumber: $phoneNumber)';
  }
}

/// Member QR Response Model
/// Backend'deki API response format'ına uygun
@JsonSerializable()
class MemberQRResponse {
  final bool success;
  final String message;
  final GetMemberQRByPhoneNumberDto? data;

  const MemberQRResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory MemberQRResponse.fromJson(Map<String, dynamic> json) =>
      _$MemberQRResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MemberQRResponseToJson(this);

  @override
  String toString() {
    return 'MemberQRResponse(success: $success, message: $message, data: $data)';
  }
}

/// QR Timer State Model
/// Angular frontend'deki timer state'inden uyarlanmıştır
class QRTimerState {
  final Duration remainingTime;
  final Duration totalTime;
  final bool isActive;
  final bool isExpired;
  final double progress; // 0.0 - 1.0

  const QRTimerState({
    required this.remainingTime,
    required this.totalTime,
    required this.isActive,
    required this.isExpired,
    required this.progress,
  });

  factory QRTimerState.initial() {
    const totalTime = Duration(minutes: 5); // 5 dakika
    return QRTimerState(
      remainingTime: totalTime,
      totalTime: totalTime,
      isActive: false,
      isExpired: false,
      progress: 1.0,
    );
  }

  QRTimerState copyWith({
    Duration? remainingTime,
    Duration? totalTime,
    bool? isActive,
    bool? isExpired,
    double? progress,
  }) {
    return QRTimerState(
      remainingTime: remainingTime ?? this.remainingTime,
      totalTime: totalTime ?? this.totalTime,
      isActive: isActive ?? this.isActive,
      isExpired: isExpired ?? this.isExpired,
      progress: progress ?? this.progress,
    );
  }

  /// Kalan süreyi formatla (mm:ss)
  String get formattedTime {
    final minutes = remainingTime.inMinutes;
    final seconds = remainingTime.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Progress yüzdesi
  int get progressPercentage {
    return (progress * 100).round();
  }

  @override
  String toString() {
    return 'QRTimerState(remainingTime: $remainingTime, totalTime: $totalTime, isActive: $isActive, isExpired: $isExpired, progress: $progress)';
  }
}

/// QR Code State Model
/// Angular frontend'deki QR component state'inden uyarlanmıştır
class QRCodeState {
  final GetMemberQRByPhoneNumberDto? qrData;
  final QRTimerState timerState;
  final bool isLoading;
  final String? error;

  const QRCodeState({
    this.qrData,
    required this.timerState,
    this.isLoading = false,
    this.error,
  });

  factory QRCodeState.initial() {
    return QRCodeState(
      timerState: QRTimerState.initial(),
    );
  }

  QRCodeState copyWith({
    GetMemberQRByPhoneNumberDto? qrData,
    QRTimerState? timerState,
    bool? isLoading,
    String? error,
    bool clearQrData = false,
    bool clearError = false,
  }) {
    return QRCodeState(
      qrData: clearQrData ? null : (qrData ?? this.qrData),
      timerState: timerState ?? this.timerState,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
    );
  }

  /// QR kod aktif mi?
  bool get isQRActive {
    return qrData != null &&
           qrData!.hasActiveMembership &&
           !timerState.isExpired;
  }

  /// QR kod gösterilebilir mi? (Sadece aktif üyeliği olanlar)
  bool get canShowQR {
    return qrData != null &&
           qrData!.hasActiveMembership &&
           qrData!.scanNumber.isNotEmpty &&
           !timerState.isExpired;
  }

  @override
  String toString() {
    return 'QRCodeState(qrData: $qrData, timerState: $timerState, isLoading: $isLoading, error: $error)';
  }
}

/// Member Profile DTO
/// Backend'deki MemberProfileDto'dan uyarlanmıştır
@JsonSerializable()
class MemberProfileDto {
  @JsonKey(name: 'firstName')
  final String firstName;

  @JsonKey(name: 'lastName')
  final String lastName;

  @JsonKey(name: 'email')
  final String email;

  @JsonKey(name: 'adress')
  final String? adress;

  @JsonKey(name: 'birthDate')
  final DateTime? birthDate;

  @JsonKey(name: 'phoneNumber')
  final String phoneNumber;

  @JsonKey(name: 'profileImagePath')
  final String? profileImagePath;

  const MemberProfileDto({
    required this.firstName,
    required this.lastName,
    required this.email,
    this.adress,
    this.birthDate,
    required this.phoneNumber,
    this.profileImagePath,
  });

  factory MemberProfileDto.fromJson(Map<String, dynamic> json) =>
      _$MemberProfileDtoFromJson(json);

  Map<String, dynamic> toJson() => _$MemberProfileDtoToJson(this);

  @override
  String toString() {
    return 'MemberProfileDto(firstName: $firstName, lastName: $lastName, email: $email, adress: $adress, birthDate: $birthDate, phoneNumber: $phoneNumber, profileImagePath: $profileImagePath)';
  }
}

/// Helper functions for date serialization
String? _dateTimeToDateString(DateTime? dateTime) {
  if (dateTime == null) return null;
  // Backend DateOnly formatı: YYYY-MM-DD
  return '${dateTime.year.toString().padLeft(4, '0')}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
}

DateTime? _dateStringToDateTime(String? dateString) {
  if (dateString == null || dateString.isEmpty) return null;
  try {
    return DateTime.parse(dateString);
  } catch (e) {
    return null;
  }
}

/// Member Profile Update DTO
/// Backend'deki MemberProfileUpdateDto'dan uyarlanmıştır
@JsonSerializable()
class MemberProfileUpdateDto {
  @JsonKey(name: 'firstName')
  final String firstName;

  @JsonKey(name: 'lastName')
  final String lastName;

  @JsonKey(name: 'adress')
  final String? adress;

  @JsonKey(name: 'birthDate', toJson: _dateTimeToDateString, fromJson: _dateStringToDateTime)
  final DateTime? birthDate;

  const MemberProfileUpdateDto({
    required this.firstName,
    required this.lastName,
    this.adress,
    this.birthDate,
  });

  factory MemberProfileUpdateDto.fromJson(Map<String, dynamic> json) =>
      _$MemberProfileUpdateDtoFromJson(json);

  Map<String, dynamic> toJson() => _$MemberProfileUpdateDtoToJson(this);

  @override
  String toString() {
    return 'MemberProfileUpdateDto(firstName: $firstName, lastName: $lastName, adress: $adress, birthDate: $birthDate)';
  }
}

/// Member Profile Response Model
/// Backend'deki API response format'ına uygun
@JsonSerializable()
class MemberProfileResponse {
  final bool success;
  final String message;
  final MemberProfileDto? data;

  const MemberProfileResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory MemberProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$MemberProfileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MemberProfileResponseToJson(this);

  @override
  String toString() {
    return 'MemberProfileResponse(success: $success, message: $message, data: $data)';
  }
}
