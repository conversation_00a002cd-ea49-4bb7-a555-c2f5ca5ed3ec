/// Storage Service - GymKod Pro Mobile
///
/// Bu service JWT token'ları ve user data'yı gü<PERSON>li şekilde saklar.
/// Referans: Angular frontend'deki localStorage kullanımı
library;

import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';

/// Secure Storage Service
/// Angular frontend'deki localStorage service'e benzer
class StorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Singleton pattern
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  /// Access Token'ı kaydet
  Future<void> saveAccessToken(String token) async {
    try {
      await _storage.write(key: AppConstants.accessTokenKey, value: token);
    } catch (e) {
      throw StorageException('Access token kaydedilemedi: $e');
    }
  }

  /// Access Token'ı al
  Future<String?> getAccessToken() async {
    try {
      return await _storage.read(key: AppConstants.accessTokenKey);
    } catch (e) {
      throw StorageException('Access token alınamadı: $e');
    }
  }

  /// Refresh Token'ı kaydet
  Future<void> saveRefreshToken(String refreshToken) async {
    try {
      await _storage.write(key: AppConstants.refreshTokenKey, value: refreshToken);
    } catch (e) {
      throw StorageException('Refresh token kaydedilemedi: $e');
    }
  }

  /// Refresh Token'ı al
  Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: AppConstants.refreshTokenKey);
    } catch (e) {
      throw StorageException('Refresh token alınamadı: $e');
    }
  }

  /// User Data'yı kaydet
  Future<void> saveUserData(UserModel user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      await _storage.write(key: AppConstants.userDataKey, value: userJson);
    } catch (e) {
      throw StorageException('User data kaydedilemedi: $e');
    }
  }

  /// User Data'yı al
  Future<UserModel?> getUserData() async {
    try {
      final userJson = await _storage.read(key: AppConstants.userDataKey);
      if (userJson == null) return null;

      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return UserModel.fromJson(userMap);
    } catch (e) {
      throw StorageException('User data alınamadı: $e');
    }
  }

  /// Theme Mode'u kaydet
  Future<void> saveThemeMode(String themeMode) async {
    try {
      await _storage.write(key: AppConstants.themeKey, value: themeMode);
    } catch (e) {
      throw StorageException('Theme mode kaydedilemedi: $e');
    }
  }

  /// Theme Mode'u al
  Future<String?> getThemeMode() async {
    try {
      return await _storage.read(key: AppConstants.themeKey);
    } catch (e) {
      throw StorageException('Theme mode alınamadı: $e');
    }
  }

  /// Language Code'u kaydet
  Future<void> saveLanguageCode(String languageCode) async {
    try {
      await _storage.write(key: AppConstants.languageKey, value: languageCode);
    } catch (e) {
      throw StorageException('Language code kaydedilemedi: $e');
    }
  }

  /// Language Code'u al
  Future<String?> getLanguageCode() async {
    try {
      return await _storage.read(key: AppConstants.languageKey);
    } catch (e) {
      throw StorageException('Language code alınamadı: $e');
    }
  }

  /// First Launch flag'ini kaydet
  Future<void> saveFirstLaunch(bool isFirstLaunch) async {
    try {
      await _storage.write(
        key: AppConstants.firstLaunchKey,
        value: isFirstLaunch.toString(),
      );
    } catch (e) {
      throw StorageException('First launch flag kaydedilemedi: $e');
    }
  }

  /// First Launch flag'ini al
  Future<bool> getFirstLaunch() async {
    try {
      final value = await _storage.read(key: AppConstants.firstLaunchKey);
      return value?.toLowerCase() == 'true';
    } catch (e) {
      throw StorageException('First launch flag alınamadı: $e');
    }
  }

  /// Biometric Enabled flag'ini kaydet
  Future<void> saveBiometricEnabled(bool isEnabled) async {
    try {
      await _storage.write(
        key: AppConstants.biometricEnabledKey,
        value: isEnabled.toString(),
      );
    } catch (e) {
      throw StorageException('Biometric enabled flag kaydedilemedi: $e');
    }
  }

  /// Biometric Enabled flag'ini al
  Future<bool> getBiometricEnabled() async {
    try {
      final value = await _storage.read(key: AppConstants.biometricEnabledKey);
      return value?.toLowerCase() == 'true';
    } catch (e) {
      throw StorageException('Biometric enabled flag alınamadı: $e');
    }
  }

  /// Device Info'yu kaydet
  Future<void> saveDeviceInfo(DeviceInfo deviceInfo) async {
    try {
      final deviceJson = jsonEncode(deviceInfo.toJson());
      await _storage.write(key: AppConstants.deviceTypeKey, value: deviceJson);
    } catch (e) {
      throw StorageException('Device info kaydedilemedi: $e');
    }
  }

  /// Device Info'yu al
  Future<DeviceInfo?> getDeviceInfo() async {
    try {
      final deviceJson = await _storage.read(key: AppConstants.deviceTypeKey);
      if (deviceJson == null) return null;

      final deviceMap = jsonDecode(deviceJson) as Map<String, dynamic>;
      return DeviceInfo.fromJson(deviceMap);
    } catch (e) {
      throw StorageException('Device info alınamadı: $e');
    }
  }

  /// Şifre değiştirme zorunluluğunu kaydet (Angular frontend pattern)
  Future<void> setRequirePasswordChange(bool requirePasswordChange) async {
    try {
      await _storage.write(
        key: 'requirePasswordChange',
        value: requirePasswordChange.toString(),
      );
    } catch (e) {
      throw StorageException('Require password change flag kaydedilemedi: $e');
    }
  }

  /// Şifre değiştirme zorunluluğunu al
  Future<bool> getRequirePasswordChange() async {
    try {
      final value = await _storage.read(key: 'requirePasswordChange');
      return value?.toLowerCase() == 'true';
    } catch (e) {
      throw StorageException('Require password change flag alınamadı: $e');
    }
  }

  /// Şifre değiştirme zorunluluğunu temizle (şifre değiştirildikten sonra)
  Future<void> clearRequirePasswordChange() async {
    try {
      await _storage.delete(key: 'requirePasswordChange');
    } catch (e) {
      throw StorageException('Require password change flag silinemedi: $e');
    }
  }

  /// Generic string değer kaydet
  Future<void> saveString(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      throw StorageException('String değer kaydedilemedi: $e');
    }
  }

  /// Generic string değer al
  Future<String?> getString(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      throw StorageException('String değer alınamadı: $e');
    }
  }

  /// Boolean değer kaydet
  Future<void> setBool(String key, bool value) async {
    try {
      await _storage.write(key: key, value: value.toString());
    } catch (e) {
      throw StorageException('Boolean değer kaydedilemedi: $e');
    }
  }

  /// Boolean değer al
  Future<bool?> getBool(String key) async {
    try {
      final value = await _storage.read(key: key);
      if (value == null) return null;
      return value.toLowerCase() == 'true';
    } catch (e) {
      throw StorageException('Boolean değer alınamadı: $e');
    }
  }

  /// Key'i sil (generic remove method)
  Future<void> remove(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw StorageException('Key silinemedi: $e');
    }
  }

  /// Belirli bir key'i sil
  Future<void> deleteKey(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw StorageException('Key silinemedi: $e');
    }
  }

  /// Auth token'ları sil (logout için)
  Future<void> clearAuthTokens() async {
    try {
      await Future.wait([
        _storage.delete(key: AppConstants.accessTokenKey),
        _storage.delete(key: AppConstants.refreshTokenKey),
        _storage.delete(key: AppConstants.userDataKey),
      ]);
    } catch (e) {
      throw StorageException('Auth token\'ları silinemedi: $e');
    }
  }

  /// Tüm storage'ı temizle
  Future<void> clearAll() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      throw StorageException('Storage temizlenemedi: $e');
    }
  }

  /// Storage'da belirli bir key'in var olup olmadığını kontrol et
  Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      throw StorageException('Key kontrolü yapılamadı: $e');
    }
  }

  /// Tüm key'leri al
  Future<Map<String, String>> getAllKeys() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      throw StorageException('Tüm key\'ler alınamadı: $e');
    }
  }

  /// Auth durumunu kontrol et (token'ların varlığı)
  Future<bool> isAuthenticated() async {
    try {
      final accessToken = await getAccessToken();
      final refreshToken = await getRefreshToken();
      return accessToken != null && refreshToken != null;
    } catch (e) {
      return false;
    }
  }

  /// Storage'ın çalışıp çalışmadığını test et
  Future<bool> testStorage() async {
    try {
      const testKey = 'test_key';
      const testValue = 'test_value';

      await _storage.write(key: testKey, value: testValue);
      final readValue = await _storage.read(key: testKey);
      await _storage.delete(key: testKey);

      return readValue == testValue;
    } catch (e) {
      return false;
    }
  }
}

/// Storage Exception
class StorageException implements Exception {
  final String message;

  const StorageException(this.message);

  @override
  String toString() => 'StorageException: $message';
}
