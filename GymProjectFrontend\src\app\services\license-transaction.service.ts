import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { ResponseModel } from '../models/responseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { BaseApiService } from './baseApiService';
import { LicenseTransaction } from '../models/LicenseTransaction';

@Injectable({
  providedIn: 'root'
})
export class LicenseTransactionService extends BaseApiService {
  constructor(private httpClient: HttpClient) {
    super();
  }

  getAll(filters?: any): Observable<ListResponseModel<LicenseTransaction>> {
    let url = this.apiUrl + 'LicenseTransactions/getall';

    if (filters) {
      const params = new URLSearchParams();

      if (filters.userID) {
        params.append('userID', filters.userID.toString());
      }
      if (filters.startDate) {
        params.append('startDate', filters.startDate);
      }
      if (filters.endDate) {
        params.append('endDate', filters.endDate);
      }
      if (filters.page) {
        params.append('page', filters.page.toString());
      }
      if (filters.pageSize) {
        params.append('pageSize', filters.pageSize.toString());
      }

      if (params.toString()) {
        url += '?' + params.toString();
      }
    }

    return this.httpClient.get<ListResponseModel<LicenseTransaction>>(url);
  }

  getByUserId(userId: number): Observable<ListResponseModel<LicenseTransaction>> {
    return this.httpClient.get<ListResponseModel<LicenseTransaction>>(this.apiUrl + 'LicenseTransactions/getbyuserid?userId=' + userId);
  }

  add(licenseTransaction: LicenseTransaction): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'LicenseTransactions/add', licenseTransaction);
  }

  delete(id: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(this.apiUrl + 'LicenseTransactions/delete?id=' + id);
  }

  getTotals(): Observable<SingleResponseModel<any>> {
    return this.httpClient.get<SingleResponseModel<any>>(this.apiUrl + 'LicenseTransactions/gettotals');
  }

  getMonthlyRevenue(year: number = 0): Observable<SingleResponseModel<any>> {
    let params = new HttpParams();
    if (year > 0) {
      params = params.set('year', year.toString());
    }
    return this.httpClient.get<SingleResponseModel<any>>(
      this.apiUrl + 'LicenseTransactions/getmonthlyrevenue',
      { params }
    );
  }
}
