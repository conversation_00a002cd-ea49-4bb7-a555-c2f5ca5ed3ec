using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Business;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class WorkoutProgramTemplateManager : IWorkoutProgramTemplateService
    {
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;
        private readonly IWorkoutProgramDayDal _workoutProgramDayDal;
        private readonly IWorkoutProgramExerciseDal _workoutProgramExerciseDal;
        private readonly ICompanyContext _companyContext;

        public WorkoutProgramTemplateManager(
            IWorkoutProgramTemplateDal workoutProgramTemplateDal,
            IWorkoutProgramDayDal workoutProgramDayDal,
            IWorkoutProgramExerciseDal workoutProgramExerciseDal,
            ICompanyContext companyContext)
        {
            _workoutProgramTemplateDal = workoutProgramTemplateDal;
            _workoutProgramDayDal = workoutProgramDayDal;
            _workoutProgramExerciseDal = workoutProgramExerciseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 30, "WorkoutProgramTemplate", "List")]
        [PerformanceAspect(3)]
        public IDataResult<List<WorkoutProgramTemplateListDto>> GetAll()
        {
            var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateList();
            return new SuccessDataResult<List<WorkoutProgramTemplateListDto>>(result, Messages.WorkoutProgramsListed);
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 30, "WorkoutProgramTemplate", "Detail")]
        [PerformanceAspect(3)]
        public IDataResult<WorkoutProgramTemplateDto> GetById(int templateId)
        {
            var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateDetail(templateId);
            if (result == null)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<WorkoutProgramTemplateDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateAddValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("WorkoutProgramTemplate")]
        [PerformanceAspect(3)]
        public IResult Add(WorkoutProgramTemplateAddDto templateAddDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgramNameExists(templateAddDto.ProgramName),
                CheckMaxDayCount(templateAddDto.Days.Count),
                CheckDayNumbers(templateAddDto.Days),
                CheckAtLeastOneWorkoutDay(templateAddDto.Days)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var companyId = _companyContext.GetCompanyId();

            // Ana program şablonunu oluştur
            var template = new WorkoutProgramTemplate
            {
                CompanyID = companyId,
                ProgramName = templateAddDto.ProgramName,
                Description = templateAddDto.Description,
                ExperienceLevel = templateAddDto.ExperienceLevel,
                TargetGoal = templateAddDto.TargetGoal,
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _workoutProgramTemplateDal.Add(template);

            // Günleri ekle
            foreach (var dayDto in templateAddDto.Days)
            {
                var day = new WorkoutProgramDay
                {
                    WorkoutProgramTemplateID = template.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    DayNumber = dayDto.DayNumber,
                    DayName = dayDto.DayName,
                    IsRestDay = dayDto.IsRestDay,
                    CreationDate = DateTime.Now
                };

                _workoutProgramDayDal.Add(day);

                // Egzersizleri ekle (dinlenme günü değilse)
                if (!dayDto.IsRestDay)
                {
                    foreach (var exerciseDto in dayDto.Exercises)
                    {
                        var exercise = new WorkoutProgramExercise
                        {
                            WorkoutProgramDayID = day.WorkoutProgramDayID,
                            CompanyID = companyId,
                            ExerciseType = exerciseDto.ExerciseType,
                            ExerciseID = exerciseDto.ExerciseID,
                            OrderIndex = exerciseDto.OrderIndex,
                            Sets = exerciseDto.Sets,
                            Reps = exerciseDto.Reps,
                            RestTime = exerciseDto.RestTime,
                            Notes = exerciseDto.Notes,
                            CreationDate = DateTime.Now
                        };

                        _workoutProgramExerciseDal.Add(exercise);
                    }
                }
            }

            return new SuccessResult(Messages.WorkoutProgramAdded);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateUpdateValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("WorkoutProgramTemplate")]
        [PerformanceAspect(3)]
        public IResult Update(WorkoutProgramTemplateUpdateDto templateUpdateDto)
        {
            // Mevcut programı kontrol et
            var existingTemplate = _workoutProgramTemplateDal.Get(t => t.WorkoutProgramTemplateID == templateUpdateDto.WorkoutProgramTemplateID);
            if (existingTemplate == null)
            {
                return new ErrorResult(Messages.WorkoutProgramNotFound);
            }

            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgramNameExistsForUpdate(templateUpdateDto.ProgramName, templateUpdateDto.WorkoutProgramTemplateID),
                CheckMaxDayCount(templateUpdateDto.Days.Count),
                CheckDayNumbers(templateUpdateDto.Days),
                CheckAtLeastOneWorkoutDay(templateUpdateDto.Days)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            // Ana programı güncelle
            existingTemplate.ProgramName = templateUpdateDto.ProgramName;
            existingTemplate.Description = templateUpdateDto.Description;
            existingTemplate.ExperienceLevel = templateUpdateDto.ExperienceLevel;
            existingTemplate.TargetGoal = templateUpdateDto.TargetGoal;
            existingTemplate.IsActive = templateUpdateDto.IsActive;
            existingTemplate.UpdatedDate = DateTime.Now;

            _workoutProgramTemplateDal.Update(existingTemplate);

            // Mevcut günleri ve egzersizleri sil
            var existingDays = _workoutProgramDayDal.GetAll(d => d.WorkoutProgramTemplateID == templateUpdateDto.WorkoutProgramTemplateID);
            foreach (var day in existingDays)
            {
                var exercises = _workoutProgramExerciseDal.GetAll(e => e.WorkoutProgramDayID == day.WorkoutProgramDayID);
                foreach (var exercise in exercises)
                {
                    _workoutProgramExerciseDal.HardDelete(exercise.WorkoutProgramExerciseID);
                }
                _workoutProgramDayDal.HardDelete(day.WorkoutProgramDayID);
            }

            // Yeni günleri ekle
            var companyId = _companyContext.GetCompanyId();
            foreach (var dayDto in templateUpdateDto.Days)
            {
                var day = new WorkoutProgramDay
                {
                    WorkoutProgramTemplateID = existingTemplate.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    DayNumber = dayDto.DayNumber,
                    DayName = dayDto.DayName,
                    IsRestDay = dayDto.IsRestDay,
                    CreationDate = DateTime.Now
                };

                _workoutProgramDayDal.Add(day);

                // Egzersizleri ekle (dinlenme günü değilse)
                if (!dayDto.IsRestDay)
                {
                    foreach (var exerciseDto in dayDto.Exercises)
                    {
                        var exercise = new WorkoutProgramExercise
                        {
                            WorkoutProgramDayID = day.WorkoutProgramDayID,
                            CompanyID = companyId,
                            ExerciseType = exerciseDto.ExerciseType,
                            ExerciseID = exerciseDto.ExerciseID,
                            OrderIndex = exerciseDto.OrderIndex,
                            Sets = exerciseDto.Sets,
                            Reps = exerciseDto.Reps,
                            RestTime = exerciseDto.RestTime,
                            Notes = exerciseDto.Notes,
                            CreationDate = DateTime.Now
                        };

                        _workoutProgramExerciseDal.Add(exercise);
                    }
                }
            }

            return new SuccessResult(Messages.WorkoutProgramUpdated);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("WorkoutProgramTemplate")]
        [PerformanceAspect(3)]
        public IResult Delete(int templateId)
        {
            var template = _workoutProgramTemplateDal.Get(t => t.WorkoutProgramTemplateID == templateId);
            if (template == null)
            {
                return new ErrorResult(Messages.WorkoutProgramNotFound);
            }

            _workoutProgramTemplateDal.Delete(templateId);
            return new SuccessResult(Messages.WorkoutProgramDeleted);
        }

        // İş Kuralları
        private IResult CheckIfProgramNameExists(string programName)
        {
            var result = _workoutProgramTemplateDal.Get(t => t.ProgramName == programName && t.IsActive == true);
            if (result != null)
            {
                return new ErrorResult(Messages.WorkoutProgramNameExists);
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgramNameExistsForUpdate(string programName, int templateId)
        {
            var result = _workoutProgramTemplateDal.Get(t => t.ProgramName == programName && t.IsActive == true && t.WorkoutProgramTemplateID != templateId);
            if (result != null)
            {
                return new ErrorResult(Messages.WorkoutProgramNameExists);
            }
            return new SuccessResult();
        }

        private IResult CheckMaxDayCount(int dayCount)
        {
            if (dayCount != 7)
            {
                return new ErrorResult("Antrenman programı tam olarak 7 gün olmalıdır.");
            }
            return new SuccessResult();
        }

        private IResult CheckDayNumbers(List<WorkoutProgramDayAddDto> days)
        {
            var dayNumbers = days.Select(d => d.DayNumber).ToList();

            // Gün numaraları benzersiz olmalı
            if (dayNumbers.Count != dayNumbers.Distinct().Count())
            {
                return new ErrorResult(Messages.DayNumbersMustBeUnique);
            }

            // Gün numaraları 1-7 arasında olmalı
            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult(Messages.DayNumbersInvalidRange);
            }

            return new SuccessResult();
        }

        private IResult CheckAtLeastOneWorkoutDay(List<WorkoutProgramDayAddDto> days)
        {
            // En az bir gün egzersiz günü olmalı (IsRestDay = false)
            if (days.All(d => d.IsRestDay))
            {
                return new ErrorResult(Messages.AtLeastOneWorkoutDayRequired);
            }

            return new SuccessResult();
        }

        private IResult CheckDayNumbers(List<WorkoutProgramDayUpdateDto> days)
        {
            var dayNumbers = days.Select(d => d.DayNumber).ToList();

            // Gün numaraları benzersiz olmalı
            if (dayNumbers.Count != dayNumbers.Distinct().Count())
            {
                return new ErrorResult(Messages.DayNumbersMustBeUnique);
            }

            // Gün numaraları 1-7 arasında olmalı
            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult(Messages.DayNumbersInvalidRange);
            }

            return new SuccessResult();
        }

        private IResult CheckAtLeastOneWorkoutDay(List<WorkoutProgramDayUpdateDto> days)
        {
            // En az bir gün egzersiz günü olmalı (IsRestDay = false)
            if (days.All(d => d.IsRestDay))
            {
                return new ErrorResult(Messages.AtLeastOneWorkoutDayRequired);
            }

            return new SuccessResult();
        }
    }
}
