﻿using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class CompanyValidator : AbstractValidator<Company>
    {
        public CompanyValidator()
        {
            RuleFor(c => c.CompanyName).NotEmpty().WithMessage("Şirket adı boş bırakılamaz.");
            RuleFor(c => c.PhoneNumber).NotEmpty().WithMessage("Telefon numarası boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).Must((company, phone) => BeUniquePhoneNumber(company)).WithMessage("Bu telefon numarası sistemde zaten kayıtlı.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");
        }

        private bool BeUniquePhoneNumber(Company company)
        {
            using (var context = new GymContext())
            {
                if (company.CompanyID != 0)
                {
                    // Mevcut şirket güncelleme durumu
                    return !context.Companies.Any(c =>
                        c.PhoneNumber == company.PhoneNumber &&
                        c.CompanyID != company.CompanyID &&
                        c.IsActive == true);
                }
                // Yeni şirket ekleme durumu
                return !context.Companies.Any(c =>
                    c.PhoneNumber == company.PhoneNumber &&
                    c.IsActive == true);
            }
        }

     

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }
    }
}