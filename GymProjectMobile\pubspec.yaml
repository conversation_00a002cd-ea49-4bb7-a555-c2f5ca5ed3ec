name: gymprojectmobile
description: "GymKod Pro - Profesyonel Spor Salonu Yönetim Sistemi Mobil Uygulaması"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.6.1

  # Routing
  go_router: ^14.6.2

  # HTTP Client
  dio: ^5.7.0

  # Secure Storage
  flutter_secure_storage: ^9.2.2
  shared_preferences: ^2.3.2

  # JWT Handling
  jwt_decoder: ^2.0.1

  # QR Code
  qr_flutter: ^4.1.0
  # qr_code_scanner: ^1.0.1 # TODO: Android NDK uyumluluk sorunu, daha sonra eklenecek

  # UI Components
  google_fonts: ^6.2.1

  # Device Info
  device_info_plus: ^11.1.0

  # Permissions
  permission_handler: ^11.3.1

  # Utils
  intl: ^0.19.0
  mutex: ^3.1.0

  # Image Picker
  image_picker: ^1.1.2

  # HTTP Parser (for multipart)
  http_parser: ^4.0.2

  # Cached Network Image (for efficient image caching)
  cached_network_image: ^3.3.1

  # Cache Manager (for manual cache management)
  flutter_cache_manager: ^3.3.1

  # JSON Serialization
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # JSON Code Generation
  json_serializable: ^6.8.0
  build_runner: ^2.4.13

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
