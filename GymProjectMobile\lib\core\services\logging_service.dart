/// Logging Service - GymKod Pro Mobile
///
/// Bu service debug ve error logging işlemlerini yapar.
/// Referans: Angular frontend'deki console.log kullanımı
library;

import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Log Level Enum
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Log Level Extensions
extension LogLevelExtension on LogLevel {
  String get name {
    switch (this) {
      case LogLevel.debug:
        return 'DEBUG';
      case LogLevel.info:
        return 'INFO';
      case LogLevel.warning:
        return 'WARNING';
      case LogLevel.error:
        return 'ERROR';
      case LogLevel.critical:
        return 'CRITICAL';
    }
  }

  String get emoji {
    switch (this) {
      case LogLevel.debug:
        return '🐛';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
      case LogLevel.critical:
        return '🚨';
    }
  }
}

/// Logging Service
/// Angular frontend'deki console service'e benzer
class LoggingService {
  static const String _appName = 'GymKodPro';
  static LogLevel _minLogLevel = LogLevel.debug;
  static bool _enableLogging = kDebugMode;

  // Singleton pattern
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();

  /// Logging'i etkinleştir/devre dışı bırak
  static void setLoggingEnabled(bool enabled) {
    _enableLogging = enabled;
  }

  /// Minimum log level'ı ayarla
  static void setMinLogLevel(LogLevel level) {
    _minLogLevel = level;
  }

  /// Debug log
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Info log
  static void info(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Warning log
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Error log
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Critical log
  static void critical(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.critical, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Ana log metodu
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (!_enableLogging) return;
    if (level.index < _minLogLevel.index) return;

    final timestamp = DateTime.now().toIso8601String();
    final tagStr = tag ?? _getCallerInfo();
    final levelStr = level.name;
    final emoji = level.emoji;

    final logMessage = '[$timestamp] $emoji [$levelStr] [$_appName] [$tagStr] $message';

    // Console'a yazdır
    if (kDebugMode) {
      print(logMessage);

      if (error != null) {
        print('Error: $error');
      }

      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }

    // Developer log'a yazdır (Flutter DevTools için)
    developer.log(
      message,
      time: DateTime.now(),
      level: _getLogLevelValue(level),
      name: '$_appName.$tagStr',
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log level'ı developer log için int değere çevir
  static int _getLogLevelValue(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
      case LogLevel.critical:
        return 1200;
    }
  }

  /// Caller bilgisini al (basit stack trace analizi)
  static String _getCallerInfo() {
    try {
      final stackTrace = StackTrace.current;
      final lines = stackTrace.toString().split('\n');

      // İlk birkaç satırı atla (bu metod ve _log metodu)
      for (int i = 3; i < lines.length && i < 10; i++) {
        final line = lines[i].trim();
        if (line.isNotEmpty && !line.contains('LoggingService')) {
          // Dosya adını ve satır numarasını çıkar
          final match = RegExp(r'([^/\\]+\.dart):(\d+)').firstMatch(line);
          if (match != null) {
            return '${match.group(1)}:${match.group(2)}';
          }
        }
      }

      return 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }

  /// API request log'u
  static void apiRequest(String method, String url, {Map<String, dynamic>? data}) {
    debug('API Request: $method $url', tag: 'API');
    if (data != null && data.isNotEmpty) {
      debug('Request Data: $data', tag: 'API');
    }
  }

  /// API response log'u
  static void apiResponse(String method, String url, int statusCode, {dynamic data}) {
    final level = statusCode >= 400 ? LogLevel.error : LogLevel.debug;
    _log(level, 'API Response: $method $url - Status: $statusCode', tag: 'API');
    if (data != null) {
      _log(level, 'Response Data: $data', tag: 'API');
    }
  }

  /// API error log'u
  static void apiError(String method, String url, Object error, {StackTrace? stackTrace}) {
    LoggingService.error('API Error: $method $url', tag: 'API', error: error, stackTrace: stackTrace);
  }

  /// API log'u (genel)
  static void apiLog(String action, String? details) {
    debug('API: $action${details != null ? ' - $details' : ''}', tag: 'API');
  }

  /// Auth işlem log'u
  static void authLog(String action, {String? details}) {
    info('Auth: $action${details != null ? ' - $details' : ''}', tag: 'AUTH');
  }

  /// Production token logging (daha az verbose)
  static void tokenProductionLog(String message, {String? details}) {
    // Sadece debug mode'da log
    if (kDebugMode) {
      final logMessage = details != null ? '$message - $details' : message;
      info(logMessage, tag: 'TOKEN_PROD');
    }
  }

  /// Storage işlem log'u
  static void storageLog(String action, String key, {String? details}) {
    debug('Storage: $action - Key: $key${details != null ? ' - $details' : ''}', tag: 'STORAGE');
  }

  /// JWT işlem log'u
  static void jwtLog(String action, {String? details}) {
    debug('JWT: $action${details != null ? ' - $details' : ''}', tag: 'JWT');
  }

  /// Navigation log'u
  static void navigationLog(String action, String route, {String? details}) {
    debug('Navigation: $action - Route: $route${details != null ? ' - $details' : ''}', tag: 'NAV');
  }

  /// Performance log'u
  static void performanceLog(String action, Duration duration, {String? details}) {
    info('Performance: $action - Duration: ${duration.inMilliseconds}ms${details != null ? ' - $details' : ''}', tag: 'PERF');
  }

  /// Exception log'u (try-catch'lerde kullanım için)
  static void logException(Object exception, StackTrace stackTrace, {String? context}) {
    error(
      'Exception${context != null ? ' in $context' : ''}: ${exception.toString()}',
      tag: 'EXCEPTION',
      error: exception,
      stackTrace: stackTrace,
    );
  }

  /// Widget lifecycle log'u
  static void widgetLog(String widgetName, String lifecycle, {String? details}) {
    debug('Widget: $widgetName - $lifecycle${details != null ? ' - $details' : ''}', tag: 'WIDGET');
  }

  /// State management log'u
  static void stateLog(String provider, String action, {dynamic state}) {
    debug('State: $provider - $action${state != null ? ' - State: $state' : ''}', tag: 'STATE');
  }

  /// Device log'u
  static void deviceLog(String action, {String? details}) {
    debug('Device: $action${details != null ? ' - $details' : ''}', tag: 'DEVICE');
  }

  /// QR Code log'u
  static void qrLog(String action, {String? details}) {
    info('QR: $action${details != null ? ' - $details' : ''}', tag: 'QR');
  }

  /// Tüm log'ları temizle (sadece debug mode'da)
  static void clearLogs() {
    if (kDebugMode) {
      debug('Logs cleared', tag: 'SYSTEM');
    }
  }

  /// Log istatistiklerini al
  static Map<String, dynamic> getLogStats() {
    return {
      'loggingEnabled': _enableLogging,
      'minLogLevel': _minLogLevel.name,
      'isDebugMode': kDebugMode,
      'appName': _appName,
    };
  }

  /// Log ayarlarını debug string olarak al
  static String getDebugInfo() {
    final stats = getLogStats();
    return '''
Logging Debug Info:
Enabled: ${stats['loggingEnabled']}
Min Level: ${stats['minLogLevel']}
Debug Mode: ${stats['isDebugMode']}
App Name: ${stats['appName']}
''';
  }
}
