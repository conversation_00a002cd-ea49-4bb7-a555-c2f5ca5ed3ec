/// Change Password Page - GymKod Pro Mobile
///
/// Bu sayfa Angular frontend'deki change-password component'inden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/app/components/change-password/
///
/// RESPONSIVE DESIGN:
/// - Responsive form layout ve spacing
/// - Responsive typography scaling
/// - Responsive container sizing ve max width
/// - Responsive icon sizes ve button heights
/// - Responsive card padding ve margins
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/widgets.dart';
import '../providers/auth_provider.dart';

/// Change Password Page
/// Angular frontend'deki change-password component'e benzer
class ChangePasswordPage extends ConsumerStatefulWidget {
  /// Zorunlu ş<PERSON><PERSON>tirme mi (ilk giriş)
  final bool isRequired;

  const ChangePasswordPage({
    super.key,
    this.isRequired = false,
  });

  @override
  ConsumerState<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends ConsumerState<ChangePasswordPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _currentPasswordFocusNode = FocusNode();
  final _newPasswordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Animation setup (Angular'daki page transition'a benzer)
    _animationController = AnimationController(
      duration: AppConstants.normalAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();

    LoggingService.widgetLog('ChangePasswordPage', 'initState', details: 'isRequired: ${widget.isRequired}');
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _currentPasswordFocusNode.dispose();
    _newPasswordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Form validation
  bool _validateForm() {
    return _formKey.currentState?.validate() ?? false;
  }

  /// Şifre eşleşme kontrolü (Angular frontend pattern)
  String? _validatePasswordMatch(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.passwordRequiredMessage;
    }
    if (value != _newPasswordController.text) {
      return 'Yeni şifre ve şifre tekrarı eşleşmiyor';
    }
    return null;
  }

  /// Şifre değiştirme işlemi
  Future<void> _handleChangePassword() async {
    if (!_validateForm()) return;

    final currentPassword = _currentPasswordController.text;
    final newPassword = _newPasswordController.text;

    LoggingService.authLog('Change password attempt');

    final success = await ref.read(authProvider.notifier).changePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );

    if (success && mounted) {
      LoggingService.navigationLog('Password changed successfully', '/home');

      // Angular frontend pattern: localStorage.removeItem('requirePasswordChange')
      // Bu bizde otomatik olarak auth provider'da yapılıyor

      // Başarı mesajı göster
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Şifreniz başarıyla değiştirildi'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // Smart navigation helper kullan
      final authState = ref.read(authProvider);
      if (authState.user != null) {
        final navigationHelper = NavigationHelperService();
        await navigationHelper.handlePostPasswordChangeNavigation(
          context,
          authState.user!,
          widget.isRequired
        );
      } else {
        // User bilgisi yoksa login'e yönlendir
        if (mounted) context.go('/auth/login');
      }
    }
  }

  /// Geri çıkma kontrolü (zorunlu şifre değiştirme için)
  Future<bool> _onWillPop() async {
    if (widget.isRequired) {
      // Zorunlu şifre değiştirme sırasında geri çıkışa izin verme
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Güvenliğiniz için şifrenizi değiştirmeniz gerekmektedir'),
          backgroundColor: AppColors.warning,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return PopScope(
          canPop: !widget.isRequired,
          onPopInvokedWithResult: (didPop, result) async {
            if (!didPop) {
              await _onWillPop();
            }
          },
          child: Scaffold(
            backgroundColor: theme.colorScheme.surface,
            appBar: widget.isRequired ? null : AppBar(
              title: const Text('Şifre Değiştir'),
              backgroundColor: theme.colorScheme.surface,
              foregroundColor: theme.colorScheme.onSurface,
              elevation: 0,
            ),
            body: Container(
              decoration: BoxDecoration(
                // Angular change-password background
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: theme.brightness == Brightness.light
                      ? AppColors.backgroundGradient
                      : AppColors.darkBackgroundGradient,
                ),
              ),
              child: SafeArea(
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: ResponsiveContainer(
                          maxWidth: AppSpacing.responsiveMaxWidth(context),
                          padding: AppSpacing.responsiveScreenPadding(context),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Responsive Header
                                _buildResponsiveHeader(theme, deviceType),

                                ResponsiveSpacing.vertical(
                                  mobile: 24.0,
                                  tablet: 32.0,
                                  desktop: 40.0,
                                ),

                                // Responsive Change Password Form Card
                                _buildResponsiveChangePasswordForm(theme, authState, deviceType),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Responsive Header (Icon + Title + Info)
  Widget _buildResponsiveHeader(ThemeData theme, DeviceType deviceType) {
    // Responsive icon size
    final iconContainerSize = AppSpacing.responsive(context,
      mobile: 70.0,
      tablet: 80.0,
      desktop: 90.0,
    );

    final iconSize = AppSpacing.responsiveIconSize(context,
      mobile: 35.0,
      tablet: 40.0,
      desktop: 45.0,
    );

    return Column(
      children: [
        // Responsive Icon Container
        Container(
          width: iconContainerSize,
          height: iconContainerSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: widget.isRequired
                  ? AppColors.warningGradient
                  : AppColors.primaryGradient,
            ),
            boxShadow: [
              BoxShadow(
                color: (widget.isRequired ? AppColors.warning : theme.colorScheme.primary)
                    .withValues(alpha: 0.3),
                blurRadius: AppSpacing.responsiveElevation(context) * 3,
                offset: Offset(0, AppSpacing.responsiveElevation(context)),
              ),
            ],
          ),
          child: Icon(
            widget.isRequired ? Icons.security : Icons.lock_reset,
            size: iconSize,
            color: theme.colorScheme.onPrimary,
          ),
        ),

        ResponsiveSpacing.vertical(
          mobile: 20.0,
          tablet: 24.0,
          desktop: 28.0,
        ),

        // Responsive Title
        ResponsiveText(
          widget.isRequired
              ? 'Şifrenizi Değiştirmeniz Gerekiyor'
              : 'Şifre Değiştirme',
          textType: 'h2',
          style: TextStyle(
            color: theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),

        ResponsiveSpacing.vertical(
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        ),

        // Responsive Info Message
        ResponsiveCard(
          padding: AppSpacing.responsiveCardPadding(context),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: widget.isRequired
                    ? AppColors.warning
                    : AppColors.info,
                size: AppSpacing.responsiveIconSize(context,
                  mobile: 20.0,
                  tablet: 24.0,
                  desktop: 28.0,
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 12.0,
                desktop: 16.0,
              ),
              Expanded(
                child: ResponsiveText(
                  widget.isRequired
                      ? 'Güvenliğiniz için, ilk girişte şifrenizi değiştirmeniz gerekmektedir. Yeni şifreniz en az 6 karakter olmalıdır.'
                      : 'Güvenliğiniz için şifrenizi düzenli olarak değiştirmenizi öneririz.',
                  textType: 'bodymedium',
                  style: TextStyle(
                    color: widget.isRequired
                        ? AppColors.warning
                        : AppColors.info,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Responsive Change Password Form
  Widget _buildResponsiveChangePasswordForm(ThemeData theme, AuthState authState, DeviceType deviceType) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Current Password Field (Angular current password input'a benzer)
            PasswordTextField(
              label: 'Mevcut Şifre',
              hintText: 'Mevcut şifrenizi girin',
              controller: _currentPasswordController,
              focusNode: _currentPasswordFocusNode,
              textInputAction: TextInputAction.next,
              enabled: !authState.isLoading,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.passwordRequiredMessage;
                }
                return null;
              },
              onSubmitted: (_) => _newPasswordFocusNode.requestFocus(),
            ),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // New Password Field (Angular new password input'a benzer)
            PasswordTextField(
              label: 'Yeni Şifre',
              hintText: 'Yeni şifrenizi girin (en az 6 karakter)',
              controller: _newPasswordController,
              focusNode: _newPasswordFocusNode,
              textInputAction: TextInputAction.next,
              enabled: !authState.isLoading,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.passwordRequiredMessage;
                }
                if (value.length < AppConstants.minPasswordLength) {
                  return AppConstants.passwordTooShortMessage;
                }
                return null;
              },
              onSubmitted: (_) => _confirmPasswordFocusNode.requestFocus(),
            ),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Confirm Password Field (Angular confirm password input'a benzer)
            PasswordTextField(
              label: 'Şifre Tekrarı',
              hintText: 'Yeni şifrenizi tekrar girin',
              controller: _confirmPasswordController,
              focusNode: _confirmPasswordFocusNode,
              textInputAction: TextInputAction.done,
              enabled: !authState.isLoading,
              validator: _validatePasswordMatch,
              onSubmitted: (_) => _handleChangePassword(),
            ),

            ResponsiveSpacing.vertical(
              mobile: 20.0,
              tablet: 24.0,
              desktop: 28.0,
            ),

            // Responsive Error Message
            if (authState.error != null) ...[
              ResponsiveCard(
                padding: AppSpacing.responsivePadding(context,
                  mobile: const EdgeInsets.all(12.0),
                  tablet: const EdgeInsets.all(16.0),
                  desktop: const EdgeInsets.all(20.0),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: AppColors.danger,
                      size: AppSpacing.responsiveIconSize(context,
                        mobile: 20.0,
                        tablet: 24.0,
                        desktop: 28.0,
                      ),
                    ),
                    ResponsiveSpacing.horizontal(
                      mobile: 8.0,
                      tablet: 12.0,
                      desktop: 16.0,
                    ),
                    Expanded(
                      child: ResponsiveText(
                        authState.error!,
                        textType: 'bodymedium',
                        style: const TextStyle(
                          color: AppColors.danger,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              ResponsiveSpacing.vertical(
                mobile: 16.0,
                tablet: 20.0,
                desktop: 24.0,
              ),
            ],

            // Responsive Change Password Button
            SizedBox(
              height: AppSpacing.responsiveButtonHeight(context),
              child: PrimaryButton(
                text: 'Şifreyi Değiştir',
                onPressed: authState.isLoading ? null : _handleChangePassword,
                isLoading: authState.isLoading,
                icon: Icons.security,
              ),
            ),
          ],
        ),
      ),
    );
  }

}
