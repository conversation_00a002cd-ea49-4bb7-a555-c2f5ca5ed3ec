/* Company User Detail Dialog Styles */

/* Dialog Container */
.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--card-bg-color);
  color: var(--text-color);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--bg-primary-rgb, 255, 255, 255), 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
  color: var(--text-color);
}

.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Readonly Mode Styles */
.readonly-mode .form-control,
.readonly-mode .form-select,
.readonly-mode .modern-textarea {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-muted) !important;
  cursor: default !important;
  opacity: 0.8;
}

.readonly-mode .form-control:focus,
.readonly-mode .form-select:focus,
.readonly-mode .modern-textarea:focus {
  box-shadow: none !important;
  border-color: var(--border-color) !important;
}

.readonly-mode .form-label::after {
  display: none; /* Gerekli alan yıldızını gizle */
}

/* Dialog Header */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--card-bg-color);
}

.user-header-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.avatar-circle-xl {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.user-header-details {
  flex: 1;
}

.user-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.user-email {
  color: var(--text-muted);
  font-size: 1rem;
  margin-bottom: 0.75rem;
}

.btn-close {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-close:hover {
  background: #dc3545;
  color: white;
  transform: scale(1.1);
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  padding: 0 2rem;
}

.tab-button {
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: var(--text-muted);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  position: relative;
}

.tab-button:hover {
  color: var(--primary-color);
  background: rgba(67, 97, 238, 0.1);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: rgba(67, 97, 238, 0.1);
}

/* Dialog Content */
.dialog-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: var(--background-color);
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Form Sections */
.form-section {
  background: var(--card-bg-color);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
}

.section-title i {
  color: var(--primary-color);
}

/* Form Controls */
.form-label {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: ' *';
  color: #dc3545;
}

.modern-input,
.modern-select,
.modern-textarea {
  border: 1px solid var(--input-border);
  border-radius: 8px;
  background: var(--input-bg);
  color: var(--input-text);
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
  background: var(--input-bg);
  outline: none;
}

.modern-input:disabled,
.modern-select:disabled {
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-muted);
  cursor: not-allowed;
}

.modern-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Validation Styles */
.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Statistics Tab */
.statistics-tab {
  padding: 0;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-icon-members {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon-active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon-revenue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon-status {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.stat-content p {
  color: var(--text-muted);
  margin: 0;
  font-size: 0.9rem;
}

/* User Details Section */
.user-details-section {
  background: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: var(--text-muted);
}

.detail-value {
  font-weight: 600;
  color: var(--text-color);
}

/* Dialog Footer */
.dialog-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
  background: var(--card-bg-color);
  backdrop-filter: blur(10px);
}

.footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  align-items: center;
}

/* Modern Dialog Buttons */
.btn-dialog {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  text-transform: none;
  letter-spacing: 0.025em;
}

.btn-dialog:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-dialog:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-dialog:not(:disabled):active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Cancel Button */
.btn-dialog-cancel {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-dialog-cancel:not(:disabled):hover {
  background: linear-gradient(135deg, #495057 0%, #5a6c7d 100%);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

/* Save Button */
.btn-dialog-save {
  background: linear-gradient(135deg, var(--primary-color) 0%, #3a0ca3 100%);
  color: white;
  border: 2px solid transparent;
  position: relative;
}

.btn-dialog-save:not(:disabled):hover {
  background: linear-gradient(135deg, #3a0ca3 0%, #3d52a0 100%);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.btn-dialog-save:disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

/* Custom Spinner */
.spinner-sm {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Company ID Badge in Header */
.company-id-badge {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-muted);
  margin-left: 0.5rem;
  opacity: 0.8;
}

/* Status Badges */
.status-active {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-inactive {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.status-user-inactive {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.status-password-required {
  background-color: rgba(76, 201, 240, 0.1);
  color: #4cc9f0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dialog-header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .user-header-info {
    flex-direction: column;
    text-align: center;
  }
  
  .tab-navigation {
    padding: 0 1rem;
    flex-wrap: wrap;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .dialog-content {
    padding: 1.5rem;
  }
  
  .form-section {
    padding: 1.5rem;
  }
  
  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .dialog-footer {
    padding: 1rem 1.5rem;
  }

  .footer-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-dialog {
    width: 100%;
    min-width: auto;
  }

  .company-id-badge {
    display: block;
    margin-left: 0;
    margin-top: 0.25rem;
    font-size: 0.8rem;
  }
}

/* Dark Mode Compatibility */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(0, 0, 0, 0.9);
}

[data-theme="dark"] .stat-card:hover {
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .btn-dialog-cancel {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

[data-theme="dark"] .btn-dialog-cancel:not(:disabled):hover {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  box-shadow: 0 8px 25px rgba(74, 85, 104, 0.4);
}

[data-theme="dark"] .btn-dialog-save:not(:disabled):hover {
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.6);
}

[data-theme="dark"] .company-id-badge {
  color: var(--text-muted);
}

/* Dark Mode Specific Readonly Styles */
[data-theme="dark"] .readonly-mode .form-control,
[data-theme="dark"] .readonly-mode .form-select,
[data-theme="dark"] .readonly-mode .modern-textarea {
  background-color: #2d2d2d !important;
  border-color: #495057 !important;
  color: #adb5bd !important;
}

/* Dark Mode Form Controls */
[data-theme="dark"] .modern-input,
[data-theme="dark"] .modern-select,
[data-theme="dark"] .modern-textarea {
  background: var(--input-bg);
  color: var(--input-text);
  border-color: var(--input-border);
}

[data-theme="dark"] .modern-input:focus,
[data-theme="dark"] .modern-select:focus,
[data-theme="dark"] .modern-textarea:focus {
  background: var(--input-bg);
  color: var(--input-text);
}

/* Dark Mode Placeholder Text */
[data-theme="dark"] .modern-input::placeholder,
[data-theme="dark"] .modern-select::placeholder,
[data-theme="dark"] .modern-textarea::placeholder {
  color: #adb5bd;
  opacity: 0.8;
}

/* ID Display Field Styles */
.id-display-field {
  padding: 8px 12px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  min-height: 38px;
  display: flex;
  align-items: center;
}

.id-display-field .modern-badge {
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* ID Badge Colors */
.modern-badge-primary {
  background-color: #0d6efd;
  color: white;
}

.modern-badge-secondary {
  background-color: #6c757d;
  color: white;
}

.modern-badge-info {
  background-color: #0dcaf0;
  color: #000;
}

.modern-badge-warning {
  background-color: #ffc107;
  color: #000;
}

.modern-badge-success {
  background-color: #198754;
  color: white;
}

/* Dark Mode ID Display */
[data-theme="dark"] .id-display-field {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color-dark);
}

[data-theme="dark"] .modern-badge-info {
  background-color: #087990;
  color: white;
}

[data-theme="dark"] .modern-badge-warning {
  background-color: #997404;
  color: white;
}
