/// Login Page - GymKod Pro Mobile
///
/// Bu sayfa Angular frontend'deki login component'inden uyarlanmıştır.
/// Referans: Angular login component tasarımı
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/widgets.dart';
import '../providers/auth_provider.dart';

/// Login Page
/// Angular frontend'deki login component'e benzer
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Animation setup (Angular'daki page transition'a benzer)
    _animationController = AnimationController(
      duration: AppConstants.normalAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();

    // Focus'u engellemek için delay ekle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).unfocus();
    });

    LoggingService.widgetLog('LoginPage', 'initState');
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Form validation
  bool _validateForm() {
    return _formKey.currentState?.validate() ?? false;
  }

  /// Login işlemi
  Future<void> _handleLogin() async {
    if (!_validateForm()) return;

    // Login başlamadan önce error'ı temizle
    ref.read(authProvider.notifier).clearError();

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    LoggingService.authLog('Login attempt', details: 'Email: $email');

    final success = await ref.read(authProvider.notifier).login(
      email: email,
      password: password,
    );

    if (success && mounted) {
      // Kısa bir delay ekle ki auth state güncellensin
      await Future.delayed(const Duration(milliseconds: 100));

      // Şifre değiştirme zorunluluğu kontrolü (Angular frontend pattern)
      final authState = ref.read(authProvider);

      // Debug log
      LoggingService.navigationLog('Login success - checking password change requirement',
        'RequirePasswordChange: ${authState.requirePasswordChange}, AuthState: ${authState.toString()}');

      // Smart navigation helper kullan
      if (mounted) {
        final navigationHelper = NavigationHelperService();
        await navigationHelper.handlePostLoginNavigation(
          context,
          authState.user!,
          authState.requirePasswordChange
        );
      }
    }
  }

  /// Register sayfasına git
  void _goToRegister() {
    LoggingService.navigationLog('Navigate to register', '/auth/register');
    context.push('/auth/register');
  }

  /// Member register sayfasına git
  void _goToMemberRegister() {
    LoggingService.navigationLog('Navigate to member register', '/auth/register-member');
    context.push('/auth/register-member');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Container(
        decoration: BoxDecoration(
          // Angular login background gradient'ı
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: theme.brightness == Brightness.light
                ? AppColors.backgroundGradient
                : AppColors.darkBackgroundGradient,
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Center(
                    child: ResponsiveContainer(
                      maxWidth: 400,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Logo ve Title (Angular login-header'a benzer)
                            _buildHeader(theme),

                            const ResponsiveSpacing.vertical(
                              mobile: 24.0,
                              tablet: 28.0,
                              desktop: 32.0,
                            ),

                            // Login Form Card (Angular login-form'a benzer)
                            _buildLoginForm(theme, authState),

                            const ResponsiveSpacing.vertical(
                              mobile: 20.0,
                              tablet: 22.0,
                              desktop: 24.0,
                            ),

                            // Register Links (Angular register-links'e benzer)
                            _buildRegisterLinks(theme),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// Header (Logo + Title)
  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        // Logo (Angular login-logo'ya benzer)
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: AppColors.primaryGradient,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Icon(
            Icons.fitness_center,
            size: 40,
            color: theme.colorScheme.onPrimary,
          ),
        ),

        const SizedBox(height: AppSpacing.marginLG),

        // Title (Angular login-title'a benzer) - Responsive
        ResponsiveText(
          'Giriş Yap',
          textType: 'h2',
          style: TextStyle(
            color: theme.colorScheme.onSurface,
          ),
        ),

        const ResponsiveSpacing.vertical(
          mobile: 6.0,
          tablet: 7.0,
          desktop: 8.0,
        ),

        // Subtitle - Responsive
        ResponsiveText(
          'Hesabınıza giriş yapın',
          textType: 'bodylarge',
          style: TextStyle(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  /// Login Form - Responsive
  Widget _buildLoginForm(ThemeData theme, AuthState authState) {
    return ResponsiveCard(
      child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            // Email Field (Angular email input'a benzer)
            EmailTextField(
              label: 'E-posta',
              hintText: 'E-posta adresinizi girin',
              controller: _emailController,
              focusNode: _emailFocusNode,
              textInputAction: TextInputAction.next,
              autofocus: false,
              enabled: !authState.isLoading,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.emailRequiredMessage;
                }
                if (!RegExp(AppConstants.emailPattern).hasMatch(value)) {
                  return AppConstants.emailInvalidMessage;
                }
                return null;
              },
              onSubmitted: (_) => _passwordFocusNode.requestFocus(),
            ),

            ResponsiveSpacing.vertical(
              mobile: AppSpacing.responsiveFormFieldSpacing(context),
              tablet: AppSpacing.responsiveFormFieldSpacing(context),
              desktop: AppSpacing.responsiveFormFieldSpacing(context),
            ),

            // Password Field (Angular password input'a benzer)
            PasswordTextField(
              label: 'Şifre',
              hintText: 'Şifrenizi girin',
              controller: _passwordController,
              focusNode: _passwordFocusNode,
              textInputAction: TextInputAction.done,
              enabled: !authState.isLoading,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.passwordRequiredMessage;
                }
                if (value.length < AppConstants.minPasswordLength) {
                  return AppConstants.passwordTooShortMessage;
                }
                return null;
              },
              onSubmitted: (_) => _handleLogin(),
            ),

            const SizedBox(height: AppSpacing.formButtonSpacing),

            // Error Message (Angular error-message'a benzer)
            if (authState.error != null) ...[
              Container(
                padding: const EdgeInsets.all(AppSpacing.paddingMD),
                decoration: BoxDecoration(
                  color: AppColors.dangerLight,
                  borderRadius: BorderRadius.circular(AppSpacing.radiusMD),
                  border: Border.all(color: AppColors.danger),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: AppColors.danger,
                      size: AppSpacing.iconMD,
                    ),
                    const SizedBox(width: AppSpacing.marginSM),
                    Expanded(
                      child: Text(
                        authState.error!,
                        style: AppTypography.errorText.copyWith(
                          color: AppColors.danger,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppSpacing.marginMD),
            ],

            // Login Button (Angular login-button'a benzer)
            PrimaryButton(
              text: 'Giriş Yap',
              onPressed: authState.isLoading ? null : _handleLogin,
              isLoading: authState.isLoading,
              icon: Icons.login,
            ),
          ],
        ),
      ),
    );
  }

  /// Register Links
  Widget _buildRegisterLinks(ThemeData theme) {
    return Column(
      children: [
        // Divider
        Row(
          children: [
            Expanded(
              child: Divider(
                color: theme.colorScheme.outline.withValues(alpha: 0.5),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.paddingMD),
              child: Text(
                'Hesabınız yok mu?',
                style: AppTypography.bodySmall.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ),
            Expanded(
              child: Divider(
                color: theme.colorScheme.outline.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),

        const SizedBox(height: AppSpacing.marginLG),

        // Register Buttons - Responsive
        ResponsiveButtonRow(
          children: [
            // Member Register (Angular register-member-button'a benzer)
            Expanded(
              child: OutlineButton(
                text: 'Üye Ol',
                onPressed: _goToMemberRegister,
                icon: Icons.person_add,
                size: CustomButtonSize.medium,
              ),
            ),

            // Admin Register (Angular register-button'a benzer)
            Expanded(
              child: SecondaryButton(
                text: 'Admin Kayıt',
                onPressed: _goToRegister,
                icon: Icons.admin_panel_settings,
                size: CustomButtonSize.medium,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
