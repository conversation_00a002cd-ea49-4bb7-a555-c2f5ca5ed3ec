import { Component, OnInit, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { map,distinctUntilChanged } from 'rxjs/operators';
import { faTimes, faUser, faDumbbell, faCalendar, faStickyNote } from '@fortawesome/free-solid-svg-icons';

import { MemberWorkoutProgramService, MemberWorkoutProgramAdd, MemberWorkoutProgramUpdate, MemberWorkoutProgramList } from '../../services/member-workout-program.service';
import { MemberService, Member } from '../../services/member.service';
import { WorkoutProgramService } from '../../services/workout-program.service';
import { WorkoutProgramTemplateList } from '../../models/workout-program.models';

export interface ModalData {
  mode: 'add' | 'edit';
  assignment?: MemberWorkoutProgramList;
}

@Component({
  selector: 'app-member-workout-assign-modal',
  templateUrl: './member-workout-assign-modal.component.html',
  styleUrls: ['./member-workout-assign-modal.component.css'],
  standalone: false
})
export class MemberWorkoutAssignModalComponent implements OnInit {
  // Icons
  faTimes = faTimes;
  faUser = faUser;
  faDumbbell = faDumbbell;
  faCalendar = faCalendar;
  faStickyNote = faStickyNote;

  // Form
  assignmentForm: FormGroup;
  
  // Data
  members: Member[] = [];
  workoutPrograms: WorkoutProgramTemplateList[] = [];

  // Autocomplete
  filteredMembers: Observable<Member[]>;

  // UI State
  isLoading = false;
  isSubmitting = false;
  
  // Modal properties
  isEditMode = false;
  modalTitle = '';

  constructor(
    private fb: FormBuilder,
    private memberWorkoutProgramService: MemberWorkoutProgramService,
    private memberService: MemberService,
    private workoutProgramService: WorkoutProgramService,
    private toastrService: ToastrService,
    public dialogRef: MatDialogRef<MemberWorkoutAssignModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ModalData
  ) {
    this.isEditMode = data.mode === 'edit';
    this.modalTitle = this.isEditMode ? 'Program Atamasını Düzenle' : 'Yeni Program Ata';
    
    this.assignmentForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadMembers();
    this.loadWorkoutPrograms();
  }

  setupMemberAutocomplete(): void {
    const memberIdControl = this.assignmentForm.get('memberID');
    if (memberIdControl) {
      this.filteredMembers = memberIdControl.valueChanges.pipe(
        distinctUntilChanged(),
        map(value => {
          if (typeof value === 'string') {
            if (!value.trim()) {
              return []; // Boş input durumunda boş liste döndür
            }
            return this._filterMembers(value);
          }
          return []; // Üye seçildiğinde dropdown'ı kapat
        })
      );
    }
  }

  private _filterMembers(value: string): Member[] {
    const filterValue = value.toLowerCase();
    return this.members.filter(member =>
      member.name.toLowerCase().includes(filterValue) ||
      member.phoneNumber.includes(filterValue)
    );
  }

  createForm(): FormGroup {
    return this.fb.group({
      memberID: [null, [Validators.required]],
      workoutProgramTemplateID: [null, [Validators.required]],
      notes: ['']
    });
  }

  loadMembers(): void {
    console.log('Loading members...');
    this.memberService.getActiveMembers().subscribe({
      next: (response: any) => {
        console.log('Members response:', response);
        if (response.success && response.data) {
          this.members = response.data;
          console.log('Members loaded:', this.members.length);
          // Üyeler yüklendikten sonra autocomplete'i setup et
          this.setupMemberAutocomplete();

          // Edit modunda ise form'u doldur
          if (this.isEditMode && this.data.assignment) {
            this.populateForm(this.data.assignment);
          }
        }
      },
      error: (error: any) => {
        console.error('Error loading members:', error);
        this.toastrService.error('Üyeler yüklenirken hata oluştu', 'Hata');
      }
    });
  }

  loadWorkoutPrograms(): void {
    this.workoutProgramService.getAll().subscribe({
      next: (response: any) => {
        if (response.success && response.data) {
          this.workoutPrograms = response.data;
        }
      },
      error: (error: any) => {
        console.error('Error loading workout programs:', error);
        this.toastrService.error('Antrenman programları yüklenirken hata oluştu', 'Hata');
      }
    });
  }

  populateForm(assignment: MemberWorkoutProgramList): void {
    // Üye bilgisini bul ve autocomplete'e set et
    const selectedMember = this.members.find(m => m.memberID === assignment.memberID);

    this.assignmentForm.patchValue({
      memberID: selectedMember || assignment.memberID, // Member object veya ID
      workoutProgramTemplateID: assignment.memberWorkoutProgramID, // Bu assignment ID'si, template ID'si değil
      notes: '' // Notes bilgisi assignment listesinde yok, detaydan alınmalı
    });

    // Detay bilgilerini al
    this.memberWorkoutProgramService.getAssignmentDetail(assignment.memberWorkoutProgramID).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.assignmentForm.patchValue({
            workoutProgramTemplateID: response.data.workoutProgramTemplateID,
            notes: response.data.notes || ''
          });
        }
      },
      error: (error) => {
        console.error('Error loading assignment detail:', error);
      }
    });
  }

  onSubmit(): void {
    // Member ID validation - object ise memberID property'si olmalı
    const memberIdValue = this.assignmentForm.get('memberID')?.value;
    if (typeof memberIdValue === 'object' && memberIdValue !== null && !memberIdValue.memberID) {
      this.toastrService.error('Lütfen geçerli bir üye seçin', 'Hata');
      return;
    }

    if (this.assignmentForm.valid) {
      this.isSubmitting = true;

      if (this.isEditMode) {
        this.updateAssignment();
      } else {
        this.createAssignment();
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  createAssignment(): void {
    const formValue = this.assignmentForm.value;

    // Member ID'yi düzgün handle et
    const memberId = typeof formValue.memberID === 'object' && formValue.memberID !== null
      ? formValue.memberID.memberID
      : formValue.memberID;

    const assignment: MemberWorkoutProgramAdd = {
      memberID: memberId,
      workoutProgramTemplateID: formValue.workoutProgramTemplateID,
      startDate: new Date(), // Program başlangıç tarihi (bugün)
      endDate: undefined, // Bitiş tarihi yok (süresiz)
      notes: formValue.notes || undefined
    };

    console.log('Sending assignment:', assignment);

    this.memberWorkoutProgramService.assignProgram(assignment).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success('Program başarıyla atandı', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message, 'Hata');
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error creating assignment:', error);
        this.toastrService.error('Program atanırken hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  updateAssignment(): void {
    const formValue = this.assignmentForm.value;

    const assignment: MemberWorkoutProgramUpdate = {
      memberWorkoutProgramID: this.data.assignment!.memberWorkoutProgramID,
      workoutProgramTemplateID: formValue.workoutProgramTemplateID, // Program değişikliği için eklendi
      startDate: new Date(this.data.assignment!.startDate), // Mevcut başlangıç tarihini koru
      endDate: undefined, // Bitiş tarihi yok
      notes: formValue.notes || undefined,
      isActive: true // Program ataması her zaman aktif
    };

    this.memberWorkoutProgramService.updateAssignment(assignment).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success('Program ataması başarıyla güncellendi', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message, 'Hata');
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error updating assignment:', error);
        this.toastrService.error('Program ataması güncellenirken hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  markFormGroupTouched(): void {
    Object.keys(this.assignmentForm.controls).forEach(key => {
      const control = this.assignmentForm.get(key);
      control?.markAsTouched();
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.assignmentForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.assignmentForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} zorunludur`;
      }
    }
    return '';
  }

  getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      memberID: 'Üye',
      workoutProgramTemplateID: 'Antrenman Programı',
      startDate: 'Başlangıç Tarihi',
      endDate: 'Bitiş Tarihi',
      notes: 'Notlar'
    };
    return labels[fieldName] || fieldName;
  }

  getMemberName(memberId: number): string {
    const member = this.members.find(m => m.memberID === memberId);
    return member ? member.name : '';
  }

  getProgramName(programId: number): string {
    const program = this.workoutPrograms.find(p => p.workoutProgramTemplateID === programId);
    return program ? program.programName : '';
  }

  displayMember(member: Member): string {
    return member ? `${member.name} - ${member.phoneNumber}` : '';
  }

  formatDate(date: string | Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('tr-TR');
  }
}
