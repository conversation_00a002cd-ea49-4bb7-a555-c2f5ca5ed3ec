﻿using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
   public class CompanyAdressValidator:AbstractValidator<CompanyAdress>
    {
        public CompanyAdressValidator()
        {
            RuleFor(c => c.Adress).NotEmpty().WithMessage("Adres boş bırakılamaz.");
            RuleFor(c => c.CompanyID).NotEmpty().WithMessage("Şirket ismi boş bırakılamaz.");
            RuleFor(c => c.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(c => c.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
            RuleFor(c => c).Must(BeUniqueAdress).WithMessage("Bu şirkete zaten adres verilmiş.");

        }
        private bool BeUniqueAdress(CompanyAdress adress)
        {
            using (var context = new GymContext())
            {
                if (adress.CompanyAdressID != 0)
                {
                    return !context.CompanyAdresses.Any(c =>
                        c.Adress == adress.Adress &&
                        c.CompanyID == adress.CompanyID &&
                        c.CompanyAdressID != adress.CompanyAdressID &&
                        c.IsActive == true);
                }
                else
                {
                    return !context.CompanyAdresses.Any(c =>
                        c.Adress == adress.Adress &&
                        c.CompanyID == adress.CompanyID &&
                        c.IsActive == true);
                }
            }
        }
    }
}
