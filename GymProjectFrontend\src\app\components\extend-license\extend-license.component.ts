import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { UserLicenseService } from '../../services/user-license.service';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseDto } from '../../models/UserLicenseDto';
import { LicensePackage } from '../../models/licensePackage';

import { LicenseExtensionByPackageDto } from '../../models/LicenseExtensionByPackageDto';

interface DialogData {
  userLicense: UserLicenseDto;
}

@Component({
  selector: 'app-extend-license',
  templateUrl: './extend-license.component.html',
  styleUrls: ['./extend-license.component.css'],
  standalone:false
})
export class ExtendLicenseComponent implements OnInit {
  extendForm: FormGroup;
  isSubmitting = false;
  licensePackages: LicensePackage[] = [];
  isLoadingPackages = false;

  paymentMethods = [
    { value: 'Nakit', label: 'Nakit', icon: 'fa-money-bill-wave' },
    { value: 'Kredi Kartı', label: 'Kredi Kartı', icon: 'fa-credit-card' },
    { value: 'Banka Havalesi-EFT', label: 'Banka Havalesi', icon: 'fa-university' }
  ];


  constructor(
    private fb: FormBuilder,
    private userLicenseService: UserLicenseService,
    private licensePackageService: LicensePackageService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<ExtendLicenseComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.extendForm = this.fb.group({
      licensePackageID: [null, Validators.required],
      paymentMethod: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadLicensePackages();
  }

  loadLicensePackages(): void {
    this.isLoadingPackages = true;
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        // Tüm aktif lisans paketlerini göster (rol filtresi kaldırıldı)
        this.licensePackages = response.data.filter(pkg => pkg.isActive !== false);
        this.isLoadingPackages = false;
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoadingPackages = false;
      }
    });
  }

  selectPaymentMethod(method: string): void {
    this.extendForm.patchValue({ paymentMethod: method });
  }

  onSubmit(): void {
    if (this.extendForm.invalid) {
      this.toastr.error('Lütfen formu doğru şekilde doldurun', 'Hata');
      return;
    }

    this.isSubmitting = true;
    const formValue = this.extendForm.value;

    // Paket bazında uzatma
    const licenseExtensionByPackageDto: LicenseExtensionByPackageDto = {
      userLicenseId: this.data.userLicense.userLicenseID,
      licensePackageId: formValue.licensePackageID,
      paymentMethod: formValue.paymentMethod
    };

    this.userLicenseService.extendLicenseByPackage(licenseExtensionByPackageDto).subscribe({
      next: (response) => {
        this.toastr.success(response.message, 'Başarılı');
        this.dialogRef.close(true);
        this.isSubmitting = false;
      },
      error: (error) => {
        this.toastr.error('Lisans uzatılırken bir hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  getSelectedPackage(): LicensePackage | null {
    const packageId = this.extendForm.get('licensePackageID')?.value;
    return this.licensePackages.find(pkg => pkg.licensePackageID === packageId) || null;
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  getUserInitials(): string {
    const name = this.data.userLicense.userName;
    if (!name) return 'U';
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  }

  getRemainingDaysClass(): string {
    const days = this.data.userLicense.remainingDays;
    if (days <= 0) {
      return 'modern-badge-danger';
    } else if (days <= 7) {
      return 'modern-badge-danger';
    } else if (days <= 30) {
      return 'modern-badge-warning';
    } else {
      return 'modern-badge-success';
    }
  }

  setExtensionType(type: 'days' | 'package'): void {
    this.extendForm.patchValue({ extensionType: type });
  }


}
