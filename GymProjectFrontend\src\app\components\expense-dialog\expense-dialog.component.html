<h2 mat-dialog-title>{{ isEditMode ? 'Gider Düzenle' : '<PERSON><PERSON>' }}</h2>

<mat-dialog-content>
  <form [formGroup]="expenseForm" class="expense-form">

    <!-- <PERSON><PERSON> Türü (Dropdown) -->
    <mat-form-field appearance="outline" class="w-100 mb-3">
      <mat-label>Gider Türü</mat-label>
      <mat-select formControlName="expenseType">
        <mat-option *ngFor="let type of expenseTypes" [value]="type">
          {{ type }}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="expenseType?.hasError('required')">
        Gider türü seçmek zorunludur.
      </mat-error>
       <mat-error *ngIf="expenseType?.hasError('maxlength')">
        Gider türü en fazla 100 karakter olabilir.
      </mat-error>
    </mat-form-field>

    <div class="row">
      <!-- Tutar -->
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100 mb-3">
          <mat-label>Tutar (TL)</mat-label>
          <input matInput type="number" formControlName="amount">
           <span matTextPrefix>₺&nbsp;</span>
          <mat-error *ngIf="amount?.hasError('required')">
            Tutar alanı zorunludur.
          </mat-error>
          <mat-error *ngIf="amount?.hasError('min')">
            Tutar 0'dan büyük olmalıdır.
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Gider Tarihi (Datepicker Geri Geldi) -->
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100 mb-3">
          <mat-label>Gider Tarihi</mat-label>
          <input matInput [matDatepicker]="picker" formControlName="expenseDate" readonly>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <mat-hint align="end">Takvim ikonuna tıklayın</mat-hint>
          <mat-error *ngIf="expenseDate?.hasError('required')">
            Gider tarihi zorunludur.
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Açıklama -->
    <mat-form-field appearance="outline" class="w-100 mb-3">
      <mat-label>Açıklama (Opsiyonel)</mat-label>
      <textarea matInput formControlName="description" rows="3"></textarea>
      <mat-hint>Giderin açıklamasını girin (örn: Elektrik Faturası)</mat-hint>
      <mat-error *ngIf="description?.hasError('maxlength')">
        Açıklama en fazla 500 karakter olabilir.
      </mat-error>
    </mat-form-field>

  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-stroked-button (click)="closeDialog()">İptal</button>
  <button mat-flat-button color="primary" (click)="saveExpense()" [disabled]="isLoading || expenseForm.invalid">
    <span *ngIf="!isLoading">{{ isEditMode ? 'Güncelle' : 'Kaydet' }}</span>
    <span *ngIf="isLoading">
        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
        Kaydediliyor...
    </span>
  </button>
</mat-dialog-actions>