using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.CrossCuttingConcerns.Caching.KeyGeneration;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Akıllı cache temizleme aspect'i - entity değişikliklerinde ilgili cache'leri otomatik temizler
    /// </summary>
    public class SmartCacheRemoveAspect : MethodInterception
    {
        private readonly string _entityName;
        private readonly string[] _additionalEntities;
        private readonly ICacheManager _cacheManager;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly ICompanyContext _companyContext;
        private readonly CacheConfiguration _configuration;

        public SmartCacheRemoveAspect(string entityName = null, params string[] additionalEntities)
        {
            _entityName = entityName;
            _additionalEntities = additionalEntities ?? Array.Empty<string>();
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _configuration = ServiceTool.ServiceProvider.GetService<CacheConfiguration>() ?? new CacheConfiguration();
        }

        protected override void OnSuccess(IInvocation invocation)
        {
            try
            {
                var tenantId = GetTenantId();
                if (tenantId <= 0)
                    return;

                // Ana entity'yi belirle
                var targetEntityName = _entityName ?? ExtractEntityNameFromMethod(invocation);
                
                // Ana entity cache'lerini temizle
                ClearEntityCaches(tenantId, targetEntityName);

                // İlgili entity'leri temizle
                ClearRelatedEntities(tenantId, targetEntityName);

                // Ek entity'leri temizle
                foreach (var additionalEntity in _additionalEntities)
                {
                    ClearEntityCaches(tenantId, additionalEntity);
                }

                System.Diagnostics.Debug.WriteLine($"SmartCacheRemove: Tenant {tenantId}, Entity: {targetEntityName}, Method: {invocation.Method.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SmartCacheRemove Error: {ex.Message}");
            }
        }

        private int GetTenantId()
        {
            try
            {
                return _companyContext?.GetCompanyId() ?? -1;
            }
            catch
            {
                return -1;
            }
        }

        private string ExtractEntityNameFromMethod(IInvocation invocation)
        {
            var className = invocation.Method.ReflectedType?.FullName ?? "Unknown";
            var parts = className.Split('.');
            var managerName = parts.LastOrDefault() ?? className;

            if (managerName.EndsWith("Manager"))
                return managerName.Substring(0, managerName.Length - 7);

            if (managerName.EndsWith("Service"))
                return managerName.Substring(0, managerName.Length - 7);

            return managerName;
        }

        private void ClearEntityCaches(int tenantId, string entityName)
        {
            if (string.IsNullOrEmpty(entityName))
                return;

            // Entity'nin tüm cache'lerini temizle
            _cacheManager.RemoveByEntity(tenantId, entityName);

            // Tag bazlı temizlik
            var tags = _keyGenerator.GenerateTags(tenantId, entityName);
            _cacheManager.RemoveByTags(tags);
        }

        private void ClearRelatedEntities(int tenantId, string entityName)
        {
            if (!_configuration.EntitySettings.TryGetValue(entityName, out var entitySettings))
                return;

            if (!entitySettings.InvalidateOnEntityChange)
                return;

            // İlgili entity'lerin cache'lerini temizle
            foreach (var relatedEntity in entitySettings.RelatedEntities)
            {
                ClearEntityCaches(tenantId, relatedEntity);
            }
        }
    }

    /// <summary>
    /// Belirli pattern'e göre cache temizleme aspect'i
    /// </summary>
    public class CacheRemoveByPatternAspect : MethodInterception
    {
        private readonly string _pattern;
        private readonly ICacheManager _cacheManager;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly ICompanyContext _companyContext;

        public CacheRemoveByPatternAspect(string pattern)
        {
            _pattern = pattern ?? throw new ArgumentNullException(nameof(pattern));
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
        }

        protected override void OnSuccess(IInvocation invocation)
        {
            try
            {
                var tenantId = GetTenantId();
                if (tenantId <= 0)
                    return;

                // Tenant-specific pattern oluştur
                var tenantPattern = $"T{tenantId}:*{_pattern}*";
                _cacheManager.RemoveByPattern(tenantPattern);

                System.Diagnostics.Debug.WriteLine($"CacheRemoveByPattern: Tenant {tenantId}, Pattern: {tenantPattern}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CacheRemoveByPattern Error: {ex.Message}");
            }
        }

        private int GetTenantId()
        {
            try
            {
                return _companyContext?.GetCompanyId() ?? -1;
            }
            catch
            {
                return -1;
            }
        }
    }

    /// <summary>
    /// Tag bazlı cache temizleme aspect'i
    /// </summary>
    public class CacheRemoveByTagsAspect : MethodInterception
    {
        private readonly string[] _tags;
        private readonly ICacheManager _cacheManager;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly ICompanyContext _companyContext;

        public CacheRemoveByTagsAspect(params string[] tags)
        {
            _tags = tags ?? throw new ArgumentNullException(nameof(tags));
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
        }

        protected override void OnSuccess(IInvocation invocation)
        {
            try
            {
                var tenantId = GetTenantId();
                if (tenantId <= 0)
                    return;

                // Tenant-specific tags oluştur
                var tenantTags = _tags.Select(tag => $"T{tenantId}:{tag}").ToArray();
                _cacheManager.RemoveByTags(tenantTags);

                System.Diagnostics.Debug.WriteLine($"CacheRemoveByTags: Tenant {tenantId}, Tags: [{string.Join(", ", tenantTags)}]");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CacheRemoveByTags Error: {ex.Message}");
            }
        }

        private int GetTenantId()
        {
            try
            {
                return _companyContext?.GetCompanyId() ?? -1;
            }
            catch
            {
                return -1;
            }
        }
    }
}
