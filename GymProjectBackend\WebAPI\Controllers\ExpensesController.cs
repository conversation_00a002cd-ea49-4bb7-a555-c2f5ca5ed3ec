using Business.Abstract;
using Core.Utilities.Paging;
using Entities.Concrete;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ExpensesController : ControllerBase
    {
        private readonly IExpenseService _expenseService;

        public ExpensesController(IExpenseService expenseService)
        {
            _expenseService = expenseService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _expenseService.GetAll();
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getbyid")]
        public IActionResult GetById(int id)
        {
            var result = _expenseService.GetById(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(Expense expense)
        {
            // ExpenseDate'in sadece tarih kısmını alıp saat/dakika/saniye'yi sıfırlayabiliriz
            // veya frontend'den gelen veriye güvenebiliriz. Şimdilik olduğu gibi bırakalım.
            var result = _expenseService.Add(expense);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("update")] // Genellikle PUT kullanılır ama mevcut projede POST tercih edilmiş olabilir.
        public IActionResult Update(Expense expense)
        {
            var result = _expenseService.Update(expense);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpDelete("delete")] // Genellikle HTTP DELETE kullanılır.
        public IActionResult Delete(int id)
        {
            var result = _expenseService.Delete(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getbydaterange")]
        public IActionResult GetByDateRange([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
        {
            // Başlangıç ve bitiş tarihlerinin geçerliliğini kontrol etmek iyi olabilir.
            if (startDate > endDate)
            {
                return BadRequest("Başlangıç tarihi bitiş tarihinden sonra olamaz.");
            }
            var result = _expenseService.GetExpensesByDateRange(startDate, endDate);
            return result.Success ? Ok(result) : BadRequest(result);
        }



        /// <summary>
        /// Dashboard için optimize edilmiş endpoint - 5 API isteği yerine 1 API isteği
        /// Tüm dashboard verilerini tek seferde döner
        /// </summary>
        [HttpGet("getdashboarddata")]
        public IActionResult GetDashboardData([FromQuery] int year, [FromQuery] int month)
        {
            if (year < 1900 || year > DateTime.Now.Year + 5 || month < 1 || month > 12)
            {
                return BadRequest("Geçersiz yıl veya ay değeri.");
            }

            var result = _expenseService.GetExpenseDashboardData(year, month);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getallpaginated")]
        public IActionResult GetAllPaginated([FromQuery] ExpensePagingParameters parameters)
        {
            var result = _expenseService.GetExpensesPaginated(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getallfiltered")]
        public IActionResult GetAllFiltered([FromQuery] ExpensePagingParameters parameters)
        {
            var result = _expenseService.GetAllExpensesFiltered(parameters);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}