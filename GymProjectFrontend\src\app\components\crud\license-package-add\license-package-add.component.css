/* License Package Add Component Styles */

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Form Section */
.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.section-title {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s var(--transition-timing);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.5s var(--transition-timing);
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.spinner-container {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .form-section {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .form-section:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
}

/* Fix for price input group */
.input-group {
  display: flex;
  align-items: center;
  height: 38px; /* Set a fixed height to match input height */
}

.input-group .modern-form-control {
  flex: 1;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group .input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  border-top-left-radius: var(--border-radius-sm);
  border-bottom-left-radius: var(--border-radius-sm);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  height: 100%;
  background-color: var(--bg-tertiary);
  width: 40px; /* Fixed width for the currency symbol */
  border: 1px solid var(--border-color);
  border-right: none;
  color: var(--text-secondary);
}

/* Textarea styling */
.modern-form-control[rows] {
  resize: vertical;
  min-height: 80px;
}

/* Checkbox styling */
.form-check {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
}

.form-check-input {
  margin-right: 0.5rem;
  width: 1.2em;
  height: 1.2em;
}

.form-check-label {
  font-size: 0.95rem;
  color: var(--text-primary);
  cursor: pointer;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .validity-selects {
    flex-direction: column;
  }
  
  .validity-selects select {
    margin-right: 0 !important;
    margin-bottom: 0.5rem;
  }
  
  .validity-selects select:last-child {
    margin-bottom: 0;
  }
  
  /* Ensure input group stays horizontal even on mobile */
  .input-group {
    flex-direction: row;
  }
}
