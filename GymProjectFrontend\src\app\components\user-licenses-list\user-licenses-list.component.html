<div class="container-fluid mt-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="modern-card-header">
          <div>
            <h3 class="mb-0 text-primary">
              <i class="fas fa-user-tag me-2"></i>Lisanslı Spor Salonları
            </h3>
            <small class="text-muted">Aktif lisansa sahip spor salonlarını yönetin</small>
          </div>
          <button
            class="modern-btn modern-btn-primary"
            (click)="openPurchaseDialog()">
            <i class="fas fa-plus me-2"></i><PERSON><PERSON>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card filter-card">
        <div class="modern-card-header">
          <h5 class="mb-0 filter-title">
            <i class="fas fa-filter me-2"></i>Filtreleme ve Arama
          </h5>
        </div>
        <div class="modern-card-body">
          <form [formGroup]="filterForm">
            <div class="filter-section">
              <div class="row g-3">
                <!-- Search -->
                <div class="col-md-4">
                  <label class="modern-label">
                    <i class="fas fa-search me-1"></i>Arama
                  </label>
                  <div class="modern-input-group">
                    <span class="modern-input-icon">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      type="text"
                      class="modern-input"
                      formControlName="searchTerm"
                      placeholder="Email veya şirket adı ile ara...">
                  </div>
                </div>

                <!-- Company Name Filter -->
                <div class="col-md-3">
                  <label class="modern-label">
                    <i class="fas fa-building me-1"></i>Şirket Adı
                  </label>
                  <input
                    type="text"
                    class="modern-input"
                    formControlName="companyName"
                    placeholder="Şirket adı filtrele...">
                </div>

                <!-- Sort By -->
                <div class="col-md-3">
                  <label class="modern-label">
                    <i class="fas fa-sort me-1"></i>Sıralama
                  </label>
                  <select class="modern-select" formControlName="sortBy">
                    <option *ngFor="let option of sortOptions" [value]="option.value">
                      {{ option.label }}
                    </option>
                  </select>
                </div>

                <!-- Clear Filters -->
                <div class="col-md-2 d-flex align-items-end">
                  <button
                    type="button"
                    class="modern-btn modern-btn-secondary w-100"
                    (click)="clearFilters()">
                    <i class="fas fa-times me-1"></i>Temizle
                  </button>
                </div>
              </div>
            </div>

            <!-- Remaining Days Filter -->
            <div class="filter-section">
              <div class="row g-3">
                <div class="col-md-3">
                  <label class="modern-label">
                    <i class="fas fa-calendar-minus me-1"></i>Min. Kalan Gün
                  </label>
                  <input
                    type="number"
                    class="modern-input"
                    formControlName="remainingDaysMin"
                    placeholder="Minimum gün"
                    min="0">
                </div>
                <div class="col-md-3">
                  <label class="modern-label">
                    <i class="fas fa-calendar-plus me-1"></i>Max. Kalan Gün
                  </label>
                  <input
                    type="number"
                    class="modern-input"
                    formControlName="remainingDaysMax"
                    placeholder="Maximum gün"
                    min="0">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                  <button
                    type="button"
                    class="modern-btn modern-btn-primary w-100"
                    (click)="applyRemainingDaysFilter()">
                    <i class="fas fa-filter me-1"></i>Uygula
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Results Section -->
  <div class="row">
    <div class="col-12">
      <div class="modern-card">
        <div class="modern-card-header">
          <div>
            <h5 class="mb-0 text-primary">
              <i class="fas fa-list me-2"></i>Lisans Listesi
            </h5>
            <small class="text-muted" *ngIf="paginationData">
              Toplam {{ paginationData.totalCount }} kayıt bulundu
            </small>
          </div>
          <div class="d-flex align-items-center gap-2">
            <label class="modern-label mb-0 me-2">Sayfa başına:</label>
            <select
              class="modern-select modern-select-sm"
              style="width: auto;"
              [value]="pageSize"
              (change)="onPageSizeChange(+($any($event.target).value))">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>

        <div class="modern-card-body">
          <!-- Loading -->
          <div *ngIf="isLoading" class="d-flex justify-content-center p-5">
            <app-loading-spinner></app-loading-spinner>
          </div>

          <!-- No Data -->
          <div *ngIf="!isLoading && userLicenses.length === 0" class="text-center p-5 empty-state">
            <div class="empty-state-icon">
              <i class="fas fa-inbox"></i>
            </div>
            <h5 class="empty-state-title">Lisans bulunamadı</h5>
            <p class="empty-state-text">Arama kriterlerinizi değiştirmeyi deneyin.</p>
          </div>

          <!-- Data Table -->
          <div *ngIf="!isLoading && userLicenses.length > 0" class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-building me-2"></i>Şirket
                  </th>
                  <th>
                    <i class="fas fa-user me-2"></i>Kullanıcı
                  </th>
                  <th>
                    <i class="fas fa-box me-2"></i>Paket
                  </th>
                  <th>
                    <i class="fas fa-user-tag me-2"></i>Rol
                  </th>
                  <th>
                    <i class="fas fa-calendar-alt me-2"></i>Başlangıç
                  </th>
                  <th>
                    <i class="fas fa-calendar-times me-2"></i>Bitiş
                  </th>
                  <th>
                    <i class="fas fa-clock me-2"></i>Kalan Gün
                  </th>
                  <th class="text-center">
                    <i class="fas fa-cogs me-2"></i>İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of userLicenses; trackBy: trackByLicenseId" class="zoom-in">
                  <!-- Company Name -->
                  <td>
                    <div class="company-info">
                      <div class="modern-avatar" style="background-color: var(--primary);">
                        {{ item.companyName.charAt(0) }}
                      </div>
                      <span class="fw-bold text-primary">{{ item.companyName }}</span>
                    </div>
                  </td>

                  <!-- User Info -->
                  <td>
                    <div class="user-info">
                      <div class="fw-bold">{{ item.userName }}</div>
                      <small class="text-muted">{{ item.userEmail }}</small>
                    </div>
                  </td>

                  <!-- Package -->
                  <td>
                    <span class="modern-badge modern-badge-info">{{ item.packageName }}</span>
                  </td>

                  <!-- Role -->
                  <td>
                    <span class="modern-badge modern-badge-secondary">{{ item.role }}</span>
                  </td>

                  <!-- Start Date -->
                  <td>
                    <div class="date-info">
                      <i class="fas fa-calendar-check text-success me-1"></i>
                      <small>{{ item.startDate | date:'dd.MM.yyyy' }}</small>
                    </div>
                  </td>

                  <!-- End Date -->
                  <td>
                    <div class="date-info">
                      <i class="fas fa-calendar-times text-danger me-1"></i>
                      <small>{{ item.endDate | date:'dd.MM.yyyy' }}</small>
                    </div>
                  </td>

                  <!-- Remaining Days -->
                  <td>
                    <div class="remaining-days">
                      <span [ngClass]="getRemainingDaysClass(item.remainingDays)" class="modern-badge">
                        {{ item.remainingDays }} gün
                      </span>
                    </div>
                  </td>

                  <!-- Actions -->
                  <td>
                    <div class="action-buttons">
                      <button
                        class="modern-btn modern-btn-info modern-btn-sm"
                        (click)="openExtendDialog(item)"
                        title="Lisansı Uzat">
                        <i class="fas fa-calendar-plus"></i>
                      </button>
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm ms-2"
                        (click)="revokeLicense(item.userLicenseID, item.userName)"
                        title="Lisansı İptal Et">
                        <i class="fas fa-times-circle"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div class="modern-card-footer" *ngIf="paginationData && paginationData.totalPages > 1">
          <div class="pagination-container">
            <nav aria-label="Sayfa navigasyonu">
              <ul class="modern-pagination">
                <!-- Previous Button -->
                <li class="modern-page-item" [class.disabled]="!paginationData.hasPreviousPage">
                  <button
                    class="modern-page-link"
                    (click)="onPageChange(currentPage - 1)"
                    [disabled]="!paginationData.hasPreviousPage">
                    <i class="fas fa-chevron-left"></i>
                  </button>
                </li>

                <!-- Page Numbers -->
                <li
                  *ngFor="let page of getPageNumbers()"
                  class="modern-page-item"
                  [class.active]="page === currentPage">
                  <button class="modern-page-link" (click)="onPageChange(page)">
                    {{ page }}
                  </button>
                </li>

                <!-- Next Button -->
                <li class="modern-page-item" [class.disabled]="!paginationData.hasNextPage">
                  <button
                    class="modern-page-link"
                    (click)="onPageChange(currentPage + 1)"
                    [disabled]="!paginationData.hasNextPage">
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </li>
              </ul>
            </nav>

            <!-- Pagination Info -->
            <div class="pagination-info">
              <small class="text-muted">
                Sayfa {{ paginationData.pageNumber }} / {{ paginationData.totalPages }}
                ({{ paginationData.totalCount }} toplam kayıt)
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>