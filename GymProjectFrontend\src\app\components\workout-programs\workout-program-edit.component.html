<div class="container-fluid">
  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
    <p class="mt-3 text-muted">Program yükleniyor...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading">
    <!-- Sticky Header -->
    <div class="sticky-header">
      <div class="container-fluid">
        <!-- Header -->
        <div class="modern-card mb-4">
          <div class="modern-card-header">
            <div class="d-flex align-items-center">
              <button
                class="modern-btn modern-btn-outline-secondary me-3"
                (click)="goBack()">
                <fa-icon [icon]="faArrowLeft"></fa-icon>
              </button>
              <h4 class="mb-0">Antre<PERSON><PERSON> Programını Düzenle</h4>
            </div>
            <div class="d-flex gap-2">
              <button
                type="button"
                class="modern-btn modern-btn-secondary"
                (click)="goBack()">
                İptal
              </button>
              <button
                type="submit"
                form="programForm"
                class="modern-btn modern-btn-primary"
                [disabled]="isSubmitting">
                <fa-icon [icon]="faSave" class="modern-btn-icon"></fa-icon>
                <span *ngIf="!isSubmitting">Güncelle</span>
                <span *ngIf="isSubmitting">Güncelleniyor...</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="main-content">

    <form id="programForm" [formGroup]="programForm" (ngSubmit)="onSubmit()">
      <!-- Program Basic Info -->
      <div class="modern-card mb-4">
        <div class="modern-card-header">
          <h5 class="mb-0">Program Bilgileri</h5>
        </div>
        <div class="modern-card-body">
          <div class="row g-3">
            <!-- Program Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Program Adı <span class="text-danger">*</span>
                </label>
                <input 
                  type="text" 
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('programName')"
                  formControlName="programName"
                  placeholder="Örn: Başlangıç Kas Yapma Programı">
                <div *ngIf="isFieldInvalid('programName')" class="invalid-feedback">
                  {{getFieldError('programName')}}
                </div>
              </div>
            </div>

            <!-- Experience Level -->
            <div class="col-md-3">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Deneyim Seviyesi <span class="text-danger">*</span>
                </label>
                <select 
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('experienceLevel')"
                  formControlName="experienceLevel">
                  <option value="">Seçiniz</option>
                  <option *ngFor="let level of experienceLevels" [value]="level.value">
                    {{level.label}}
                  </option>
                </select>
                <div *ngIf="isFieldInvalid('experienceLevel')" class="invalid-feedback">
                  {{getFieldError('experienceLevel')}}
                </div>
              </div>
            </div>

            <!-- Target Goal -->
            <div class="col-md-3">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Hedef <span class="text-danger">*</span>
                </label>
                <select 
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('targetGoal')"
                  formControlName="targetGoal">
                  <option value="">Seçiniz</option>
                  <option *ngFor="let goal of targetGoals" [value]="goal.value">
                    {{goal.label}}
                  </option>
                </select>
                <div *ngIf="isFieldInvalid('targetGoal')" class="invalid-feedback">
                  {{getFieldError('targetGoal')}}
                </div>
              </div>
            </div>

            <!-- Description -->
            <div class="col-md-9">
              <div class="modern-form-group">
                <label class="modern-form-label">Açıklama</label>
                <textarea 
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid('description')"
                  formControlName="description"
                  rows="3"
                  placeholder="Program hakkında detaylı bilgi..."></textarea>
                <div *ngIf="isFieldInvalid('description')" class="invalid-feedback">
                  {{getFieldError('description')}}
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

      <!-- Program Days -->
      <div class="modern-card mb-4">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
            Program Günleri ({{days.length}} Gün)
          </h5>
        </div>
        <div class="modern-card-body">
          <div formArrayName="days">
            <div 
              *ngFor="let dayControl of days.controls; let i = index" 
              class="day-card mb-3"
              [formGroupName]="i">
              
              <div class="day-header">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">
                    <span class="day-number">{{i + 1}}. Gün</span>
                    <span *ngIf="dayControl.get('dayName')?.value" class="day-name-preview">
                      - {{dayControl.get('dayName')?.value}}
                    </span>
                  </h6>
                  <div class="d-flex gap-2">
                    <button
                      *ngIf="!dayControl.get('isRestDay')?.value"
                      type="button"
                      class="modern-btn modern-btn-outline-primary modern-btn-sm"
                      (click)="openDayEditModal(i)">
                      <fa-icon [icon]="faEdit"></fa-icon>
                      Düzenle
                    </button>
                    <button
                      type="button"
                      class="modern-btn modern-btn-outline-danger modern-btn-sm"
                      (click)="removeDay(i)"
                      [disabled]="days.length <= 1">
                      <fa-icon [icon]="faMinus"></fa-icon>
                    </button>
                  </div>
                </div>
              </div>

              <div class="day-body">
                <div class="row g-3">
                  <!-- Day Name -->
                  <div class="col-md-6">
                    <div class="modern-form-group">
                      <label class="modern-form-label">
                        Gün Adı <span class="text-danger">*</span>
                      </label>
                      <input 
                        type="text" 
                        class="modern-form-control"
                        [class.is-invalid]="isDayFieldInvalid(i, 'dayName')"
                        formControlName="dayName"
                        placeholder="Örn: Göğüs-Triceps">
                      <div *ngIf="isDayFieldInvalid(i, 'dayName')" class="invalid-feedback">
                        {{getDayFieldError(i, 'dayName')}}
                      </div>
                    </div>
                  </div>

                  <!-- Popular Day Names -->
                  <div class="col-md-4">
                    <div class="modern-form-group">
                      <label class="modern-form-label">Popüler Gün Adları</label>
                      <select 
                        class="modern-form-control"
                        (change)="onPopularDayNameSelect(i, $any($event.target).value)">
                        <option value="">Seçiniz</option>
                        <option *ngFor="let dayName of popularDayNames" [value]="dayName">
                          {{dayName}}
                        </option>
                      </select>
                    </div>
                  </div>

                  <!-- Rest Day -->
                  <div class="col-md-2">
                    <div class="modern-form-group">
                      <label class="modern-form-label">Dinlenme Günü</label>
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          formControlName="isRestDay"
                          (change)="onRestDayChange(i)"
                          [id]="'restDay' + i">
                        <label class="form-check-label" [for]="'restDay' + i">
                          {{dayControl.get('isRestDay')?.value ? 'Evet' : 'Hayır'}}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Exercise Summary -->
                <div class="mt-3">
                  <div *ngIf="!dayControl.get('isRestDay')?.value" class="exercise-summary">
                    <div class="d-flex justify-content-between align-items-center">
                      <span class="text-muted">
                        <fa-icon [icon]="faEdit" class="me-1"></fa-icon>
                        {{dayControl.get('exercises')?.value?.length || 0}} egzersiz tanımlanmış
                      </span>
                      <small class="text-muted">Detayları düzenlemek için "Düzenle" butonuna tıklayın</small>
                    </div>
                  </div>

                  <div *ngIf="dayControl.get('isRestDay')?.value" class="alert alert-warning">
                    <small>Bu gün dinlenme günü olarak işaretlenmiştir</small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div *ngIf="days.length === 0" class="text-center py-4">
            <fa-icon [icon]="faCalendarAlt" class="text-muted mb-3" style="font-size: 2rem;"></fa-icon>
            <h6 class="text-muted">Henüz gün eklenmemiş</h6>
            <p class="text-muted">Programa gün eklemek için "Gün Ekle" butonuna tıklayın</p>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
