﻿namespace Core.Utilities.Paging
{
    // Core/Utilities/Paging/MemberPagingParameters.cs
    public class MemberPagingParameters : PagingParameters
    {
        public int? Gender { get; set; }
        public string Branch { get; set; } = ""; // Default boş string
        public bool? IsActive { get; set; }
        public string SearchText { get; set; } = ""; // Default boş string
        public int[] MembershipTypeIds { get; set; } = new int[0]; // <PERSON><PERSON>lu paket filtreleme için
    }
}
