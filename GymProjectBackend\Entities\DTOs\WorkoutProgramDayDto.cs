using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    public class WorkoutProgramDayDto : IDto
    {
        public int WorkoutProgramDayID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int DayNumber { get; set; }
        public string DayName { get; set; }
        public bool IsRestDay { get; set; }
        public DateTime? CreationDate { get; set; }
        public List<WorkoutProgramExerciseDto> Exercises { get; set; } = new List<WorkoutProgramExerciseDto>();
    }

    public class WorkoutProgramDayAddDto : IDto
    {
        public int DayNumber { get; set; }
        public string DayName { get; set; }
        public bool IsRestDay { get; set; }
        public List<WorkoutProgramExerciseAddDto> Exercises { get; set; } = new List<WorkoutProgramExerciseAddDto>();
    }

    public class WorkoutProgramDayUpdateDto : IDto
    {
        public int? WorkoutProgramDayID { get; set; } // Null ise yeni gün
        public int DayNumber { get; set; }
        public string DayName { get; set; }
        public bool IsRestDay { get; set; }
        public List<WorkoutProgramExerciseUpdateDto> Exercises { get; set; } = new List<WorkoutProgramExerciseUpdateDto>();
    }
}
