using Core.Entities;
using System;

namespace Entities.DTOs
{
    /// <summary>
    /// Silinen salon sahiplerinin listesi için DTO
    /// </summary>
    public class DeletedCompanyUserDto : IDto
    {
        public int CompanyUserID { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string CityName { get; set; }
        public string TownName { get; set; }
        public DateTime? DeletedDate { get; set; }

        // Company bilgileri
        public int? CompanyID { get; set; }
        public string? CompanyName { get; set; }
        public string? CompanyPhone { get; set; }
        public string? CompanyAddress { get; set; }

        // User bilgileri
        public int? UserID { get; set; }

        // İstatistik bilgileri (silme <PERSON>)
        public int TotalMembers { get; set; }
        public bool CanRestore { get; set; }
    }
}
