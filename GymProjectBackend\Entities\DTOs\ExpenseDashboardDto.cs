using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    public class ExpenseDashboardDto : IDto
    {
        /// <summary>
        /// Günlük toplam gider (bugün)
        /// </summary>
        public decimal TotalDailyExpense { get; set; }

        /// <summary>
        /// Seçili ayın toplam gideri
        /// </summary>
        public decimal TotalMonthlyExpense { get; set; }

        /// <summary>
        /// Seçili yılın toplam gideri
        /// </summary>
        public decimal TotalYearlyExpense { get; set; }

        /// <summary>
        /// Seçili ayın gider detayları
        /// </summary>
        public List<ExpenseDto> MonthlyExpenseDetails { get; set; } = new List<ExpenseDto>();

        /// <summary>
        /// Yıllık aylık gider özeti (grafik için)
        /// Key: Ay (1-12), Value: O ayın toplam gideri
        /// </summary>
        public Dictionary<int, decimal> MonthlyExpenseSummary { get; set; } = new Dictionary<int, decimal>();

        /// <summary>
        /// Seçili yıl
        /// </summary>
        public int SelectedYear { get; set; }

        /// <summary>
        /// Seçili ay
        /// </summary>
        public int SelectedMonth { get; set; }

        /// <summary>
        /// Veri alınma tarihi (cache kontrolü için)
        /// </summary>
        public DateTime DataRetrievedAt { get; set; } = DateTime.UtcNow;
    }
}
