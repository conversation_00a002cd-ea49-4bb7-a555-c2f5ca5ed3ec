export interface ExpensePagingParameters {
  pageNumber: number;
  pageSize: number;
  searchText?: string;
  sortBy?: string;
  sortDirection?: string;
  startDate?: Date;
  endDate?: Date;
  expenseType?: string;
  minAmount?: number;
  maxAmount?: number;
  isActive?: boolean;
}

export interface ExpenseFilterState {
  searchText: string;
  startDate: Date | null;
  endDate: Date | null;
  expenseType: string;
  minAmount: number | null;
  maxAmount: number | null;
  selectedYear: number;
  selectedMonth: number;
}

export interface ExpenseSortState {
  sortBy: string;
  sortDirection: 'asc' | 'desc';
}
