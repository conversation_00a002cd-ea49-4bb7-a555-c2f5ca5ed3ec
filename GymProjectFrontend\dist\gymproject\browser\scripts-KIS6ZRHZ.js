/*! jQuery v3.7.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */(function(F,ot){"use strict";typeof module=="object"&&typeof module.exports=="object"?module.exports=F.document?ot(F,!0):function(ke){if(!ke.document)throw new Error("jQuery requires a window with a document");return ot(ke)}:ot(F)})(typeof window<"u"?window:this,function(F,ot){"use strict";var ke=[],Ti=Object.getPrototypeOf,Ue=ke.slice,mt=ke.flat?function(e){return ke.flat.call(e)}:function(e){return ke.concat.apply([],e)},st=ke.push,Ye=ke.indexOf,at={},Ei=at.toString,Mt=at.hasOwnProperty,ln=Mt.toString,Qi=ln.call(Object),K={},B=function(e){return typeof e=="function"&&typeof e.nodeType!="number"&&typeof e.item!="function"},Pe=function(e){return e!=null&&e===e.window},q=F.document,Ki={type:!0,src:!0,nonce:!0,noModule:!0};function Kn(e,n,i){var r,a,c=(i=i||q).createElement("script");if(c.text=e,n)for(r in Ki)(a=n[r]||n.getAttribute&&n.getAttribute(r))&&c.setAttribute(r,a);i.head.appendChild(c).parentNode.removeChild(c)}function cn(e){return e==null?e+"":typeof e=="object"||typeof e=="function"?at[Ei.call(e)]||"object":typeof e}var Gi="3.7.1",Or=/HTML$/i,o=function(e,n){return new o.fn.init(e,n)};function Gn(e){var n=!!e&&"length"in e&&e.length,i=cn(e);return!B(e)&&!Pe(e)&&(i==="array"||n===0||typeof n=="number"&&0<n&&n-1 in e)}function he(e,n){return e.nodeName&&e.nodeName.toLowerCase()===n.toLowerCase()}o.fn=o.prototype={jquery:Gi,constructor:o,length:0,toArray:function(){return Ue.call(this)},get:function(e){return e==null?Ue.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var n=o.merge(this.constructor(),e);return n.prevObject=this,n},each:function(e){return o.each(this,e)},map:function(e){return this.pushStack(o.map(this,function(n,i){return e.call(n,i,n)}))},slice:function(){return this.pushStack(Ue.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(o.grep(this,function(e,n){return(n+1)%2}))},odd:function(){return this.pushStack(o.grep(this,function(e,n){return n%2}))},eq:function(e){var n=this.length,i=+e+(e<0?n:0);return this.pushStack(0<=i&&i<n?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:st,sort:ke.sort,splice:ke.splice},o.extend=o.fn.extend=function(){var e,n,i,r,a,c,u=arguments[0]||{},p=1,h=arguments.length,v=!1;for(typeof u=="boolean"&&(v=u,u=arguments[p]||{},p++),typeof u=="object"||B(u)||(u={}),p===h&&(u=this,p--);p<h;p++)if((e=arguments[p])!=null)for(n in e)r=e[n],n!=="__proto__"&&u!==r&&(v&&r&&(o.isPlainObject(r)||(a=Array.isArray(r)))?(i=u[n],c=a&&!Array.isArray(i)?[]:a||o.isPlainObject(i)?i:{},a=!1,u[n]=o.extend(v,c,r)):r!==void 0&&(u[n]=r));return u},o.extend({expando:"jQuery"+(Gi+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var n,i;return!(!e||Ei.call(e)!=="[object Object]")&&(!(n=Ti(e))||typeof(i=Mt.call(n,"constructor")&&n.constructor)=="function"&&ln.call(i)===Qi)},isEmptyObject:function(e){var n;for(n in e)return!1;return!0},globalEval:function(e,n,i){Kn(e,{nonce:n&&n.nonce},i)},each:function(e,n){var i,r=0;if(Gn(e))for(i=e.length;r<i&&n.call(e[r],r,e[r])!==!1;r++);else for(r in e)if(n.call(e[r],r,e[r])===!1)break;return e},text:function(e){var n,i="",r=0,a=e.nodeType;if(!a)for(;n=e[r++];)i+=o.text(n);return a===1||a===11?e.textContent:a===9?e.documentElement.textContent:a===3||a===4?e.nodeValue:i},makeArray:function(e,n){var i=n||[];return e!=null&&(Gn(Object(e))?o.merge(i,typeof e=="string"?[e]:e):st.call(i,e)),i},inArray:function(e,n,i){return n==null?-1:Ye.call(n,e,i)},isXMLDoc:function(e){var n=e&&e.namespaceURI,i=e&&(e.ownerDocument||e).documentElement;return!Or.test(n||i&&i.nodeName||"HTML")},merge:function(e,n){for(var i=+n.length,r=0,a=e.length;r<i;r++)e[a++]=n[r];return e.length=a,e},grep:function(e,n,i){for(var r=[],a=0,c=e.length,u=!i;a<c;a++)!n(e[a],a)!==u&&r.push(e[a]);return r},map:function(e,n,i){var r,a,c=0,u=[];if(Gn(e))for(r=e.length;c<r;c++)(a=n(e[c],c,i))!=null&&u.push(a);else for(c in e)(a=n(e[c],c,i))!=null&&u.push(a);return mt(u)},guid:1,support:K}),typeof Symbol=="function"&&(o.fn[Symbol.iterator]=ke[Symbol.iterator]),o.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,n){at["[object "+n+"]"]=n.toLowerCase()});var Dr=ke.pop,Ji=ke.sort,Zi=ke.splice,ce="[\\x20\\t\\r\\n\\f]",un=new RegExp("^"+ce+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ce+"+$","g");o.contains=function(e,n){var i=n&&n.parentNode;return e===i||!(!i||i.nodeType!==1||!(e.contains?e.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))};var er=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function Ci(e,n){return n?e==="\0"?"\uFFFD":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}o.escapeSelector=function(e){return(e+"").replace(er,Ci)};var Lt=q,Jn=st;(function(){var e,n,i,r,a,c,u,p,h,v,w=Jn,C=o.expando,b=0,A=0,I=Ee(),V=Ee(),X=Ee(),be=Ee(),Se=function(f,g){return f===g&&(a=!0),0},ft="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",qe="(?:\\\\[\\da-fA-F]{1,6}"+ce+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",te="\\["+ce+"*("+qe+")(?:"+ce+"*([*^$|!~]?=)"+ce+`*(?:'((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)"|(`+qe+"))|)"+ce+"*\\]",on=":("+qe+`)(?:\\((('((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)")|((?:\\\\.|[^\\\\()[\\]]|`+te+")*)|.*)\\)|)",re=new RegExp(ce+"+","g"),xe=new RegExp("^"+ce+"*,"+ce+"*"),Wn=new RegExp("^"+ce+"*([>+~]|"+ce+")"+ce+"*"),qi=new RegExp(ce+"|>"),Ct=new RegExp(on),xn=new RegExp("^"+qe+"$"),At={ID:new RegExp("^#("+qe+")"),CLASS:new RegExp("^\\.("+qe+")"),TAG:new RegExp("^("+qe+"|[*])"),ATTR:new RegExp("^"+te),PSEUDO:new RegExp("^"+on),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ce+"*(even|odd|(([+-]|)(\\d*)n|)"+ce+"*(?:([+-]|)"+ce+"*(\\d+)|))"+ce+"*\\)|)","i"),bool:new RegExp("^(?:"+ft+")$","i"),needsContext:new RegExp("^"+ce+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ce+"*((?:-\\d)?\\d*)"+ce+"*\\)|)(?=[^-]|$)","i")},Rt=/^(?:input|select|textarea|button)$/i,ht=/^h\d$/i,je=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Ne=/[+~]/,_e=new RegExp("\\\\[\\da-fA-F]{1,6}"+ce+"?|\\\\([^\\r\\n\\f])","g"),ze=function(f,g){var _="0x"+f.slice(1)-65536;return g||(_<0?String.fromCharCode(_+65536):String.fromCharCode(_>>10|55296,1023&_|56320))},Wt=function(){zt()},Ve=yi(function(f){return f.disabled===!0&&he(f,"fieldset")},{dir:"parentNode",next:"legend"});try{w.apply(ke=Ue.call(Lt.childNodes),Lt.childNodes),ke[Lt.childNodes.length].nodeType}catch{w={apply:function(g,_){Jn.apply(g,Ue.call(_))},call:function(g){Jn.apply(g,Ue.call(arguments,1))}}}function W(f,g,_,x){var E,j,N,$,D,ae,Y,G=g&&g.ownerDocument,oe=g?g.nodeType:9;if(_=_||[],typeof f!="string"||!f||oe!==1&&oe!==9&&oe!==11)return _;if(!x&&(zt(g),g=g||c,p)){if(oe!==11&&(D=je.exec(f)))if(E=D[1]){if(oe===9){if(!(N=g.getElementById(E)))return _;if(N.id===E)return w.call(_,N),_}else if(G&&(N=G.getElementById(E))&&W.contains(g,N)&&N.id===E)return w.call(_,N),_}else{if(D[2])return w.apply(_,g.getElementsByTagName(f)),_;if((E=D[3])&&g.getElementsByClassName)return w.apply(_,g.getElementsByClassName(E)),_}if(!(be[f+" "]||h&&h.test(f))){if(Y=f,G=g,oe===1&&(qi.test(f)||Wn.test(f))){for((G=Ne.test(f)&&vi(g.parentNode)||g)==g&&K.scope||(($=g.getAttribute("id"))?$=o.escapeSelector($):g.setAttribute("id",$=C)),j=(ae=Tn(f)).length;j--;)ae[j]=($?"#"+$:":scope")+" "+sn(ae[j]);Y=ae.join(",")}try{return w.apply(_,G.querySelectorAll(Y)),_}catch{be(f,!0)}finally{$===C&&g.removeAttribute("id")}}}return Bi(f.replace(un,"$1"),g,_,x)}function Ee(){var f=[];return function g(_,x){return f.push(_+" ")>n.cacheLength&&delete g[f.shift()],g[_+" "]=x}}function Ce(f){return f[C]=!0,f}function me(f){var g=c.createElement("fieldset");try{return!!f(g)}catch{return!1}finally{g.parentNode&&g.parentNode.removeChild(g),g=null}}function kt(f){return function(g){return he(g,"input")&&g.type===f}}function Fe(f){return function(g){return(he(g,"input")||he(g,"button"))&&g.type===f}}function Bn(f){return function(g){return"form"in g?g.parentNode&&g.disabled===!1?"label"in g?"label"in g.parentNode?g.parentNode.disabled===f:g.disabled===f:g.isDisabled===f||g.isDisabled!==!f&&Ve(g)===f:g.disabled===f:"label"in g&&g.disabled===f}}function Bt(f){return Ce(function(g){return g=+g,Ce(function(_,x){for(var E,j=f([],_.length,g),N=j.length;N--;)_[E=j[N]]&&(_[E]=!(x[E]=_[E]))})})}function vi(f){return f&&typeof f.getElementsByTagName<"u"&&f}function zt(f){var g,_=f?f.ownerDocument||f:Lt;return _!=c&&_.nodeType===9&&_.documentElement&&(u=(c=_).documentElement,p=!o.isXMLDoc(c),v=u.matches||u.webkitMatchesSelector||u.msMatchesSelector,u.msMatchesSelector&&Lt!=c&&(g=c.defaultView)&&g.top!==g&&g.addEventListener("unload",Wt),K.getById=me(function(x){return u.appendChild(x).id=o.expando,!c.getElementsByName||!c.getElementsByName(o.expando).length}),K.disconnectedMatch=me(function(x){return v.call(x,"*")}),K.scope=me(function(){return c.querySelectorAll(":scope")}),K.cssHas=me(function(){try{return c.querySelector(":has(*,:jqfake)"),!1}catch{return!0}}),K.getById?(n.filter.ID=function(x){var E=x.replace(_e,ze);return function(j){return j.getAttribute("id")===E}},n.find.ID=function(x,E){if(typeof E.getElementById<"u"&&p){var j=E.getElementById(x);return j?[j]:[]}}):(n.filter.ID=function(x){var E=x.replace(_e,ze);return function(j){var N=typeof j.getAttributeNode<"u"&&j.getAttributeNode("id");return N&&N.value===E}},n.find.ID=function(x,E){if(typeof E.getElementById<"u"&&p){var j,N,$,D=E.getElementById(x);if(D){if((j=D.getAttributeNode("id"))&&j.value===x)return[D];for($=E.getElementsByName(x),N=0;D=$[N++];)if((j=D.getAttributeNode("id"))&&j.value===x)return[D]}return[]}}),n.find.TAG=function(x,E){return typeof E.getElementsByTagName<"u"?E.getElementsByTagName(x):E.querySelectorAll(x)},n.find.CLASS=function(x,E){if(typeof E.getElementsByClassName<"u"&&p)return E.getElementsByClassName(x)},h=[],me(function(x){var E;u.appendChild(x).innerHTML="<a id='"+C+"' href='' disabled='disabled'></a><select id='"+C+"-\r\\' disabled='disabled'><option selected=''></option></select>",x.querySelectorAll("[selected]").length||h.push("\\["+ce+"*(?:value|"+ft+")"),x.querySelectorAll("[id~="+C+"-]").length||h.push("~="),x.querySelectorAll("a#"+C+"+*").length||h.push(".#.+[+~]"),x.querySelectorAll(":checked").length||h.push(":checked"),(E=c.createElement("input")).setAttribute("type","hidden"),x.appendChild(E).setAttribute("name","D"),u.appendChild(x).disabled=!0,x.querySelectorAll(":disabled").length!==2&&h.push(":enabled",":disabled"),(E=c.createElement("input")).setAttribute("name",""),x.appendChild(E),x.querySelectorAll("[name='']").length||h.push("\\["+ce+"*name"+ce+"*="+ce+`*(?:''|"")`)}),K.cssHas||h.push(":has"),h=h.length&&new RegExp(h.join("|")),Se=function(x,E){if(x===E)return a=!0,0;var j=!x.compareDocumentPosition-!E.compareDocumentPosition;return j||(1&(j=(x.ownerDocument||x)==(E.ownerDocument||E)?x.compareDocumentPosition(E):1)||!K.sortDetached&&E.compareDocumentPosition(x)===j?x===c||x.ownerDocument==Lt&&W.contains(Lt,x)?-1:E===c||E.ownerDocument==Lt&&W.contains(Lt,E)?1:r?Ye.call(r,x)-Ye.call(r,E):0:4&j?-1:1)}),c}for(e in W.matches=function(f,g){return W(f,null,null,g)},W.matchesSelector=function(f,g){if(zt(f),p&&!be[g+" "]&&(!h||!h.test(g)))try{var _=v.call(f,g);if(_||K.disconnectedMatch||f.document&&f.document.nodeType!==11)return _}catch{be(g,!0)}return 0<W(g,c,null,[f]).length},W.contains=function(f,g){return(f.ownerDocument||f)!=c&&zt(f),o.contains(f,g)},W.attr=function(f,g){(f.ownerDocument||f)!=c&&zt(f);var _=n.attrHandle[g.toLowerCase()],x=_&&Mt.call(n.attrHandle,g.toLowerCase())?_(f,g,!p):void 0;return x!==void 0?x:f.getAttribute(g)},W.error=function(f){throw new Error("Syntax error, unrecognized expression: "+f)},o.uniqueSort=function(f){var g,_=[],x=0,E=0;if(a=!K.sortStable,r=!K.sortStable&&Ue.call(f,0),Ji.call(f,Se),a){for(;g=f[E++];)g===f[E]&&(x=_.push(E));for(;x--;)Zi.call(f,_[x],1)}return r=null,f},o.fn.uniqueSort=function(){return this.pushStack(o.uniqueSort(Ue.apply(this)))},(n=o.expr={cacheLength:50,createPseudo:Ce,match:At,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(f){return f[1]=f[1].replace(_e,ze),f[3]=(f[3]||f[4]||f[5]||"").replace(_e,ze),f[2]==="~="&&(f[3]=" "+f[3]+" "),f.slice(0,4)},CHILD:function(f){return f[1]=f[1].toLowerCase(),f[1].slice(0,3)==="nth"?(f[3]||W.error(f[0]),f[4]=+(f[4]?f[5]+(f[6]||1):2*(f[3]==="even"||f[3]==="odd")),f[5]=+(f[7]+f[8]||f[3]==="odd")):f[3]&&W.error(f[0]),f},PSEUDO:function(f){var g,_=!f[6]&&f[2];return At.CHILD.test(f[0])?null:(f[3]?f[2]=f[4]||f[5]||"":_&&Ct.test(_)&&(g=Tn(_,!0))&&(g=_.indexOf(")",_.length-g)-_.length)&&(f[0]=f[0].slice(0,g),f[2]=_.slice(0,g)),f.slice(0,3))}},filter:{TAG:function(f){var g=f.replace(_e,ze).toLowerCase();return f==="*"?function(){return!0}:function(_){return he(_,g)}},CLASS:function(f){var g=I[f+" "];return g||(g=new RegExp("(^|"+ce+")"+f+"("+ce+"|$)"))&&I(f,function(_){return g.test(typeof _.className=="string"&&_.className||typeof _.getAttribute<"u"&&_.getAttribute("class")||"")})},ATTR:function(f,g,_){return function(x){var E=W.attr(x,f);return E==null?g==="!=":!g||(E+="",g==="="?E===_:g==="!="?E!==_:g==="^="?_&&E.indexOf(_)===0:g==="*="?_&&-1<E.indexOf(_):g==="$="?_&&E.slice(-_.length)===_:g==="~="?-1<(" "+E.replace(re," ")+" ").indexOf(_):g==="|="&&(E===_||E.slice(0,_.length+1)===_+"-"))}},CHILD:function(f,g,_,x,E){var j=f.slice(0,3)!=="nth",N=f.slice(-4)!=="last",$=g==="of-type";return x===1&&E===0?function(D){return!!D.parentNode}:function(D,ae,Y){var G,oe,z,Oe,Ie,De=j!==N?"nextSibling":"previousSibling",Re=D.parentNode,Ze=$&&D.nodeName.toLowerCase(),pt=!Y&&!$,le=!1;if(Re){if(j){for(;De;){for(z=D;z=z[De];)if($?he(z,Ze):z.nodeType===1)return!1;Ie=De=f==="only"&&!Ie&&"nextSibling"}return!0}if(Ie=[N?Re.firstChild:Re.lastChild],N&&pt){for(le=(Oe=(G=(oe=Re[C]||(Re[C]={}))[f]||[])[0]===b&&G[1])&&G[2],z=Oe&&Re.childNodes[Oe];z=++Oe&&z&&z[De]||(le=Oe=0)||Ie.pop();)if(z.nodeType===1&&++le&&z===D){oe[f]=[b,Oe,le];break}}else if(pt&&(le=Oe=(G=(oe=D[C]||(D[C]={}))[f]||[])[0]===b&&G[1]),le===!1)for(;(z=++Oe&&z&&z[De]||(le=Oe=0)||Ie.pop())&&!(($?he(z,Ze):z.nodeType===1)&&++le&&(pt&&((oe=z[C]||(z[C]={}))[f]=[b,le]),z===D)););return(le-=E)===x||le%x==0&&0<=le/x}}},PSEUDO:function(f,g){var _,x=n.pseudos[f]||n.setFilters[f.toLowerCase()]||W.error("unsupported pseudo: "+f);return x[C]?x(g):1<x.length?(_=[f,f,"",g],n.setFilters.hasOwnProperty(f.toLowerCase())?Ce(function(E,j){for(var N,$=x(E,g),D=$.length;D--;)E[N=Ye.call(E,$[D])]=!(j[N]=$[D])}):function(E){return x(E,0,_)}):x}},pseudos:{not:Ce(function(f){var g=[],_=[],x=bi(f.replace(un,"$1"));return x[C]?Ce(function(E,j,N,$){for(var D,ae=x(E,null,$,[]),Y=E.length;Y--;)(D=ae[Y])&&(E[Y]=!(j[Y]=D))}):function(E,j,N){return g[0]=E,x(g,null,N,_),g[0]=null,!_.pop()}}),has:Ce(function(f){return function(g){return 0<W(f,g).length}}),contains:Ce(function(f){return f=f.replace(_e,ze),function(g){return-1<(g.textContent||o.text(g)).indexOf(f)}}),lang:Ce(function(f){return xn.test(f||"")||W.error("unsupported lang: "+f),f=f.replace(_e,ze).toLowerCase(),function(g){var _;do if(_=p?g.lang:g.getAttribute("xml:lang")||g.getAttribute("lang"))return(_=_.toLowerCase())===f||_.indexOf(f+"-")===0;while((g=g.parentNode)&&g.nodeType===1);return!1}}),target:function(f){var g=F.location&&F.location.hash;return g&&g.slice(1)===f.id},root:function(f){return f===u},focus:function(f){return f===function(){try{return c.activeElement}catch{}}()&&c.hasFocus()&&!!(f.type||f.href||~f.tabIndex)},enabled:Bn(!1),disabled:Bn(!0),checked:function(f){return he(f,"input")&&!!f.checked||he(f,"option")&&!!f.selected},selected:function(f){return f.parentNode&&f.parentNode.selectedIndex,f.selected===!0},empty:function(f){for(f=f.firstChild;f;f=f.nextSibling)if(f.nodeType<6)return!1;return!0},parent:function(f){return!n.pseudos.empty(f)},header:function(f){return ht.test(f.nodeName)},input:function(f){return Rt.test(f.nodeName)},button:function(f){return he(f,"input")&&f.type==="button"||he(f,"button")},text:function(f){var g;return he(f,"input")&&f.type==="text"&&((g=f.getAttribute("type"))==null||g.toLowerCase()==="text")},first:Bt(function(){return[0]}),last:Bt(function(f,g){return[g-1]}),eq:Bt(function(f,g,_){return[_<0?_+g:_]}),even:Bt(function(f,g){for(var _=0;_<g;_+=2)f.push(_);return f}),odd:Bt(function(f,g){for(var _=1;_<g;_+=2)f.push(_);return f}),lt:Bt(function(f,g,_){var x;for(x=_<0?_+g:g<_?g:_;0<=--x;)f.push(x);return f}),gt:Bt(function(f,g,_){for(var x=_<0?_+g:_;++x<g;)f.push(x);return f})}}).pseudos.nth=n.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})n.pseudos[e]=kt(e);for(e in{submit:!0,reset:!0})n.pseudos[e]=Fe(e);function _r(){}function Tn(f,g){var _,x,E,j,N,$,D,ae=V[f+" "];if(ae)return g?0:ae.slice(0);for(N=f,$=[],D=n.preFilter;N;){for(j in _&&!(x=xe.exec(N))||(x&&(N=N.slice(x[0].length)||N),$.push(E=[])),_=!1,(x=Wn.exec(N))&&(_=x.shift(),E.push({value:_,type:x[0].replace(un," ")}),N=N.slice(_.length)),n.filter)!(x=At[j].exec(N))||D[j]&&!(x=D[j](x))||(_=x.shift(),E.push({value:_,type:j,matches:x}),N=N.slice(_.length));if(!_)break}return g?N.length:N?W.error(f):V(f,$).slice(0)}function sn(f){for(var g=0,_=f.length,x="";g<_;g++)x+=f[g].value;return x}function yi(f,g,_){var x=g.dir,E=g.next,j=E||x,N=_&&j==="parentNode",$=A++;return g.first?function(D,ae,Y){for(;D=D[x];)if(D.nodeType===1||N)return f(D,ae,Y);return!1}:function(D,ae,Y){var G,oe,z=[b,$];if(Y){for(;D=D[x];)if((D.nodeType===1||N)&&f(D,ae,Y))return!0}else for(;D=D[x];)if(D.nodeType===1||N)if(oe=D[C]||(D[C]={}),E&&he(D,E))D=D[x]||D;else{if((G=oe[j])&&G[0]===b&&G[1]===$)return z[2]=G[2];if((oe[j]=z)[2]=f(D,ae,Y))return!0}return!1}}function Fi(f){return 1<f.length?function(g,_,x){for(var E=f.length;E--;)if(!f[E](g,_,x))return!1;return!0}:f[0]}function zn(f,g,_,x,E){for(var j,N=[],$=0,D=f.length,ae=g!=null;$<D;$++)(j=f[$])&&(_&&!_(j,x,E)||(N.push(j),ae&&g.push($)));return N}function Ri(f,g,_,x,E,j){return x&&!x[C]&&(x=Ri(x)),E&&!E[C]&&(E=Ri(E,j)),Ce(function(N,$,D,ae){var Y,G,oe,z,Oe=[],Ie=[],De=$.length,Re=N||function(pt,le,Xe){for(var rt=0,_i=le.length;rt<_i;rt++)W(pt,le[rt],Xe);return Xe}(g||"*",D.nodeType?[D]:D,[]),Ze=!f||!N&&g?Re:zn(Re,Oe,f,D,ae);if(_?_(Ze,z=E||(N?f:De||x)?[]:$,D,ae):z=Ze,x)for(Y=zn(z,Ie),x(Y,[],D,ae),G=Y.length;G--;)(oe=Y[G])&&(z[Ie[G]]=!(Ze[Ie[G]]=oe));if(N){if(E||f){if(E){for(Y=[],G=z.length;G--;)(oe=z[G])&&Y.push(Ze[G]=oe);E(null,z=[],Y,ae)}for(G=z.length;G--;)(oe=z[G])&&-1<(Y=E?Ye.call(N,oe):Oe[G])&&(N[Y]=!($[Y]=oe))}}else z=zn(z===$?z.splice(De,z.length):z),E?E(null,$,z,ae):w.apply($,z)})}function Wi(f){for(var g,_,x,E=f.length,j=n.relative[f[0].type],N=j||n.relative[" "],$=j?1:0,D=yi(function(G){return G===g},N,!0),ae=yi(function(G){return-1<Ye.call(g,G)},N,!0),Y=[function(G,oe,z){var Oe=!j&&(z||oe!=i)||((g=oe).nodeType?D(G,oe,z):ae(G,oe,z));return g=null,Oe}];$<E;$++)if(_=n.relative[f[$].type])Y=[yi(Fi(Y),_)];else{if((_=n.filter[f[$].type].apply(null,f[$].matches))[C]){for(x=++$;x<E&&!n.relative[f[x].type];x++);return Ri(1<$&&Fi(Y),1<$&&sn(f.slice(0,$-1).concat({value:f[$-2].type===" "?"*":""})).replace(un,"$1"),_,$<x&&Wi(f.slice($,x)),x<E&&Wi(f=f.slice(x)),x<E&&sn(f))}Y.push(_)}return Fi(Y)}function bi(f,g){var _,x,E,j,N,$,D=[],ae=[],Y=X[f+" "];if(!Y){for(g||(g=Tn(f)),_=g.length;_--;)(Y=Wi(g[_]))[C]?D.push(Y):ae.push(Y);(Y=X(f,(x=ae,j=0<(E=D).length,N=0<x.length,$=function(G,oe,z,Oe,Ie){var De,Re,Ze,pt=0,le="0",Xe=G&&[],rt=[],_i=i,zi=G||N&&n.find.TAG("*",Ie),Vi=b+=_i==null?1:Math.random()||.1,wr=zi.length;for(Ie&&(i=oe==c||oe||Ie);le!==wr&&(De=zi[le])!=null;le++){if(N&&De){for(Re=0,oe||De.ownerDocument==c||(zt(De),z=!p);Ze=x[Re++];)if(Ze(De,oe||c,z)){w.call(Oe,De);break}Ie&&(b=Vi)}j&&((De=!Ze&&De)&&pt--,G&&Xe.push(De))}if(pt+=le,j&&le!==pt){for(Re=0;Ze=E[Re++];)Ze(Xe,rt,oe,z);if(G){if(0<pt)for(;le--;)Xe[le]||rt[le]||(rt[le]=Dr.call(Oe));rt=zn(rt)}w.apply(Oe,rt),Ie&&!G&&0<rt.length&&1<pt+E.length&&o.uniqueSort(Oe)}return Ie&&(b=Vi,i=_i),Xe},j?Ce($):$))).selector=f}return Y}function Bi(f,g,_,x){var E,j,N,$,D,ae=typeof f=="function"&&f,Y=!x&&Tn(f=ae.selector||f);if(_=_||[],Y.length===1){if(2<(j=Y[0]=Y[0].slice(0)).length&&(N=j[0]).type==="ID"&&g.nodeType===9&&p&&n.relative[j[1].type]){if(!(g=(n.find.ID(N.matches[0].replace(_e,ze),g)||[])[0]))return _;ae&&(g=g.parentNode),f=f.slice(j.shift().value.length)}for(E=At.needsContext.test(f)?0:j.length;E--&&(N=j[E],!n.relative[$=N.type]);)if((D=n.find[$])&&(x=D(N.matches[0].replace(_e,ze),Ne.test(j[0].type)&&vi(g.parentNode)||g))){if(j.splice(E,1),!(f=x.length&&sn(j)))return w.apply(_,x),_;break}}return(ae||bi(f,Y))(x,g,!p,_,!g||Ne.test(f)&&vi(g.parentNode)||g),_}_r.prototype=n.filters=n.pseudos,n.setFilters=new _r,K.sortStable=C.split("").sort(Se).join("")===C,zt(),K.sortDetached=me(function(f){return 1&f.compareDocumentPosition(c.createElement("fieldset"))}),o.find=W,o.expr[":"]=o.expr.pseudos,o.unique=o.uniqueSort,W.compile=bi,W.select=Bi,W.setDocument=zt,W.tokenize=Tn,W.escape=o.escapeSelector,W.getText=o.text,W.isXML=o.isXMLDoc,W.selectors=o.expr,W.support=o.support,W.uniqueSort=o.uniqueSort})();var S=function(e,n,i){for(var r=[],a=i!==void 0;(e=e[n])&&e.nodeType!==9;)if(e.nodeType===1){if(a&&o(e).is(i))break;r.push(e)}return r},Zn=function(e,n){for(var i=[];e;e=e.nextSibling)e.nodeType===1&&e!==n&&i.push(e);return i},Ai=o.expr.match.needsContext,ei=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function lt(e,n,i){return B(n)?o.grep(e,function(r,a){return!!n.call(r,a,r)!==i}):n.nodeType?o.grep(e,function(r){return r===n!==i}):typeof n!="string"?o.grep(e,function(r){return-1<Ye.call(n,r)!==i}):o.filter(n,e,i)}o.filter=function(e,n,i){var r=n[0];return i&&(e=":not("+e+")"),n.length===1&&r.nodeType===1?o.find.matchesSelector(r,e)?[r]:[]:o.find.matches(e,o.grep(n,function(a){return a.nodeType===1}))},o.fn.extend({find:function(e){var n,i,r=this.length,a=this;if(typeof e!="string")return this.pushStack(o(e).filter(function(){for(n=0;n<r;n++)if(o.contains(a[n],this))return!0}));for(i=this.pushStack([]),n=0;n<r;n++)o.find(e,a[n],i);return 1<r?o.uniqueSort(i):i},filter:function(e){return this.pushStack(lt(this,e||[],!1))},not:function(e){return this.pushStack(lt(this,e||[],!0))},is:function(e){return!!lt(this,typeof e=="string"&&Ai.test(e)?o(e):e||[],!1).length}});var dn,ct=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(o.fn.init=function(e,n,i){var r,a;if(!e)return this;if(i=i||dn,typeof e=="string"){if(!(r=e[0]==="<"&&e[e.length-1]===">"&&3<=e.length?[null,e,null]:ct.exec(e))||!r[1]&&n)return!n||n.jquery?(n||i).find(e):this.constructor(n).find(e);if(r[1]){if(n=n instanceof o?n[0]:n,o.merge(this,o.parseHTML(r[1],n&&n.nodeType?n.ownerDocument||n:q,!0)),ei.test(r[1])&&o.isPlainObject(n))for(r in n)B(this[r])?this[r](n[r]):this.attr(r,n[r]);return this}return(a=q.getElementById(r[2]))&&(this[0]=a,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):B(e)?i.ready!==void 0?i.ready(e):e(o):o.makeArray(e,this)}).prototype=o.fn,dn=o(q);var ki=/^(?:parents|prev(?:Until|All))/,R={children:!0,contents:!0,next:!0,prev:!0};function An(e,n){for(;(e=e[n])&&e.nodeType!==1;);return e}o.fn.extend({has:function(e){var n=o(e,this),i=n.length;return this.filter(function(){for(var r=0;r<i;r++)if(o.contains(this,n[r]))return!0})},closest:function(e,n){var i,r=0,a=this.length,c=[],u=typeof e!="string"&&o(e);if(!Ai.test(e)){for(;r<a;r++)for(i=this[r];i&&i!==n;i=i.parentNode)if(i.nodeType<11&&(u?-1<u.index(i):i.nodeType===1&&o.find.matchesSelector(i,e))){c.push(i);break}}return this.pushStack(1<c.length?o.uniqueSort(c):c)},index:function(e){return e?typeof e=="string"?Ye.call(o(e),this[0]):Ye.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,n){return this.pushStack(o.uniqueSort(o.merge(this.get(),o(e,n))))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}}),o.each({parent:function(e){var n=e.parentNode;return n&&n.nodeType!==11?n:null},parents:function(e){return S(e,"parentNode")},parentsUntil:function(e,n,i){return S(e,"parentNode",i)},next:function(e){return An(e,"nextSibling")},prev:function(e){return An(e,"previousSibling")},nextAll:function(e){return S(e,"nextSibling")},prevAll:function(e){return S(e,"previousSibling")},nextUntil:function(e,n,i){return S(e,"nextSibling",i)},prevUntil:function(e,n,i){return S(e,"previousSibling",i)},siblings:function(e){return Zn((e.parentNode||{}).firstChild,e)},children:function(e){return Zn(e.firstChild)},contents:function(e){return e.contentDocument!=null&&Ti(e.contentDocument)?e.contentDocument:(he(e,"template")&&(e=e.content||e),o.merge([],e.childNodes))}},function(e,n){o.fn[e]=function(i,r){var a=o.map(this,n,i);return e.slice(-5)!=="Until"&&(r=i),r&&typeof r=="string"&&(a=o.filter(r,a)),1<this.length&&(R[e]||o.uniqueSort(a),ki.test(e)&&a.reverse()),this.pushStack(a)}});var ut=/[^\x20\t\r\n\f]+/g;function fn(e){return e}function ti(e){throw e}function hn(e,n,i,r){var a;try{e&&B(a=e.promise)?a.call(e).done(n).fail(i):e&&B(a=e.then)?a.call(e,n,i):n.apply(void 0,[e].slice(r))}catch(c){i.apply(void 0,[c])}}o.Callbacks=function(e){var n,i;e=typeof e=="string"?(n=e,i={},o.each(n.match(ut)||[],function(b,A){i[A]=!0}),i):o.extend({},e);var r,a,c,u,p=[],h=[],v=-1,w=function(){for(u=u||e.once,c=r=!0;h.length;v=-1)for(a=h.shift();++v<p.length;)p[v].apply(a[0],a[1])===!1&&e.stopOnFalse&&(v=p.length,a=!1);e.memory||(a=!1),r=!1,u&&(p=a?[]:"")},C={add:function(){return p&&(a&&!r&&(v=p.length-1,h.push(a)),function b(A){o.each(A,function(I,V){B(V)?e.unique&&C.has(V)||p.push(V):V&&V.length&&cn(V)!=="string"&&b(V)})}(arguments),a&&!r&&w()),this},remove:function(){return o.each(arguments,function(b,A){for(var I;-1<(I=o.inArray(A,p,I));)p.splice(I,1),I<=v&&v--}),this},has:function(b){return b?-1<o.inArray(b,p):0<p.length},empty:function(){return p&&(p=[]),this},disable:function(){return u=h=[],p=a="",this},disabled:function(){return!p},lock:function(){return u=h=[],a||r||(p=a=""),this},locked:function(){return!!u},fireWith:function(b,A){return u||(A=[b,(A=A||[]).slice?A.slice():A],h.push(A),r||w()),this},fire:function(){return C.fireWith(this,arguments),this},fired:function(){return!!c}};return C},o.extend({Deferred:function(e){var n=[["notify","progress",o.Callbacks("memory"),o.Callbacks("memory"),2],["resolve","done",o.Callbacks("once memory"),o.Callbacks("once memory"),0,"resolved"],["reject","fail",o.Callbacks("once memory"),o.Callbacks("once memory"),1,"rejected"]],i="pending",r={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},catch:function(c){return r.then(null,c)},pipe:function(){var c=arguments;return o.Deferred(function(u){o.each(n,function(p,h){var v=B(c[h[4]])&&c[h[4]];a[h[1]](function(){var w=v&&v.apply(this,arguments);w&&B(w.promise)?w.promise().progress(u.notify).done(u.resolve).fail(u.reject):u[h[0]+"With"](this,v?[w]:arguments)})}),c=null}).promise()},then:function(c,u,p){var h=0;function v(w,C,b,A){return function(){var I=this,V=arguments,X=function(){var Se,ft;if(!(w<h)){if((Se=b.apply(I,V))===C.promise())throw new TypeError("Thenable self-resolution");ft=Se&&(typeof Se=="object"||typeof Se=="function")&&Se.then,B(ft)?A?ft.call(Se,v(h,C,fn,A),v(h,C,ti,A)):(h++,ft.call(Se,v(h,C,fn,A),v(h,C,ti,A),v(h,C,fn,C.notifyWith))):(b!==fn&&(I=void 0,V=[Se]),(A||C.resolveWith)(I,V))}},be=A?X:function(){try{X()}catch(Se){o.Deferred.exceptionHook&&o.Deferred.exceptionHook(Se,be.error),h<=w+1&&(b!==ti&&(I=void 0,V=[Se]),C.rejectWith(I,V))}};w?be():(o.Deferred.getErrorHook?be.error=o.Deferred.getErrorHook():o.Deferred.getStackHook&&(be.error=o.Deferred.getStackHook()),F.setTimeout(be))}}return o.Deferred(function(w){n[0][3].add(v(0,w,B(p)?p:fn,w.notifyWith)),n[1][3].add(v(0,w,B(c)?c:fn)),n[2][3].add(v(0,w,B(u)?u:ti))}).promise()},promise:function(c){return c!=null?o.extend(c,r):r}},a={};return o.each(n,function(c,u){var p=u[2],h=u[5];r[u[1]]=p.add,h&&p.add(function(){i=h},n[3-c][2].disable,n[3-c][3].disable,n[0][2].lock,n[0][3].lock),p.add(u[3].fire),a[u[0]]=function(){return a[u[0]+"With"](this===a?void 0:this,arguments),this},a[u[0]+"With"]=p.fireWith}),r.promise(a),e&&e.call(a,a),a},when:function(e){var n=arguments.length,i=n,r=Array(i),a=Ue.call(arguments),c=o.Deferred(),u=function(p){return function(h){r[p]=this,a[p]=1<arguments.length?Ue.call(arguments):h,--n||c.resolveWith(r,a)}};if(n<=1&&(hn(e,c.done(u(i)).resolve,c.reject,!n),c.state()==="pending"||B(a[i]&&a[i].then)))return c.then();for(;i--;)hn(a[i],u(i),c.reject);return c.promise()}});var tr=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;o.Deferred.exceptionHook=function(e,n){F.console&&F.console.warn&&e&&tr.test(e.name)&&F.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},o.readyException=function(e){F.setTimeout(function(){throw e})};var Ut=o.Deferred();function jt(){q.removeEventListener("DOMContentLoaded",jt),F.removeEventListener("load",jt),o.ready()}o.fn.ready=function(e){return Ut.then(e).catch(function(n){o.readyException(n)}),this},o.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--o.readyWait:o.isReady)||(o.isReady=!0)!==e&&0<--o.readyWait||Ut.resolveWith(q,[o])}}),o.ready.then=Ut.then,q.readyState==="complete"||q.readyState!=="loading"&&!q.documentElement.doScroll?F.setTimeout(o.ready):(q.addEventListener("DOMContentLoaded",jt),F.addEventListener("load",jt));var Nt=function(e,n,i,r,a,c,u){var p=0,h=e.length,v=i==null;if(cn(i)==="object")for(p in a=!0,i)Nt(e,n,p,i[p],!0,c,u);else if(r!==void 0&&(a=!0,B(r)||(u=!0),v&&(u?(n.call(e,r),n=null):(v=n,n=function(w,C,b){return v.call(o(w),b)})),n))for(;p<h;p++)n(e[p],i,u?r:r.call(e[p],p,n(e[p],i)));return a?e:v?n.call(e):h?n(e[0],i):c},Lr=/^-ms-/,jr=/-([a-z])/g;function Nr(e,n){return n.toUpperCase()}function vt(e){return e.replace(Lr,"ms-").replace(jr,Nr)}var kn=function(e){return e.nodeType===1||e.nodeType===9||!+e.nodeType};function Sn(){this.expando=o.expando+Sn.uid++}Sn.uid=1,Sn.prototype={cache:function(e){var n=e[this.expando];return n||(n={},kn(e)&&(e.nodeType?e[this.expando]=n:Object.defineProperty(e,this.expando,{value:n,configurable:!0}))),n},set:function(e,n,i){var r,a=this.cache(e);if(typeof n=="string")a[vt(n)]=i;else for(r in n)a[vt(r)]=n[r];return a},get:function(e,n){return n===void 0?this.cache(e):e[this.expando]&&e[this.expando][vt(n)]},access:function(e,n,i){return n===void 0||n&&typeof n=="string"&&i===void 0?this.get(e,n):(this.set(e,n,i),i!==void 0?i:n)},remove:function(e,n){var i,r=e[this.expando];if(r!==void 0){if(n!==void 0)for(i=(n=Array.isArray(n)?n.map(vt):(n=vt(n))in r?[n]:n.match(ut)||[]).length;i--;)delete r[n[i]];(n===void 0||o.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var n=e[this.expando];return n!==void 0&&!o.isEmptyObject(n)}};var H=new Sn,Te=new Sn,nr=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Pr=/[A-Z]/g;function ir(e,n,i){var r,a;if(i===void 0&&e.nodeType===1)if(r="data-"+n.replace(Pr,"-$&").toLowerCase(),typeof(i=e.getAttribute(r))=="string"){try{i=(a=i)==="true"||a!=="false"&&(a==="null"?null:a===+a+""?+a:nr.test(a)?JSON.parse(a):a)}catch{}Te.set(e,n,i)}else i=void 0;return i}o.extend({hasData:function(e){return Te.hasData(e)||H.hasData(e)},data:function(e,n,i){return Te.access(e,n,i)},removeData:function(e,n){Te.remove(e,n)},_data:function(e,n,i){return H.access(e,n,i)},_removeData:function(e,n){H.remove(e,n)}}),o.fn.extend({data:function(e,n){var i,r,a,c=this[0],u=c&&c.attributes;if(e===void 0){if(this.length&&(a=Te.get(c),c.nodeType===1&&!H.get(c,"hasDataAttrs"))){for(i=u.length;i--;)u[i]&&(r=u[i].name).indexOf("data-")===0&&(r=vt(r.slice(5)),ir(c,r,a[r]));H.set(c,"hasDataAttrs",!0)}return a}return typeof e=="object"?this.each(function(){Te.set(this,e)}):Nt(this,function(p){var h;if(c&&p===void 0)return(h=Te.get(c,e))!==void 0||(h=ir(c,e))!==void 0?h:void 0;this.each(function(){Te.set(this,e,p)})},null,n,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){Te.remove(this,e)})}}),o.extend({queue:function(e,n,i){var r;if(e)return n=(n||"fx")+"queue",r=H.get(e,n),i&&(!r||Array.isArray(i)?r=H.access(e,n,o.makeArray(i)):r.push(i)),r||[]},dequeue:function(e,n){n=n||"fx";var i=o.queue(e,n),r=i.length,a=i.shift(),c=o._queueHooks(e,n);a==="inprogress"&&(a=i.shift(),r--),a&&(n==="fx"&&i.unshift("inprogress"),delete c.stop,a.call(e,function(){o.dequeue(e,n)},c)),!r&&c&&c.empty.fire()},_queueHooks:function(e,n){var i=n+"queueHooks";return H.get(e,i)||H.access(e,i,{empty:o.Callbacks("once memory").add(function(){H.remove(e,[n+"queue",i])})})}}),o.fn.extend({queue:function(e,n){var i=2;return typeof e!="string"&&(n=e,e="fx",i--),arguments.length<i?o.queue(this[0],e):n===void 0?this:this.each(function(){var r=o.queue(this,e,n);o._queueHooks(this,e),e==="fx"&&r[0]!=="inprogress"&&o.dequeue(this,e)})},dequeue:function(e){return this.each(function(){o.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,n){var i,r=1,a=o.Deferred(),c=this,u=this.length,p=function(){--r||a.resolveWith(c,[c])};for(typeof e!="string"&&(n=e,e=void 0),e=e||"fx";u--;)(i=H.get(c[u],e+"queueHooks"))&&i.empty&&(r++,i.empty.add(p));return p(),a.promise(n)}});var pn=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,yt=new RegExp("^(?:([+-])=|)("+pn+")([a-z%]*)$","i"),Qe=["Top","Right","Bottom","Left"],bt=q.documentElement,gn=function(e){return o.contains(e.ownerDocument,e)},Si={composed:!0};bt.getRootNode&&(gn=function(e){return o.contains(e.ownerDocument,e)||e.getRootNode(Si)===e.ownerDocument});var ni=function(e,n){return(e=n||e).style.display==="none"||e.style.display===""&&gn(e)&&o.css(e,"display")==="none"};function rr(e,n,i,r){var a,c,u=20,p=r?function(){return r.cur()}:function(){return o.css(e,n,"")},h=p(),v=i&&i[3]||(o.cssNumber[n]?"":"px"),w=e.nodeType&&(o.cssNumber[n]||v!=="px"&&+h)&&yt.exec(o.css(e,n));if(w&&w[3]!==v){for(h/=2,v=v||w[3],w=+h||1;u--;)o.style(e,n,w+v),(1-c)*(1-(c=p()/h||.5))<=0&&(u=0),w/=c;w*=2,o.style(e,n,w+v),i=i||[]}return i&&(w=+w||+h||0,a=i[1]?w+(i[1]+1)*i[2]:+i[2],r&&(r.unit=v,r.start=w,r.end=a)),a}var or={};function mn(e,n){for(var i,r,a,c,u,p,h,v=[],w=0,C=e.length;w<C;w++)(r=e[w]).style&&(i=r.style.display,n?(i==="none"&&(v[w]=H.get(r,"display")||null,v[w]||(r.style.display="")),r.style.display===""&&ni(r)&&(v[w]=(h=u=c=void 0,u=(a=r).ownerDocument,p=a.nodeName,(h=or[p])||(c=u.body.appendChild(u.createElement(p)),h=o.css(c,"display"),c.parentNode.removeChild(c),h==="none"&&(h="block"),or[p]=h)))):i!=="none"&&(v[w]="none",H.set(r,"display",i)));for(w=0;w<C;w++)v[w]!=null&&(e[w].style.display=v[w]);return e}o.fn.extend({show:function(){return mn(this,!0)},hide:function(){return mn(this)},toggle:function(e){return typeof e=="boolean"?e?this.show():this.hide():this.each(function(){ni(this)?o(this).show():o(this).hide()})}});var Yt,ii,vn=/^(?:checkbox|radio)$/i,On=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Oi=/^$|^module$|\/(?:java|ecma)script/i;Yt=q.createDocumentFragment().appendChild(q.createElement("div")),(ii=q.createElement("input")).setAttribute("type","radio"),ii.setAttribute("checked","checked"),ii.setAttribute("name","t"),Yt.appendChild(ii),K.checkClone=Yt.cloneNode(!0).cloneNode(!0).lastChild.checked,Yt.innerHTML="<textarea>x</textarea>",K.noCloneChecked=!!Yt.cloneNode(!0).lastChild.defaultValue,Yt.innerHTML="<option></option>",K.option=!!Yt.lastChild;var Je={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Ke(e,n){var i;return i=typeof e.getElementsByTagName<"u"?e.getElementsByTagName(n||"*"):typeof e.querySelectorAll<"u"?e.querySelectorAll(n||"*"):[],n===void 0||n&&he(e,n)?o.merge([e],i):i}function Di(e,n){for(var i=0,r=e.length;i<r;i++)H.set(e[i],"globalEval",!n||H.get(n[i],"globalEval"))}Je.tbody=Je.tfoot=Je.colgroup=Je.caption=Je.thead,Je.th=Je.td,K.option||(Je.optgroup=Je.option=[1,"<select multiple='multiple'>","</select>"]);var Mr=/<|&#?\w+;/;function sr(e,n,i,r,a){for(var c,u,p,h,v,w,C=n.createDocumentFragment(),b=[],A=0,I=e.length;A<I;A++)if((c=e[A])||c===0)if(cn(c)==="object")o.merge(b,c.nodeType?[c]:c);else if(Mr.test(c)){for(u=u||C.appendChild(n.createElement("div")),p=(On.exec(c)||["",""])[1].toLowerCase(),h=Je[p]||Je._default,u.innerHTML=h[1]+o.htmlPrefilter(c)+h[2],w=h[0];w--;)u=u.lastChild;o.merge(b,u.childNodes),(u=C.firstChild).textContent=""}else b.push(n.createTextNode(c));for(C.textContent="",A=0;c=b[A++];)if(r&&-1<o.inArray(c,r))a&&a.push(c);else if(v=gn(c),u=Ke(C.appendChild(c),"script"),v&&Di(u),i)for(w=0;c=u[w++];)Oi.test(c.type||"")&&i.push(c);return C}var Qt=/^([^.]*)(?:\.(.+)|)/;function _t(){return!0}function yn(){return!1}function Li(e,n,i,r,a,c){var u,p;if(typeof n=="object"){for(p in typeof i!="string"&&(r=r||i,i=void 0),n)Li(e,p,i,r,n[p],c);return e}if(r==null&&a==null?(a=i,r=i=void 0):a==null&&(typeof i=="string"?(a=r,r=void 0):(a=r,r=i,i=void 0)),a===!1)a=yn;else if(!a)return e;return c===1&&(u=a,(a=function(h){return o().off(h),u.apply(this,arguments)}).guid=u.guid||(u.guid=o.guid++)),e.each(function(){o.event.add(this,n,a,r,i)})}function ri(e,n,i){i?(H.set(e,n,!1),o.event.add(e,n,{namespace:!1,handler:function(r){var a,c=H.get(this,n);if(1&r.isTrigger&&this[n]){if(c)(o.event.special[n]||{}).delegateType&&r.stopPropagation();else if(c=Ue.call(arguments),H.set(this,n,c),this[n](),a=H.get(this,n),H.set(this,n,!1),c!==a)return r.stopImmediatePropagation(),r.preventDefault(),a}else c&&(H.set(this,n,o.event.trigger(c[0],c.slice(1),this)),r.stopPropagation(),r.isImmediatePropagationStopped=_t)}})):H.get(e,n)===void 0&&o.event.add(e,n,_t)}o.event={global:{},add:function(e,n,i,r,a){var c,u,p,h,v,w,C,b,A,I,V,X=H.get(e);if(kn(e))for(i.handler&&(i=(c=i).handler,a=c.selector),a&&o.find.matchesSelector(bt,a),i.guid||(i.guid=o.guid++),(h=X.events)||(h=X.events=Object.create(null)),(u=X.handle)||(u=X.handle=function(be){return typeof o<"u"&&o.event.triggered!==be.type?o.event.dispatch.apply(e,arguments):void 0}),v=(n=(n||"").match(ut)||[""]).length;v--;)A=V=(p=Qt.exec(n[v])||[])[1],I=(p[2]||"").split(".").sort(),A&&(C=o.event.special[A]||{},A=(a?C.delegateType:C.bindType)||A,C=o.event.special[A]||{},w=o.extend({type:A,origType:V,data:r,handler:i,guid:i.guid,selector:a,needsContext:a&&o.expr.match.needsContext.test(a),namespace:I.join(".")},c),(b=h[A])||((b=h[A]=[]).delegateCount=0,C.setup&&C.setup.call(e,r,I,u)!==!1||e.addEventListener&&e.addEventListener(A,u)),C.add&&(C.add.call(e,w),w.handler.guid||(w.handler.guid=i.guid)),a?b.splice(b.delegateCount++,0,w):b.push(w),o.event.global[A]=!0)},remove:function(e,n,i,r,a){var c,u,p,h,v,w,C,b,A,I,V,X=H.hasData(e)&&H.get(e);if(X&&(h=X.events)){for(v=(n=(n||"").match(ut)||[""]).length;v--;)if(A=V=(p=Qt.exec(n[v])||[])[1],I=(p[2]||"").split(".").sort(),A){for(C=o.event.special[A]||{},b=h[A=(r?C.delegateType:C.bindType)||A]||[],p=p[2]&&new RegExp("(^|\\.)"+I.join("\\.(?:.*\\.|)")+"(\\.|$)"),u=c=b.length;c--;)w=b[c],!a&&V!==w.origType||i&&i.guid!==w.guid||p&&!p.test(w.namespace)||r&&r!==w.selector&&(r!=="**"||!w.selector)||(b.splice(c,1),w.selector&&b.delegateCount--,C.remove&&C.remove.call(e,w));u&&!b.length&&(C.teardown&&C.teardown.call(e,I,X.handle)!==!1||o.removeEvent(e,A,X.handle),delete h[A])}else for(A in h)o.event.remove(e,A+n[v],i,r,!0);o.isEmptyObject(h)&&H.remove(e,"handle events")}},dispatch:function(e){var n,i,r,a,c,u,p=new Array(arguments.length),h=o.event.fix(e),v=(H.get(this,"events")||Object.create(null))[h.type]||[],w=o.event.special[h.type]||{};for(p[0]=h,n=1;n<arguments.length;n++)p[n]=arguments[n];if(h.delegateTarget=this,!w.preDispatch||w.preDispatch.call(this,h)!==!1){for(u=o.event.handlers.call(this,h,v),n=0;(a=u[n++])&&!h.isPropagationStopped();)for(h.currentTarget=a.elem,i=0;(c=a.handlers[i++])&&!h.isImmediatePropagationStopped();)h.rnamespace&&c.namespace!==!1&&!h.rnamespace.test(c.namespace)||(h.handleObj=c,h.data=c.data,(r=((o.event.special[c.origType]||{}).handle||c.handler).apply(a.elem,p))!==void 0&&(h.result=r)===!1&&(h.preventDefault(),h.stopPropagation()));return w.postDispatch&&w.postDispatch.call(this,h),h.result}},handlers:function(e,n){var i,r,a,c,u,p=[],h=n.delegateCount,v=e.target;if(h&&v.nodeType&&!(e.type==="click"&&1<=e.button)){for(;v!==this;v=v.parentNode||this)if(v.nodeType===1&&(e.type!=="click"||v.disabled!==!0)){for(c=[],u={},i=0;i<h;i++)u[a=(r=n[i]).selector+" "]===void 0&&(u[a]=r.needsContext?-1<o(a,this).index(v):o.find(a,this,null,[v]).length),u[a]&&c.push(r);c.length&&p.push({elem:v,handlers:c})}}return v=this,h<n.length&&p.push({elem:v,handlers:n.slice(h)}),p},addProp:function(e,n){Object.defineProperty(o.Event.prototype,e,{enumerable:!0,configurable:!0,get:B(n)?function(){if(this.originalEvent)return n(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(i){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:i})}})},fix:function(e){return e[o.expando]?e:new o.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var n=this||e;return vn.test(n.type)&&n.click&&he(n,"input")&&ri(n,"click",!0),!1},trigger:function(e){var n=this||e;return vn.test(n.type)&&n.click&&he(n,"input")&&ri(n,"click"),!0},_default:function(e){var n=e.target;return vn.test(n.type)&&n.click&&he(n,"input")&&H.get(n,"click")||he(n,"a")}},beforeunload:{postDispatch:function(e){e.result!==void 0&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},o.removeEvent=function(e,n,i){e.removeEventListener&&e.removeEventListener(n,i)},o.Event=function(e,n){if(!(this instanceof o.Event))return new o.Event(e,n);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===void 0&&e.returnValue===!1?_t:yn,this.target=e.target&&e.target.nodeType===3?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,n&&o.extend(this,n),this.timeStamp=e&&e.timeStamp||Date.now(),this[o.expando]=!0},o.Event.prototype={constructor:o.Event,isDefaultPrevented:yn,isPropagationStopped:yn,isImmediatePropagationStopped:yn,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=_t,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=_t,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=_t,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},o.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},o.event.addProp),o.each({focus:"focusin",blur:"focusout"},function(e,n){function i(r){if(q.documentMode){var a=H.get(this,"handle"),c=o.event.fix(r);c.type=r.type==="focusin"?"focus":"blur",c.isSimulated=!0,a(r),c.target===c.currentTarget&&a(c)}else o.event.simulate(n,r.target,o.event.fix(r))}o.event.special[e]={setup:function(){var r;if(ri(this,e,!0),!q.documentMode)return!1;(r=H.get(this,n))||this.addEventListener(n,i),H.set(this,n,(r||0)+1)},trigger:function(){return ri(this,e),!0},teardown:function(){var r;if(!q.documentMode)return!1;(r=H.get(this,n)-1)?H.set(this,n,r):(this.removeEventListener(n,i),H.remove(this,n))},_default:function(r){return H.get(r.target,e)},delegateType:n},o.event.special[n]={setup:function(){var r=this.ownerDocument||this.document||this,a=q.documentMode?this:r,c=H.get(a,n);c||(q.documentMode?this.addEventListener(n,i):r.addEventListener(e,i,!0)),H.set(a,n,(c||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,a=q.documentMode?this:r,c=H.get(a,n)-1;c?H.set(a,n,c):(q.documentMode?this.removeEventListener(n,i):r.removeEventListener(e,i,!0),H.remove(a,n))}}}),o.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,n){o.event.special[e]={delegateType:n,bindType:n,handle:function(i){var r,a=i.relatedTarget,c=i.handleObj;return a&&(a===this||o.contains(this,a))||(i.type=c.origType,r=c.handler.apply(this,arguments),i.type=n),r}}}),o.fn.extend({on:function(e,n,i,r){return Li(this,e,n,i,r)},one:function(e,n,i,r){return Li(this,e,n,i,r,1)},off:function(e,n,i){var r,a;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,o(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if(typeof e=="object"){for(a in e)this.off(a,n,e[a]);return this}return n!==!1&&typeof n!="function"||(i=n,n=void 0),i===!1&&(i=yn),this.each(function(){o.event.remove(this,e,i,n)})}});var Ir=/<script|<style|<link/i,$r=/checked\s*(?:[^=]|=\s*.checked.)/i,ji=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Kt(e,n){return he(e,"table")&&he(n.nodeType!==11?n:n.firstChild,"tr")&&o(e).children("tbody")[0]||e}function oi(e){return e.type=(e.getAttribute("type")!==null)+"/"+e.type,e}function Hr(e){return(e.type||"").slice(0,5)==="true/"?e.type=e.type.slice(5):e.removeAttribute("type"),e}function si(e,n){var i,r,a,c,u,p;if(n.nodeType===1){if(H.hasData(e)&&(p=H.get(e).events))for(a in H.remove(n,"handle events"),p)for(i=0,r=p[a].length;i<r;i++)o.event.add(n,a,p[a][i]);Te.hasData(e)&&(c=Te.access(e),u=o.extend({},c),Te.set(n,u))}}function bn(e,n,i,r){n=mt(n);var a,c,u,p,h,v,w=0,C=e.length,b=C-1,A=n[0],I=B(A);if(I||1<C&&typeof A=="string"&&!K.checkClone&&$r.test(A))return e.each(function(V){var X=e.eq(V);I&&(n[0]=A.call(this,V,X.html())),bn(X,n,i,r)});if(C&&(c=(a=sr(n,e[0].ownerDocument,!1,e,r)).firstChild,a.childNodes.length===1&&(a=c),c||r)){for(p=(u=o.map(Ke(a,"script"),oi)).length;w<C;w++)h=a,w!==b&&(h=o.clone(h,!0,!0),p&&o.merge(u,Ke(h,"script"))),i.call(e[w],h,w);if(p)for(v=u[u.length-1].ownerDocument,o.map(u,Hr),w=0;w<p;w++)h=u[w],Oi.test(h.type||"")&&!H.access(h,"globalEval")&&o.contains(v,h)&&(h.src&&(h.type||"").toLowerCase()!=="module"?o._evalUrl&&!h.noModule&&o._evalUrl(h.src,{nonce:h.nonce||h.getAttribute("nonce")},v):Kn(h.textContent.replace(ji,""),h,v))}return e}function ar(e,n,i){for(var r,a=n?o.filter(n,e):e,c=0;(r=a[c])!=null;c++)i||r.nodeType!==1||o.cleanData(Ke(r)),r.parentNode&&(i&&gn(r)&&Di(Ke(r,"script")),r.parentNode.removeChild(r));return e}o.extend({htmlPrefilter:function(e){return e},clone:function(e,n,i){var r,a,c,u,p,h,v,w=e.cloneNode(!0),C=gn(e);if(!(K.noCloneChecked||e.nodeType!==1&&e.nodeType!==11||o.isXMLDoc(e)))for(u=Ke(w),r=0,a=(c=Ke(e)).length;r<a;r++)p=c[r],h=u[r],(v=h.nodeName.toLowerCase())==="input"&&vn.test(p.type)?h.checked=p.checked:v!=="input"&&v!=="textarea"||(h.defaultValue=p.defaultValue);if(n)if(i)for(c=c||Ke(e),u=u||Ke(w),r=0,a=c.length;r<a;r++)si(c[r],u[r]);else si(e,w);return 0<(u=Ke(w,"script")).length&&Di(u,!C&&Ke(e,"script")),w},cleanData:function(e){for(var n,i,r,a=o.event.special,c=0;(i=e[c])!==void 0;c++)if(kn(i)){if(n=i[H.expando]){if(n.events)for(r in n.events)a[r]?o.event.remove(i,r):o.removeEvent(i,r,n.handle);i[H.expando]=void 0}i[Te.expando]&&(i[Te.expando]=void 0)}}}),o.fn.extend({detach:function(e){return ar(this,e,!0)},remove:function(e){return ar(this,e)},text:function(e){return Nt(this,function(n){return n===void 0?o.text(this):this.empty().each(function(){this.nodeType!==1&&this.nodeType!==11&&this.nodeType!==9||(this.textContent=n)})},null,e,arguments.length)},append:function(){return bn(this,arguments,function(e){this.nodeType!==1&&this.nodeType!==11&&this.nodeType!==9||Kt(this,e).appendChild(e)})},prepend:function(){return bn(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var n=Kt(this,e);n.insertBefore(e,n.firstChild)}})},before:function(){return bn(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return bn(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,n=0;(e=this[n])!=null;n++)e.nodeType===1&&(o.cleanData(Ke(e,!1)),e.textContent="");return this},clone:function(e,n){return e=e!=null&&e,n=n??e,this.map(function(){return o.clone(this,e,n)})},html:function(e){return Nt(this,function(n){var i=this[0]||{},r=0,a=this.length;if(n===void 0&&i.nodeType===1)return i.innerHTML;if(typeof n=="string"&&!Ir.test(n)&&!Je[(On.exec(n)||["",""])[1].toLowerCase()]){n=o.htmlPrefilter(n);try{for(;r<a;r++)(i=this[r]||{}).nodeType===1&&(o.cleanData(Ke(i,!1)),i.innerHTML=n);i=0}catch{}}i&&this.empty().append(n)},null,e,arguments.length)},replaceWith:function(){var e=[];return bn(this,arguments,function(n){var i=this.parentNode;o.inArray(this,e)<0&&(o.cleanData(Ke(this)),i&&i.replaceChild(n,this))},e)}}),o.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,n){o.fn[e]=function(i){for(var r,a=[],c=o(i),u=c.length-1,p=0;p<=u;p++)r=p===u?this:this.clone(!0),o(c[p])[n](r),st.apply(a,r.get());return this.pushStack(a)}});var It=new RegExp("^("+pn+")(?!px)[a-z%]+$","i"),Me=/^--/,$e=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=F),n.getComputedStyle(e)},Ge=function(e,n,i){var r,a,c={};for(a in n)c[a]=e.style[a],e.style[a]=n[a];for(a in r=i.call(e),n)e.style[a]=c[a];return r},Be=new RegExp(Qe.join("|"),"i");function $t(e,n,i){var r,a,c,u,p=Me.test(n),h=e.style;return(i=i||$e(e))&&(u=i.getPropertyValue(n)||i[n],p&&u&&(u=u.replace(un,"$1")||void 0),u!==""||gn(e)||(u=o.style(e,n)),!K.pixelBoxStyles()&&It.test(u)&&Be.test(n)&&(r=h.width,a=h.minWidth,c=h.maxWidth,h.minWidth=h.maxWidth=h.width=u,u=i.width,h.width=r,h.minWidth=a,h.maxWidth=c)),u!==void 0?u+"":u}function Gt(e,n){return{get:function(){if(!e())return(this.get=n).apply(this,arguments);delete this.get}}}(function(){function e(){if(v){h.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",v.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",bt.appendChild(h).appendChild(v);var w=F.getComputedStyle(v);i=w.top!=="1%",p=n(w.marginLeft)===12,v.style.right="60%",c=n(w.right)===36,r=n(w.width)===36,v.style.position="absolute",a=n(v.offsetWidth/3)===12,bt.removeChild(h),v=null}}function n(w){return Math.round(parseFloat(w))}var i,r,a,c,u,p,h=q.createElement("div"),v=q.createElement("div");v.style&&(v.style.backgroundClip="content-box",v.cloneNode(!0).style.backgroundClip="",K.clearCloneStyle=v.style.backgroundClip==="content-box",o.extend(K,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),c},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),p},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var w,C,b,A;return u==null&&(w=q.createElement("table"),C=q.createElement("tr"),b=q.createElement("div"),w.style.cssText="position:absolute;left:-11111px;border-collapse:separate",C.style.cssText="box-sizing:content-box;border:1px solid",C.style.height="1px",b.style.height="9px",b.style.display="block",bt.appendChild(w).appendChild(C).appendChild(b),A=F.getComputedStyle(C),u=parseInt(A.height,10)+parseInt(A.borderTopWidth,10)+parseInt(A.borderBottomWidth,10)===C.offsetHeight,bt.removeChild(w)),u}}))})();var Ht=["Webkit","Moz","ms"],Jt=q.createElement("div").style,Ni={};function Dn(e){var n=o.cssProps[e]||Ni[e];return n||(e in Jt?e:Ni[e]=function(i){for(var r=i[0].toUpperCase()+i.slice(1),a=Ht.length;a--;)if((i=Ht[a]+r)in Jt)return i}(e)||e)}var _n=/^(none|table(?!-c[ea]).+)/,lr={position:"absolute",visibility:"hidden",display:"block"},ai={letterSpacing:"0",fontWeight:"400"};function li(e,n,i){var r=yt.exec(n);return r?Math.max(0,r[2]-(i||0))+(r[3]||"px"):n}function ci(e,n,i,r,a,c){var u=n==="width"?1:0,p=0,h=0,v=0;if(i===(r?"border":"content"))return 0;for(;u<4;u+=2)i==="margin"&&(v+=o.css(e,i+Qe[u],!0,a)),r?(i==="content"&&(h-=o.css(e,"padding"+Qe[u],!0,a)),i!=="margin"&&(h-=o.css(e,"border"+Qe[u]+"Width",!0,a))):(h+=o.css(e,"padding"+Qe[u],!0,a),i!=="padding"?h+=o.css(e,"border"+Qe[u]+"Width",!0,a):p+=o.css(e,"border"+Qe[u]+"Width",!0,a));return!r&&0<=c&&(h+=Math.max(0,Math.ceil(e["offset"+n[0].toUpperCase()+n.slice(1)]-c-h-p-.5))||0),h+v}function Pi(e,n,i){var r=$e(e),a=(!K.boxSizingReliable()||i)&&o.css(e,"boxSizing",!1,r)==="border-box",c=a,u=$t(e,n,r),p="offset"+n[0].toUpperCase()+n.slice(1);if(It.test(u)){if(!i)return u;u="auto"}return(!K.boxSizingReliable()&&a||!K.reliableTrDimensions()&&he(e,"tr")||u==="auto"||!parseFloat(u)&&o.css(e,"display",!1,r)==="inline")&&e.getClientRects().length&&(a=o.css(e,"boxSizing",!1,r)==="border-box",(c=p in e)&&(u=e[p])),(u=parseFloat(u)||0)+ci(e,n,i||(a?"border":"content"),c,r,u)+"px"}function nt(e,n,i,r,a){return new nt.prototype.init(e,n,i,r,a)}o.extend({cssHooks:{opacity:{get:function(e,n){if(n){var i=$t(e,"opacity");return i===""?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,n,i,r){if(e&&e.nodeType!==3&&e.nodeType!==8&&e.style){var a,c,u,p=vt(n),h=Me.test(n),v=e.style;if(h||(n=Dn(p)),u=o.cssHooks[n]||o.cssHooks[p],i===void 0)return u&&"get"in u&&(a=u.get(e,!1,r))!==void 0?a:v[n];(c=typeof i)=="string"&&(a=yt.exec(i))&&a[1]&&(i=rr(e,n,a),c="number"),i!=null&&i==i&&(c!=="number"||h||(i+=a&&a[3]||(o.cssNumber[p]?"":"px")),K.clearCloneStyle||i!==""||n.indexOf("background")!==0||(v[n]="inherit"),u&&"set"in u&&(i=u.set(e,i,r))===void 0||(h?v.setProperty(n,i):v[n]=i))}},css:function(e,n,i,r){var a,c,u,p=vt(n);return Me.test(n)||(n=Dn(p)),(u=o.cssHooks[n]||o.cssHooks[p])&&"get"in u&&(a=u.get(e,!0,i)),a===void 0&&(a=$t(e,n,r)),a==="normal"&&n in ai&&(a=ai[n]),i===""||i?(c=parseFloat(a),i===!0||isFinite(c)?c||0:a):a}}),o.each(["height","width"],function(e,n){o.cssHooks[n]={get:function(i,r,a){if(r)return!_n.test(o.css(i,"display"))||i.getClientRects().length&&i.getBoundingClientRect().width?Pi(i,n,a):Ge(i,lr,function(){return Pi(i,n,a)})},set:function(i,r,a){var c,u=$e(i),p=!K.scrollboxSize()&&u.position==="absolute",h=(p||a)&&o.css(i,"boxSizing",!1,u)==="border-box",v=a?ci(i,n,a,h,u):0;return h&&p&&(v-=Math.ceil(i["offset"+n[0].toUpperCase()+n.slice(1)]-parseFloat(u[n])-ci(i,n,"border",!1,u)-.5)),v&&(c=yt.exec(r))&&(c[3]||"px")!=="px"&&(i.style[n]=r,r=o.css(i,n)),li(0,r,v)}}}),o.cssHooks.marginLeft=Gt(K.reliableMarginLeft,function(e,n){if(n)return(parseFloat($t(e,"marginLeft"))||e.getBoundingClientRect().left-Ge(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),o.each({margin:"",padding:"",border:"Width"},function(e,n){o.cssHooks[e+n]={expand:function(i){for(var r=0,a={},c=typeof i=="string"?i.split(" "):[i];r<4;r++)a[e+Qe[r]+n]=c[r]||c[r-2]||c[0];return a}},e!=="margin"&&(o.cssHooks[e+n].set=li)}),o.fn.extend({css:function(e,n){return Nt(this,function(i,r,a){var c,u,p={},h=0;if(Array.isArray(r)){for(c=$e(i),u=r.length;h<u;h++)p[r[h]]=o.css(i,r[h],!1,c);return p}return a!==void 0?o.style(i,r,a):o.css(i,r)},e,n,1<arguments.length)}}),((o.Tween=nt).prototype={constructor:nt,init:function(e,n,i,r,a,c){this.elem=e,this.prop=i,this.easing=a||o.easing._default,this.options=n,this.start=this.now=this.cur(),this.end=r,this.unit=c||(o.cssNumber[i]?"":"px")},cur:function(){var e=nt.propHooks[this.prop];return e&&e.get?e.get(this):nt.propHooks._default.get(this)},run:function(e){var n,i=nt.propHooks[this.prop];return this.options.duration?this.pos=n=o.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=n=e,this.now=(this.end-this.start)*n+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):nt.propHooks._default.set(this),this}}).init.prototype=nt.prototype,(nt.propHooks={_default:{get:function(e){var n;return e.elem.nodeType!==1||e.elem[e.prop]!=null&&e.elem.style[e.prop]==null?e.elem[e.prop]:(n=o.css(e.elem,e.prop,""))&&n!=="auto"?n:0},set:function(e){o.fx.step[e.prop]?o.fx.step[e.prop](e):e.elem.nodeType!==1||!o.cssHooks[e.prop]&&e.elem.style[Dn(e.prop)]==null?e.elem[e.prop]=e.now:o.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=nt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},o.easing={linear:function(e){return e},swing:function(e){return .5-Math.cos(e*Math.PI)/2},_default:"swing"},o.fx=nt.prototype.init,o.fx.step={};var Zt,Ln,en,Mi,cr=/^(?:toggle|show|hide)$/,ur=/queueHooks$/;function ui(){Ln&&(q.hidden===!1&&F.requestAnimationFrame?F.requestAnimationFrame(ui):F.setTimeout(ui,o.fx.interval),o.fx.tick())}function dt(){return F.setTimeout(function(){Zt=void 0}),Zt=Date.now()}function He(e,n){var i,r=0,a={height:e};for(n=n?1:0;r<4;r+=2-n)a["margin"+(i=Qe[r])]=a["padding"+i]=e;return n&&(a.opacity=a.width=e),a}function qt(e,n,i){for(var r,a=(we.tweeners[n]||[]).concat(we.tweeners["*"]),c=0,u=a.length;c<u;c++)if(r=a[c].call(i,n,e))return r}function we(e,n,i){var r,a,c=0,u=we.prefilters.length,p=o.Deferred().always(function(){delete h.elem}),h=function(){if(a)return!1;for(var C=Zt||dt(),b=Math.max(0,v.startTime+v.duration-C),A=1-(b/v.duration||0),I=0,V=v.tweens.length;I<V;I++)v.tweens[I].run(A);return p.notifyWith(e,[v,A,b]),A<1&&V?b:(V||p.notifyWith(e,[v,1,0]),p.resolveWith(e,[v]),!1)},v=p.promise({elem:e,props:o.extend({},n),opts:o.extend(!0,{specialEasing:{},easing:o.easing._default},i),originalProperties:n,originalOptions:i,startTime:Zt||dt(),duration:i.duration,tweens:[],createTween:function(C,b){var A=o.Tween(e,v.opts,C,b,v.opts.specialEasing[C]||v.opts.easing);return v.tweens.push(A),A},stop:function(C){var b=0,A=C?v.tweens.length:0;if(a)return this;for(a=!0;b<A;b++)v.tweens[b].run(1);return C?(p.notifyWith(e,[v,1,0]),p.resolveWith(e,[v,C])):p.rejectWith(e,[v,C]),this}}),w=v.props;for(!function(C,b){var A,I,V,X,be;for(A in C)if(V=b[I=vt(A)],X=C[A],Array.isArray(X)&&(V=X[1],X=C[A]=X[0]),A!==I&&(C[I]=X,delete C[A]),(be=o.cssHooks[I])&&"expand"in be)for(A in X=be.expand(X),delete C[I],X)A in C||(C[A]=X[A],b[A]=V);else b[I]=V}(w,v.opts.specialEasing);c<u;c++)if(r=we.prefilters[c].call(v,e,w,v.opts))return B(r.stop)&&(o._queueHooks(v.elem,v.opts.queue).stop=r.stop.bind(r)),r;return o.map(w,qt,v),B(v.opts.start)&&v.opts.start.call(e,v),v.progress(v.opts.progress).done(v.opts.done,v.opts.complete).fail(v.opts.fail).always(v.opts.always),o.fx.timer(o.extend(h,{elem:e,anim:v,queue:v.opts.queue})),v}o.Animation=o.extend(we,{tweeners:{"*":[function(e,n){var i=this.createTween(e,n);return rr(i.elem,e,yt.exec(n),i),i}]},tweener:function(e,n){B(e)?(n=e,e=["*"]):e=e.match(ut);for(var i,r=0,a=e.length;r<a;r++)i=e[r],we.tweeners[i]=we.tweeners[i]||[],we.tweeners[i].unshift(n)},prefilters:[function(e,n,i){var r,a,c,u,p,h,v,w,C="width"in n||"height"in n,b=this,A={},I=e.style,V=e.nodeType&&ni(e),X=H.get(e,"fxshow");for(r in i.queue||((u=o._queueHooks(e,"fx")).unqueued==null&&(u.unqueued=0,p=u.empty.fire,u.empty.fire=function(){u.unqueued||p()}),u.unqueued++,b.always(function(){b.always(function(){u.unqueued--,o.queue(e,"fx").length||u.empty.fire()})})),n)if(a=n[r],cr.test(a)){if(delete n[r],c=c||a==="toggle",a===(V?"hide":"show")){if(a!=="show"||!X||X[r]===void 0)continue;V=!0}A[r]=X&&X[r]||o.style(e,r)}if((h=!o.isEmptyObject(n))||!o.isEmptyObject(A))for(r in C&&e.nodeType===1&&(i.overflow=[I.overflow,I.overflowX,I.overflowY],(v=X&&X.display)==null&&(v=H.get(e,"display")),(w=o.css(e,"display"))==="none"&&(v?w=v:(mn([e],!0),v=e.style.display||v,w=o.css(e,"display"),mn([e]))),(w==="inline"||w==="inline-block"&&v!=null)&&o.css(e,"float")==="none"&&(h||(b.done(function(){I.display=v}),v==null&&(w=I.display,v=w==="none"?"":w)),I.display="inline-block")),i.overflow&&(I.overflow="hidden",b.always(function(){I.overflow=i.overflow[0],I.overflowX=i.overflow[1],I.overflowY=i.overflow[2]})),h=!1,A)h||(X?"hidden"in X&&(V=X.hidden):X=H.access(e,"fxshow",{display:v}),c&&(X.hidden=!V),V&&mn([e],!0),b.done(function(){for(r in V||mn([e]),H.remove(e,"fxshow"),A)o.style(e,r,A[r])})),h=qt(V?X[r]:0,r,b),r in X||(X[r]=h.start,V&&(h.end=h.start,h.start=0))}],prefilter:function(e,n){n?we.prefilters.unshift(e):we.prefilters.push(e)}}),o.speed=function(e,n,i){var r=e&&typeof e=="object"?o.extend({},e):{complete:i||!i&&n||B(e)&&e,duration:e,easing:i&&n||n&&!B(n)&&n};return o.fx.off?r.duration=0:typeof r.duration!="number"&&(r.duration in o.fx.speeds?r.duration=o.fx.speeds[r.duration]:r.duration=o.fx.speeds._default),r.queue!=null&&r.queue!==!0||(r.queue="fx"),r.old=r.complete,r.complete=function(){B(r.old)&&r.old.call(this),r.queue&&o.dequeue(this,r.queue)},r},o.fn.extend({fadeTo:function(e,n,i,r){return this.filter(ni).css("opacity",0).show().end().animate({opacity:n},e,i,r)},animate:function(e,n,i,r){var a=o.isEmptyObject(e),c=o.speed(n,i,r),u=function(){var p=we(this,o.extend({},e),c);(a||H.get(this,"finish"))&&p.stop(!0)};return u.finish=u,a||c.queue===!1?this.each(u):this.queue(c.queue,u)},stop:function(e,n,i){var r=function(a){var c=a.stop;delete a.stop,c(i)};return typeof e!="string"&&(i=n,n=e,e=void 0),n&&this.queue(e||"fx",[]),this.each(function(){var a=!0,c=e!=null&&e+"queueHooks",u=o.timers,p=H.get(this);if(c)p[c]&&p[c].stop&&r(p[c]);else for(c in p)p[c]&&p[c].stop&&ur.test(c)&&r(p[c]);for(c=u.length;c--;)u[c].elem!==this||e!=null&&u[c].queue!==e||(u[c].anim.stop(i),a=!1,u.splice(c,1));!a&&i||o.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var n,i=H.get(this),r=i[e+"queue"],a=i[e+"queueHooks"],c=o.timers,u=r?r.length:0;for(i.finish=!0,o.queue(this,e,[]),a&&a.stop&&a.stop.call(this,!0),n=c.length;n--;)c[n].elem===this&&c[n].queue===e&&(c[n].anim.stop(!0),c.splice(n,1));for(n=0;n<u;n++)r[n]&&r[n].finish&&r[n].finish.call(this);delete i.finish})}}),o.each(["toggle","show","hide"],function(e,n){var i=o.fn[n];o.fn[n]=function(r,a,c){return r==null||typeof r=="boolean"?i.apply(this,arguments):this.animate(He(n,!0),r,a,c)}}),o.each({slideDown:He("show"),slideUp:He("hide"),slideToggle:He("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,n){o.fn[e]=function(i,r,a){return this.animate(n,i,r,a)}}),o.timers=[],o.fx.tick=function(){var e,n=0,i=o.timers;for(Zt=Date.now();n<i.length;n++)(e=i[n])()||i[n]!==e||i.splice(n--,1);i.length||o.fx.stop(),Zt=void 0},o.fx.timer=function(e){o.timers.push(e),o.fx.start()},o.fx.interval=13,o.fx.start=function(){Ln||(Ln=!0,ui())},o.fx.stop=function(){Ln=null},o.fx.speeds={slow:600,fast:200,_default:400},o.fn.delay=function(e,n){return e=o.fx&&o.fx.speeds[e]||e,n=n||"fx",this.queue(n,function(i,r){var a=F.setTimeout(i,e);r.stop=function(){F.clearTimeout(a)}})},en=q.createElement("input"),Mi=q.createElement("select").appendChild(q.createElement("option")),en.type="checkbox",K.checkOn=en.value!=="",K.optSelected=Mi.selected,(en=q.createElement("input")).value="t",en.type="radio",K.radioValue=en.value==="t";var di,tn=o.expr.attrHandle;o.fn.extend({attr:function(e,n){return Nt(this,o.attr,e,n,1<arguments.length)},removeAttr:function(e){return this.each(function(){o.removeAttr(this,e)})}}),o.extend({attr:function(e,n,i){var r,a,c=e.nodeType;if(c!==3&&c!==8&&c!==2)return typeof e.getAttribute>"u"?o.prop(e,n,i):(c===1&&o.isXMLDoc(e)||(a=o.attrHooks[n.toLowerCase()]||(o.expr.match.bool.test(n)?di:void 0)),i!==void 0?i===null?void o.removeAttr(e,n):a&&"set"in a&&(r=a.set(e,i,n))!==void 0?r:(e.setAttribute(n,i+""),i):a&&"get"in a&&(r=a.get(e,n))!==null?r:(r=o.find.attr(e,n))==null?void 0:r)},attrHooks:{type:{set:function(e,n){if(!K.radioValue&&n==="radio"&&he(e,"input")){var i=e.value;return e.setAttribute("type",n),i&&(e.value=i),n}}}},removeAttr:function(e,n){var i,r=0,a=n&&n.match(ut);if(a&&e.nodeType===1)for(;i=a[r++];)e.removeAttribute(i)}}),di={set:function(e,n,i){return n===!1?o.removeAttr(e,i):e.setAttribute(i,i),i}},o.each(o.expr.match.bool.source.match(/\w+/g),function(e,n){var i=tn[n]||o.find.attr;tn[n]=function(r,a,c){var u,p,h=a.toLowerCase();return c||(p=tn[h],tn[h]=u,u=i(r,a,c)!=null?h:null,tn[h]=p),u}});var wt=/^(?:input|select|textarea|button)$/i,nn=/^(?:a|area)$/i;function xt(e){return(e.match(ut)||[]).join(" ")}function it(e){return e.getAttribute&&e.getAttribute("class")||""}function jn(e){return Array.isArray(e)?e:typeof e=="string"&&e.match(ut)||[]}o.fn.extend({prop:function(e,n){return Nt(this,o.prop,e,n,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[o.propFix[e]||e]})}}),o.extend({prop:function(e,n,i){var r,a,c=e.nodeType;if(c!==3&&c!==8&&c!==2)return c===1&&o.isXMLDoc(e)||(n=o.propFix[n]||n,a=o.propHooks[n]),i!==void 0?a&&"set"in a&&(r=a.set(e,i,n))!==void 0?r:e[n]=i:a&&"get"in a&&(r=a.get(e,n))!==null?r:e[n]},propHooks:{tabIndex:{get:function(e){var n=o.find.attr(e,"tabindex");return n?parseInt(n,10):wt.test(e.nodeName)||nn.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),K.optSelected||(o.propHooks.selected={get:function(e){var n=e.parentNode;return n&&n.parentNode&&n.parentNode.selectedIndex,null},set:function(e){var n=e.parentNode;n&&(n.selectedIndex,n.parentNode&&n.parentNode.selectedIndex)}}),o.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){o.propFix[this.toLowerCase()]=this}),o.fn.extend({addClass:function(e){var n,i,r,a,c,u;return B(e)?this.each(function(p){o(this).addClass(e.call(this,p,it(this)))}):(n=jn(e)).length?this.each(function(){if(r=it(this),i=this.nodeType===1&&" "+xt(r)+" "){for(c=0;c<n.length;c++)a=n[c],i.indexOf(" "+a+" ")<0&&(i+=a+" ");u=xt(i),r!==u&&this.setAttribute("class",u)}}):this},removeClass:function(e){var n,i,r,a,c,u;return B(e)?this.each(function(p){o(this).removeClass(e.call(this,p,it(this)))}):arguments.length?(n=jn(e)).length?this.each(function(){if(r=it(this),i=this.nodeType===1&&" "+xt(r)+" "){for(c=0;c<n.length;c++)for(a=n[c];-1<i.indexOf(" "+a+" ");)i=i.replace(" "+a+" "," ");u=xt(i),r!==u&&this.setAttribute("class",u)}}):this:this.attr("class","")},toggleClass:function(e,n){var i,r,a,c,u=typeof e,p=u==="string"||Array.isArray(e);return B(e)?this.each(function(h){o(this).toggleClass(e.call(this,h,it(this),n),n)}):typeof n=="boolean"&&p?n?this.addClass(e):this.removeClass(e):(i=jn(e),this.each(function(){if(p)for(c=o(this),a=0;a<i.length;a++)r=i[a],c.hasClass(r)?c.removeClass(r):c.addClass(r);else e!==void 0&&u!=="boolean"||((r=it(this))&&H.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||e===!1?"":H.get(this,"__className__")||""))}))},hasClass:function(e){var n,i,r=0;for(n=" "+e+" ";i=this[r++];)if(i.nodeType===1&&-1<(" "+xt(it(i))+" ").indexOf(n))return!0;return!1}});var dr=/\r/g;o.fn.extend({val:function(e){var n,i,r,a=this[0];return arguments.length?(r=B(e),this.each(function(c){var u;this.nodeType===1&&((u=r?e.call(this,c,o(this).val()):e)==null?u="":typeof u=="number"?u+="":Array.isArray(u)&&(u=o.map(u,function(p){return p==null?"":p+""})),(n=o.valHooks[this.type]||o.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&n.set(this,u,"value")!==void 0||(this.value=u))})):a?(n=o.valHooks[a.type]||o.valHooks[a.nodeName.toLowerCase()])&&"get"in n&&(i=n.get(a,"value"))!==void 0?i:typeof(i=a.value)=="string"?i.replace(dr,""):i??"":void 0}}),o.extend({valHooks:{option:{get:function(e){var n=o.find.attr(e,"value");return n??xt(o.text(e))}},select:{get:function(e){var n,i,r,a=e.options,c=e.selectedIndex,u=e.type==="select-one",p=u?null:[],h=u?c+1:a.length;for(r=c<0?h:u?c:0;r<h;r++)if(((i=a[r]).selected||r===c)&&!i.disabled&&(!i.parentNode.disabled||!he(i.parentNode,"optgroup"))){if(n=o(i).val(),u)return n;p.push(n)}return p},set:function(e,n){for(var i,r,a=e.options,c=o.makeArray(n),u=a.length;u--;)((r=a[u]).selected=-1<o.inArray(o.valHooks.option.get(r),c))&&(i=!0);return i||(e.selectedIndex=-1),c}}}}),o.each(["radio","checkbox"],function(){o.valHooks[this]={set:function(e,n){if(Array.isArray(n))return e.checked=-1<o.inArray(o(e).val(),n)}},K.checkOn||(o.valHooks[this].get=function(e){return e.getAttribute("value")===null?"on":e.value})});var Tt=F.location,fi={guid:Date.now()},hi=/\?/;o.parseXML=function(e){var n,i;if(!e||typeof e!="string")return null;try{n=new F.DOMParser().parseFromString(e,"text/xml")}catch{}return i=n&&n.getElementsByTagName("parsererror")[0],n&&!i||o.error("Invalid XML: "+(i?o.map(i.childNodes,function(r){return r.textContent}).join(`
`):e)),n};var Et=/^(?:focusinfocus|focusoutblur)$/,fr=function(e){e.stopPropagation()};o.extend(o.event,{trigger:function(e,n,i,r){var a,c,u,p,h,v,w,C,b=[i||q],A=Mt.call(e,"type")?e.type:e,I=Mt.call(e,"namespace")?e.namespace.split("."):[];if(c=C=u=i=i||q,i.nodeType!==3&&i.nodeType!==8&&!Et.test(A+o.event.triggered)&&(-1<A.indexOf(".")&&(A=(I=A.split(".")).shift(),I.sort()),h=A.indexOf(":")<0&&"on"+A,(e=e[o.expando]?e:new o.Event(A,typeof e=="object"&&e)).isTrigger=r?2:3,e.namespace=I.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+I.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=n==null?[e]:o.makeArray(n,[e]),w=o.event.special[A]||{},r||!w.trigger||w.trigger.apply(i,n)!==!1)){if(!r&&!w.noBubble&&!Pe(i)){for(p=w.delegateType||A,Et.test(p+A)||(c=c.parentNode);c;c=c.parentNode)b.push(c),u=c;u===(i.ownerDocument||q)&&b.push(u.defaultView||u.parentWindow||F)}for(a=0;(c=b[a++])&&!e.isPropagationStopped();)C=c,e.type=1<a?p:w.bindType||A,(v=(H.get(c,"events")||Object.create(null))[e.type]&&H.get(c,"handle"))&&v.apply(c,n),(v=h&&c[h])&&v.apply&&kn(c)&&(e.result=v.apply(c,n),e.result===!1&&e.preventDefault());return e.type=A,r||e.isDefaultPrevented()||w._default&&w._default.apply(b.pop(),n)!==!1||!kn(i)||h&&B(i[A])&&!Pe(i)&&((u=i[h])&&(i[h]=null),o.event.triggered=A,e.isPropagationStopped()&&C.addEventListener(A,fr),i[A](),e.isPropagationStopped()&&C.removeEventListener(A,fr),o.event.triggered=void 0,u&&(i[h]=u)),e.result}},simulate:function(e,n,i){var r=o.extend(new o.Event,i,{type:e,isSimulated:!0});o.event.trigger(r,null,n)}}),o.fn.extend({trigger:function(e,n){return this.each(function(){o.event.trigger(e,n,this)})},triggerHandler:function(e,n){var i=this[0];if(i)return o.event.trigger(e,n,i,!0)}});var Ft=/\[\]$/,Nn=/\r?\n/g,hr=/^(?:submit|button|image|reset|file)$/i,Pn=/^(?:input|select|textarea|keygen)/i;function Mn(e,n,i,r){var a;if(Array.isArray(n))o.each(n,function(c,u){i||Ft.test(e)?r(e,u):Mn(e+"["+(typeof u=="object"&&u!=null?c:"")+"]",u,i,r)});else if(i||cn(n)!=="object")r(e,n);else for(a in n)Mn(e+"["+a+"]",n[a],i,r)}o.param=function(e,n){var i,r=[],a=function(c,u){var p=B(u)?u():u;r[r.length]=encodeURIComponent(c)+"="+encodeURIComponent(p??"")};if(e==null)return"";if(Array.isArray(e)||e.jquery&&!o.isPlainObject(e))o.each(e,function(){a(this.name,this.value)});else for(i in e)Mn(i,e[i],n,a);return r.join("&")},o.fn.extend({serialize:function(){return o.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=o.prop(this,"elements");return e?o.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!o(this).is(":disabled")&&Pn.test(this.nodeName)&&!hr.test(e)&&(this.checked||!vn.test(e))}).map(function(e,n){var i=o(this).val();return i==null?null:Array.isArray(i)?o.map(i,function(r){return{name:n.name,value:r.replace(Nn,`\r
`)}}):{name:n.name,value:i.replace(Nn,`\r
`)}}).get()}});var In=/%20/g,pr=/#.*$/,gr=/([?&])_=[^&]*/,mr=/^(.*?):[ \t]*([^\r\n]*)$/gm,wn=/^(?:GET|HEAD)$/,qr=/^\/\//,Ii={},$n={},Hn="*/".concat("*"),qn=q.createElement("a");function vr(e){return function(n,i){typeof n!="string"&&(i=n,n="*");var r,a=0,c=n.toLowerCase().match(ut)||[];if(B(i))for(;r=c[a++];)r[0]==="+"?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(i)):(e[r]=e[r]||[]).push(i)}}function Fn(e,n,i,r){var a={},c=e===$n;function u(p){var h;return a[p]=!0,o.each(e[p]||[],function(v,w){var C=w(n,i,r);return typeof C!="string"||c||a[C]?c?!(h=C):void 0:(n.dataTypes.unshift(C),u(C),!1)}),h}return u(n.dataTypes[0])||!a["*"]&&u("*")}function $i(e,n){var i,r,a=o.ajaxSettings.flatOptions||{};for(i in n)n[i]!==void 0&&((a[i]?e:r||(r={}))[i]=n[i]);return r&&o.extend(!0,e,r),e}qn.href=Tt.href,o.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Tt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Tt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Hn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":o.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,n){return n?$i($i(e,o.ajaxSettings),n):$i(o.ajaxSettings,e)},ajaxPrefilter:vr(Ii),ajaxTransport:vr($n),ajax:function(e,n){typeof e=="object"&&(n=e,e=void 0),n=n||{};var i,r,a,c,u,p,h,v,w,C,b=o.ajaxSetup({},n),A=b.context||b,I=b.context&&(A.nodeType||A.jquery)?o(A):o.event,V=o.Deferred(),X=o.Callbacks("once memory"),be=b.statusCode||{},Se={},ft={},qe="canceled",te={readyState:0,getResponseHeader:function(re){var xe;if(h){if(!c)for(c={};xe=mr.exec(a);)c[xe[1].toLowerCase()+" "]=(c[xe[1].toLowerCase()+" "]||[]).concat(xe[2]);xe=c[re.toLowerCase()+" "]}return xe==null?null:xe.join(", ")},getAllResponseHeaders:function(){return h?a:null},setRequestHeader:function(re,xe){return h==null&&(re=ft[re.toLowerCase()]=ft[re.toLowerCase()]||re,Se[re]=xe),this},overrideMimeType:function(re){return h==null&&(b.mimeType=re),this},statusCode:function(re){var xe;if(re)if(h)te.always(re[te.status]);else for(xe in re)be[xe]=[be[xe],re[xe]];return this},abort:function(re){var xe=re||qe;return i&&i.abort(xe),on(0,xe),this}};if(V.promise(te),b.url=((e||b.url||Tt.href)+"").replace(qr,Tt.protocol+"//"),b.type=n.method||n.type||b.method||b.type,b.dataTypes=(b.dataType||"*").toLowerCase().match(ut)||[""],b.crossDomain==null){p=q.createElement("a");try{p.href=b.url,p.href=p.href,b.crossDomain=qn.protocol+"//"+qn.host!=p.protocol+"//"+p.host}catch{b.crossDomain=!0}}if(b.data&&b.processData&&typeof b.data!="string"&&(b.data=o.param(b.data,b.traditional)),Fn(Ii,b,n,te),h)return te;for(w in(v=o.event&&b.global)&&o.active++==0&&o.event.trigger("ajaxStart"),b.type=b.type.toUpperCase(),b.hasContent=!wn.test(b.type),r=b.url.replace(pr,""),b.hasContent?b.data&&b.processData&&(b.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&(b.data=b.data.replace(In,"+")):(C=b.url.slice(r.length),b.data&&(b.processData||typeof b.data=="string")&&(r+=(hi.test(r)?"&":"?")+b.data,delete b.data),b.cache===!1&&(r=r.replace(gr,"$1"),C=(hi.test(r)?"&":"?")+"_="+fi.guid+++C),b.url=r+C),b.ifModified&&(o.lastModified[r]&&te.setRequestHeader("If-Modified-Since",o.lastModified[r]),o.etag[r]&&te.setRequestHeader("If-None-Match",o.etag[r])),(b.data&&b.hasContent&&b.contentType!==!1||n.contentType)&&te.setRequestHeader("Content-Type",b.contentType),te.setRequestHeader("Accept",b.dataTypes[0]&&b.accepts[b.dataTypes[0]]?b.accepts[b.dataTypes[0]]+(b.dataTypes[0]!=="*"?", "+Hn+"; q=0.01":""):b.accepts["*"]),b.headers)te.setRequestHeader(w,b.headers[w]);if(b.beforeSend&&(b.beforeSend.call(A,te,b)===!1||h))return te.abort();if(qe="abort",X.add(b.complete),te.done(b.success),te.fail(b.error),i=Fn($n,b,n,te)){if(te.readyState=1,v&&I.trigger("ajaxSend",[te,b]),h)return te;b.async&&0<b.timeout&&(u=F.setTimeout(function(){te.abort("timeout")},b.timeout));try{h=!1,i.send(Se,on)}catch(re){if(h)throw re;on(-1,re)}}else on(-1,"No Transport");function on(re,xe,Wn,qi){var Ct,xn,At,Rt,ht,je=xe;h||(h=!0,u&&F.clearTimeout(u),i=void 0,a=qi||"",te.readyState=0<re?4:0,Ct=200<=re&&re<300||re===304,Wn&&(Rt=function(Ne,_e,ze){for(var Wt,Ve,W,Ee,Ce=Ne.contents,me=Ne.dataTypes;me[0]==="*";)me.shift(),Wt===void 0&&(Wt=Ne.mimeType||_e.getResponseHeader("Content-Type"));if(Wt){for(Ve in Ce)if(Ce[Ve]&&Ce[Ve].test(Wt)){me.unshift(Ve);break}}if(me[0]in ze)W=me[0];else{for(Ve in ze){if(!me[0]||Ne.converters[Ve+" "+me[0]]){W=Ve;break}Ee||(Ee=Ve)}W=W||Ee}if(W)return W!==me[0]&&me.unshift(W),ze[W]}(b,te,Wn)),!Ct&&-1<o.inArray("script",b.dataTypes)&&o.inArray("json",b.dataTypes)<0&&(b.converters["text script"]=function(){}),Rt=function(Ne,_e,ze,Wt){var Ve,W,Ee,Ce,me,kt={},Fe=Ne.dataTypes.slice();if(Fe[1])for(Ee in Ne.converters)kt[Ee.toLowerCase()]=Ne.converters[Ee];for(W=Fe.shift();W;)if(Ne.responseFields[W]&&(ze[Ne.responseFields[W]]=_e),!me&&Wt&&Ne.dataFilter&&(_e=Ne.dataFilter(_e,Ne.dataType)),me=W,W=Fe.shift()){if(W==="*")W=me;else if(me!=="*"&&me!==W){if(!(Ee=kt[me+" "+W]||kt["* "+W])){for(Ve in kt)if((Ce=Ve.split(" "))[1]===W&&(Ee=kt[me+" "+Ce[0]]||kt["* "+Ce[0]])){Ee===!0?Ee=kt[Ve]:kt[Ve]!==!0&&(W=Ce[0],Fe.unshift(Ce[1]));break}}if(Ee!==!0)if(Ee&&Ne.throws)_e=Ee(_e);else try{_e=Ee(_e)}catch(Bn){return{state:"parsererror",error:Ee?Bn:"No conversion from "+me+" to "+W}}}}return{state:"success",data:_e}}(b,Rt,te,Ct),Ct?(b.ifModified&&((ht=te.getResponseHeader("Last-Modified"))&&(o.lastModified[r]=ht),(ht=te.getResponseHeader("etag"))&&(o.etag[r]=ht)),re===204||b.type==="HEAD"?je="nocontent":re===304?je="notmodified":(je=Rt.state,xn=Rt.data,Ct=!(At=Rt.error))):(At=je,!re&&je||(je="error",re<0&&(re=0))),te.status=re,te.statusText=(xe||je)+"",Ct?V.resolveWith(A,[xn,je,te]):V.rejectWith(A,[te,je,At]),te.statusCode(be),be=void 0,v&&I.trigger(Ct?"ajaxSuccess":"ajaxError",[te,b,Ct?xn:At]),X.fireWith(A,[te,je]),v&&(I.trigger("ajaxComplete",[te,b]),--o.active||o.event.trigger("ajaxStop")))}return te},getJSON:function(e,n,i){return o.get(e,n,i,"json")},getScript:function(e,n){return o.get(e,void 0,n,"script")}}),o.each(["get","post"],function(e,n){o[n]=function(i,r,a,c){return B(r)&&(c=c||a,a=r,r=void 0),o.ajax(o.extend({url:i,type:n,dataType:c,data:r,success:a},o.isPlainObject(i)&&i))}}),o.ajaxPrefilter(function(e){var n;for(n in e.headers)n.toLowerCase()==="content-type"&&(e.contentType=e.headers[n]||"")}),o._evalUrl=function(e,n,i){return o.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(r){o.globalEval(r,n,i)}})},o.fn.extend({wrapAll:function(e){var n;return this[0]&&(B(e)&&(e=e.call(this[0])),n=o(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&n.insertBefore(this[0]),n.map(function(){for(var i=this;i.firstElementChild;)i=i.firstElementChild;return i}).append(this)),this},wrapInner:function(e){return B(e)?this.each(function(n){o(this).wrapInner(e.call(this,n))}):this.each(function(){var n=o(this),i=n.contents();i.length?i.wrapAll(e):n.append(e)})},wrap:function(e){var n=B(e);return this.each(function(i){o(this).wrapAll(n?e.call(this,i):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){o(this).replaceWith(this.childNodes)}),this}}),o.expr.pseudos.hidden=function(e){return!o.expr.pseudos.visible(e)},o.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},o.ajaxSettings.xhr=function(){try{return new F.XMLHttpRequest}catch{}};var yr={0:200,1223:204},rn=o.ajaxSettings.xhr();K.cors=!!rn&&"withCredentials"in rn,K.ajax=rn=!!rn,o.ajaxTransport(function(e){var n,i;if(K.cors||rn&&!e.crossDomain)return{send:function(r,a){var c,u=e.xhr();if(u.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(c in e.xhrFields)u[c]=e.xhrFields[c];for(c in e.mimeType&&u.overrideMimeType&&u.overrideMimeType(e.mimeType),e.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)u.setRequestHeader(c,r[c]);n=function(p){return function(){n&&(n=i=u.onload=u.onerror=u.onabort=u.ontimeout=u.onreadystatechange=null,p==="abort"?u.abort():p==="error"?typeof u.status!="number"?a(0,"error"):a(u.status,u.statusText):a(yr[u.status]||u.status,u.statusText,(u.responseType||"text")!=="text"||typeof u.responseText!="string"?{binary:u.response}:{text:u.responseText},u.getAllResponseHeaders()))}},u.onload=n(),i=u.onerror=u.ontimeout=n("error"),u.onabort!==void 0?u.onabort=i:u.onreadystatechange=function(){u.readyState===4&&F.setTimeout(function(){n&&i()})},n=n("abort");try{u.send(e.hasContent&&e.data||null)}catch(p){if(n)throw p}},abort:function(){n&&n()}}}),o.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),o.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return o.globalEval(e),e}}}),o.ajaxPrefilter("script",function(e){e.cache===void 0&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),o.ajaxTransport("script",function(e){var n,i;if(e.crossDomain||e.scriptAttrs)return{send:function(r,a){n=o("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",i=function(c){n.remove(),i=null,c&&a(c.type==="error"?404:200,c.type)}),q.head.appendChild(n[0])},abort:function(){i&&i()}}});var pi,gi=[],mi=/(=)\?(?=&|$)|\?\?/;o.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=gi.pop()||o.expando+"_"+fi.guid++;return this[e]=!0,e}}),o.ajaxPrefilter("json jsonp",function(e,n,i){var r,a,c,u=e.jsonp!==!1&&(mi.test(e.url)?"url":typeof e.data=="string"&&(e.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&mi.test(e.data)&&"data");if(u||e.dataTypes[0]==="jsonp")return r=e.jsonpCallback=B(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,u?e[u]=e[u].replace(mi,"$1"+r):e.jsonp!==!1&&(e.url+=(hi.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return c||o.error(r+" was not called"),c[0]},e.dataTypes[0]="json",a=F[r],F[r]=function(){c=arguments},i.always(function(){a===void 0?o(F).removeProp(r):F[r]=a,e[r]&&(e.jsonpCallback=n.jsonpCallback,gi.push(r)),c&&B(a)&&a(c[0]),c=a=void 0}),"script"}),K.createHTMLDocument=((pi=q.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",pi.childNodes.length===2),o.parseHTML=function(e,n,i){return typeof e!="string"?[]:(typeof n=="boolean"&&(i=n,n=!1),n||(K.createHTMLDocument?((r=(n=q.implementation.createHTMLDocument("")).createElement("base")).href=q.location.href,n.head.appendChild(r)):n=q),c=!i&&[],(a=ei.exec(e))?[n.createElement(a[1])]:(a=sr([e],n,c),c&&c.length&&o(c).remove(),o.merge([],a.childNodes)));var r,a,c},o.fn.load=function(e,n,i){var r,a,c,u=this,p=e.indexOf(" ");return-1<p&&(r=xt(e.slice(p)),e=e.slice(0,p)),B(n)?(i=n,n=void 0):n&&typeof n=="object"&&(a="POST"),0<u.length&&o.ajax({url:e,type:a||"GET",dataType:"html",data:n}).done(function(h){c=arguments,u.html(r?o("<div>").append(o.parseHTML(h)).find(r):h)}).always(i&&function(h,v){u.each(function(){i.apply(this,c||[h.responseText,v,h])})}),this},o.expr.pseudos.animated=function(e){return o.grep(o.timers,function(n){return e===n.elem}).length},o.offset={setOffset:function(e,n,i){var r,a,c,u,p,h,v=o.css(e,"position"),w=o(e),C={};v==="static"&&(e.style.position="relative"),p=w.offset(),c=o.css(e,"top"),h=o.css(e,"left"),(v==="absolute"||v==="fixed")&&-1<(c+h).indexOf("auto")?(u=(r=w.position()).top,a=r.left):(u=parseFloat(c)||0,a=parseFloat(h)||0),B(n)&&(n=n.call(e,i,o.extend({},p))),n.top!=null&&(C.top=n.top-p.top+u),n.left!=null&&(C.left=n.left-p.left+a),"using"in n?n.using.call(e,C):w.css(C)}},o.fn.extend({offset:function(e){if(arguments.length)return e===void 0?this:this.each(function(a){o.offset.setOffset(this,e,a)});var n,i,r=this[0];return r?r.getClientRects().length?(n=r.getBoundingClientRect(),i=r.ownerDocument.defaultView,{top:n.top+i.pageYOffset,left:n.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,n,i,r=this[0],a={top:0,left:0};if(o.css(r,"position")==="fixed")n=r.getBoundingClientRect();else{for(n=this.offset(),i=r.ownerDocument,e=r.offsetParent||i.documentElement;e&&(e===i.body||e===i.documentElement)&&o.css(e,"position")==="static";)e=e.parentNode;e&&e!==r&&e.nodeType===1&&((a=o(e).offset()).top+=o.css(e,"borderTopWidth",!0),a.left+=o.css(e,"borderLeftWidth",!0))}return{top:n.top-a.top-o.css(r,"marginTop",!0),left:n.left-a.left-o.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&o.css(e,"position")==="static";)e=e.offsetParent;return e||bt})}}),o.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,n){var i=n==="pageYOffset";o.fn[e]=function(r){return Nt(this,function(a,c,u){var p;if(Pe(a)?p=a:a.nodeType===9&&(p=a.defaultView),u===void 0)return p?p[n]:a[c];p?p.scrollTo(i?p.pageXOffset:u,i?u:p.pageYOffset):a[c]=u},e,r,arguments.length)}}),o.each(["top","left"],function(e,n){o.cssHooks[n]=Gt(K.pixelPosition,function(i,r){if(r)return r=$t(i,n),It.test(r)?o(i).position()[n]+"px":r})}),o.each({Height:"height",Width:"width"},function(e,n){o.each({padding:"inner"+e,content:n,"":"outer"+e},function(i,r){o.fn[r]=function(a,c){var u=arguments.length&&(i||typeof a!="boolean"),p=i||(a===!0||c===!0?"margin":"border");return Nt(this,function(h,v,w){var C;return Pe(h)?r.indexOf("outer")===0?h["inner"+e]:h.document.documentElement["client"+e]:h.nodeType===9?(C=h.documentElement,Math.max(h.body["scroll"+e],C["scroll"+e],h.body["offset"+e],C["offset"+e],C["client"+e])):w===void 0?o.css(h,v,p):o.style(h,v,w,p)},n,u?a:void 0,u)}})}),o.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,n){o.fn[n]=function(i){return this.on(n,i)}}),o.fn.extend({bind:function(e,n,i){return this.on(e,null,n,i)},unbind:function(e,n){return this.off(e,null,n)},delegate:function(e,n,i,r){return this.on(n,e,i,r)},undelegate:function(e,n,i){return arguments.length===1?this.off(e,"**"):this.off(n,e||"**",i)},hover:function(e,n){return this.on("mouseenter",e).on("mouseleave",n||e)}}),o.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){o.fn[n]=function(i,r){return 0<arguments.length?this.on(n,null,i,r):this.trigger(n)}});var Rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;o.proxy=function(e,n){var i,r,a;if(typeof n=="string"&&(i=e[n],n=e,e=i),B(e))return r=Ue.call(arguments,2),(a=function(){return e.apply(n||this,r.concat(Ue.call(arguments)))}).guid=e.guid=e.guid||o.guid++,a},o.holdReady=function(e){e?o.readyWait++:o.ready(!0)},o.isArray=Array.isArray,o.parseJSON=JSON.parse,o.nodeName=he,o.isFunction=B,o.isWindow=Pe,o.camelCase=vt,o.type=cn,o.now=Date.now,o.isNumeric=function(e){var n=o.type(e);return(n==="number"||n==="string")&&!isNaN(e-parseFloat(e))},o.trim=function(e){return e==null?"":(e+"").replace(Rn,"$1")},typeof define=="function"&&define.amd&&define("jquery",[],function(){return o});var Hi=F.jQuery,br=F.$;return o.noConflict=function(e){return F.$===o&&(F.$=br),e&&F.jQuery===o&&(F.jQuery=Hi),o},typeof ot>"u"&&(F.jQuery=F.$=o),o});/*!
  * Bootstrap v5.3.5 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */(function(F,ot){typeof exports=="object"&&typeof module<"u"?module.exports=ot():typeof define=="function"&&define.amd?define(ot):(F=typeof globalThis<"u"?globalThis:F||self).bootstrap=ot()})(this,function(){"use strict";const F=new Map,ot={set(l,t,s){F.has(l)||F.set(l,new Map);const d=F.get(l);d.has(t)||d.size===0?d.set(t,s):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(d.keys())[0]}.`)},get:(l,t)=>F.has(l)&&F.get(l).get(t)||null,remove(l,t){if(!F.has(l))return;const s=F.get(l);s.delete(t),s.size===0&&F.delete(l)}},ke="transitionend",Ti=l=>(l&&window.CSS&&window.CSS.escape&&(l=l.replace(/#([^\s"#']+)/g,(t,s)=>`#${CSS.escape(s)}`)),l),Ue=l=>{l.dispatchEvent(new Event(ke))},mt=l=>!(!l||typeof l!="object")&&(l.jquery!==void 0&&(l=l[0]),l.nodeType!==void 0),st=l=>mt(l)?l.jquery?l[0]:l:typeof l=="string"&&l.length>0?document.querySelector(Ti(l)):null,Ye=l=>{if(!mt(l)||l.getClientRects().length===0)return!1;const t=getComputedStyle(l).getPropertyValue("visibility")==="visible",s=l.closest("details:not([open])");if(!s)return t;if(s!==l){const d=l.closest("summary");if(d&&d.parentNode!==s||d===null)return!1}return t},at=l=>!l||l.nodeType!==Node.ELEMENT_NODE||!!l.classList.contains("disabled")||(l.disabled!==void 0?l.disabled:l.hasAttribute("disabled")&&l.getAttribute("disabled")!=="false"),Ei=l=>{if(!document.documentElement.attachShadow)return null;if(typeof l.getRootNode=="function"){const t=l.getRootNode();return t instanceof ShadowRoot?t:null}return l instanceof ShadowRoot?l:l.parentNode?Ei(l.parentNode):null},Mt=()=>{},ln=l=>{l.offsetHeight},Qi=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,K=[],B=()=>document.documentElement.dir==="rtl",Pe=l=>{var t;t=()=>{const s=Qi();if(s){const d=l.NAME,m=s.fn[d];s.fn[d]=l.jQueryInterface,s.fn[d].Constructor=l,s.fn[d].noConflict=()=>(s.fn[d]=m,l.jQueryInterface)}},document.readyState==="loading"?(K.length||document.addEventListener("DOMContentLoaded",()=>{for(const s of K)s()}),K.push(t)):t()},q=(l,t=[],s=l)=>typeof l=="function"?l.call(...t):s,Ki=(l,t,s=!0)=>{if(!s)return void q(l);const d=(T=>{if(!T)return 0;let{transitionDuration:k,transitionDelay:O}=window.getComputedStyle(T);const P=Number.parseFloat(k),M=Number.parseFloat(O);return P||M?(k=k.split(",")[0],O=O.split(",")[0],1e3*(Number.parseFloat(k)+Number.parseFloat(O))):0})(t)+5;let m=!1;const y=({target:T})=>{T===t&&(m=!0,t.removeEventListener(ke,y),q(l))};t.addEventListener(ke,y),setTimeout(()=>{m||Ue(t)},d)},Kn=(l,t,s,d)=>{const m=l.length;let y=l.indexOf(t);return y===-1?!s&&d?l[m-1]:l[0]:(y+=s?1:-1,d&&(y=(y+m)%m),l[Math.max(0,Math.min(y,m-1))])},cn=/[^.]*(?=\..*)\.|.*/,Gi=/\..*/,Or=/::\d+$/,o={};let Gn=1;const he={mouseenter:"mouseover",mouseleave:"mouseout"},Dr=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Ji(l,t){return t&&`${t}::${Gn++}`||l.uidEvent||Gn++}function Zi(l){const t=Ji(l);return l.uidEvent=t,o[t]=o[t]||{},o[t]}function ce(l,t,s=null){return Object.values(l).find(d=>d.callable===t&&d.delegationSelector===s)}function un(l,t,s){const d=typeof t=="string",m=d?s:t||s;let y=Jn(l);return Dr.has(y)||(y=l),[d,m,y]}function er(l,t,s,d,m){if(typeof t!="string"||!l)return;let[y,T,k]=un(t,s,d);t in he&&(T=(J=>function(Q){if(!Q.relatedTarget||Q.relatedTarget!==Q.delegateTarget&&!Q.delegateTarget.contains(Q.relatedTarget))return J.call(this,Q)})(T));const O=Zi(l),P=O[k]||(O[k]={}),M=ce(P,T,y?s:null);if(M)return void(M.oneOff=M.oneOff&&m);const L=Ji(T,t.replace(cn,"")),ee=y?function(U,J,Q){return function Z(pe){const ye=U.querySelectorAll(J);for(let{target:ie}=pe;ie&&ie!==this;ie=ie.parentNode)for(const ue of ye)if(ue===ie)return Zn(pe,{delegateTarget:ie}),Z.oneOff&&S.off(U,pe.type,J,Q),Q.apply(ie,[pe])}}(l,s,T):function(U,J){return function Q(Z){return Zn(Z,{delegateTarget:U}),Q.oneOff&&S.off(U,Z.type,J),J.apply(U,[Z])}}(l,T);ee.delegationSelector=y?s:null,ee.callable=T,ee.oneOff=m,ee.uidEvent=L,P[L]=ee,l.addEventListener(k,ee,y)}function Ci(l,t,s,d,m){const y=ce(t[s],d,m);y&&(l.removeEventListener(s,y,!!m),delete t[s][y.uidEvent])}function Lt(l,t,s,d){const m=t[s]||{};for(const[y,T]of Object.entries(m))y.includes(d)&&Ci(l,t,s,T.callable,T.delegationSelector)}function Jn(l){return l=l.replace(Gi,""),he[l]||l}const S={on(l,t,s,d){er(l,t,s,d,!1)},one(l,t,s,d){er(l,t,s,d,!0)},off(l,t,s,d){if(typeof t!="string"||!l)return;const[m,y,T]=un(t,s,d),k=T!==t,O=Zi(l),P=O[T]||{},M=t.startsWith(".");if(y===void 0){if(M)for(const L of Object.keys(O))Lt(l,O,L,t.slice(1));for(const[L,ee]of Object.entries(P)){const U=L.replace(Or,"");k&&!t.includes(U)||Ci(l,O,T,ee.callable,ee.delegationSelector)}}else{if(!Object.keys(P).length)return;Ci(l,O,T,y,m?s:null)}},trigger(l,t,s){if(typeof t!="string"||!l)return null;const d=Qi();let m=null,y=!0,T=!0,k=!1;t!==Jn(t)&&d&&(m=d.Event(t,s),d(l).trigger(m),y=!m.isPropagationStopped(),T=!m.isImmediatePropagationStopped(),k=m.isDefaultPrevented());const O=Zn(new Event(t,{bubbles:y,cancelable:!0}),s);return k&&O.preventDefault(),T&&l.dispatchEvent(O),O.defaultPrevented&&m&&m.preventDefault(),O}};function Zn(l,t={}){for(const[s,d]of Object.entries(t))try{l[s]=d}catch{Object.defineProperty(l,s,{configurable:!0,get:()=>d})}return l}function Ai(l){if(l==="true")return!0;if(l==="false")return!1;if(l===Number(l).toString())return Number(l);if(l===""||l==="null")return null;if(typeof l!="string")return l;try{return JSON.parse(decodeURIComponent(l))}catch{return l}}function ei(l){return l.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const lt={setDataAttribute(l,t,s){l.setAttribute(`data-bs-${ei(t)}`,s)},removeDataAttribute(l,t){l.removeAttribute(`data-bs-${ei(t)}`)},getDataAttributes(l){if(!l)return{};const t={},s=Object.keys(l.dataset).filter(d=>d.startsWith("bs")&&!d.startsWith("bsConfig"));for(const d of s){let m=d.replace(/^bs/,"");m=m.charAt(0).toLowerCase()+m.slice(1),t[m]=Ai(l.dataset[d])}return t},getDataAttribute:(l,t)=>Ai(l.getAttribute(`data-bs-${ei(t)}`))};class dn{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,s){const d=mt(s)?lt.getDataAttribute(s,"config"):{};return{...this.constructor.Default,...typeof d=="object"?d:{},...mt(s)?lt.getDataAttributes(s):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,s=this.constructor.DefaultType){for(const[m,y]of Object.entries(s)){const T=t[m],k=mt(T)?"element":(d=T)==null?`${d}`:Object.prototype.toString.call(d).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(y).test(k))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${m}" provided type "${k}" but expected type "${y}".`)}var d}}class ct extends dn{constructor(t,s){super(),(t=st(t))&&(this._element=t,this._config=this._getConfig(s),ot.set(this._element,this.constructor.DATA_KEY,this))}dispose(){ot.remove(this._element,this.constructor.DATA_KEY),S.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,s,d=!0){Ki(t,s,d)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return ot.get(st(t),this.DATA_KEY)}static getOrCreateInstance(t,s={}){return this.getInstance(t)||new this(t,typeof s=="object"?s:null)}static get VERSION(){return"5.3.5"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const ki=l=>{let t=l.getAttribute("data-bs-target");if(!t||t==="#"){let s=l.getAttribute("href");if(!s||!s.includes("#")&&!s.startsWith("."))return null;s.includes("#")&&!s.startsWith("#")&&(s=`#${s.split("#")[1]}`),t=s&&s!=="#"?s.trim():null}return t?t.split(",").map(s=>Ti(s)).join(","):null},R={find:(l,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,l)),findOne:(l,t=document.documentElement)=>Element.prototype.querySelector.call(t,l),children:(l,t)=>[].concat(...l.children).filter(s=>s.matches(t)),parents(l,t){const s=[];let d=l.parentNode.closest(t);for(;d;)s.push(d),d=d.parentNode.closest(t);return s},prev(l,t){let s=l.previousElementSibling;for(;s;){if(s.matches(t))return[s];s=s.previousElementSibling}return[]},next(l,t){let s=l.nextElementSibling;for(;s;){if(s.matches(t))return[s];s=s.nextElementSibling}return[]},focusableChildren(l){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(s=>`${s}:not([tabindex^="-"])`).join(",");return this.find(t,l).filter(s=>!at(s)&&Ye(s))},getSelectorFromElement(l){const t=ki(l);return t&&R.findOne(t)?t:null},getElementFromSelector(l){const t=ki(l);return t?R.findOne(t):null},getMultipleElementsFromSelector(l){const t=ki(l);return t?R.find(t):[]}},An=(l,t="hide")=>{const s=`click.dismiss${l.EVENT_KEY}`,d=l.NAME;S.on(document,s,`[data-bs-dismiss="${d}"]`,function(m){if(["A","AREA"].includes(this.tagName)&&m.preventDefault(),at(this))return;const y=R.getElementFromSelector(this)||this.closest(`.${d}`);l.getOrCreateInstance(y)[t]()})},ut=".bs.alert",fn=`close${ut}`,ti=`closed${ut}`;class hn extends ct{static get NAME(){return"alert"}close(){if(S.trigger(this._element,fn).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,t)}_destroyElement(){this._element.remove(),S.trigger(this._element,ti),this.dispose()}static jQueryInterface(t){return this.each(function(){const s=hn.getOrCreateInstance(this);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t](this)}})}}An(hn,"close"),Pe(hn);const tr='[data-bs-toggle="button"]';class Ut extends ct{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each(function(){const s=Ut.getOrCreateInstance(this);t==="toggle"&&s[t]()})}}S.on(document,"click.bs.button.data-api",tr,l=>{l.preventDefault();const t=l.target.closest(tr);Ut.getOrCreateInstance(t).toggle()}),Pe(Ut);const jt=".bs.swipe",Nt=`touchstart${jt}`,Lr=`touchmove${jt}`,jr=`touchend${jt}`,Nr=`pointerdown${jt}`,vt=`pointerup${jt}`,kn={endCallback:null,leftCallback:null,rightCallback:null},Sn={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class H extends dn{constructor(t,s){super(),this._element=t,t&&H.isSupported()&&(this._config=this._getConfig(s),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return kn}static get DefaultType(){return Sn}static get NAME(){return"swipe"}dispose(){S.off(this._element,jt)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),q(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const s=t/this._deltaX;this._deltaX=0,s&&q(s>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(S.on(this._element,Nr,t=>this._start(t)),S.on(this._element,vt,t=>this._end(t)),this._element.classList.add("pointer-event")):(S.on(this._element,Nt,t=>this._start(t)),S.on(this._element,Lr,t=>this._move(t)),S.on(this._element,jr,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType==="pen"||t.pointerType==="touch")}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Te=".bs.carousel",nr=".data-api",Pr="ArrowLeft",ir="ArrowRight",pn="next",yt="prev",Qe="left",bt="right",gn=`slide${Te}`,Si=`slid${Te}`,ni=`keydown${Te}`,rr=`mouseenter${Te}`,or=`mouseleave${Te}`,mn=`dragstart${Te}`,Yt=`load${Te}${nr}`,ii=`click${Te}${nr}`,vn="carousel",On="active",Oi=".active",Je=".carousel-item",Ke=Oi+Je,Di={[Pr]:bt,[ir]:Qe},Mr={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},sr={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Qt extends ct{constructor(t,s){super(t,s),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=R.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===vn&&this.cycle()}static get Default(){return Mr}static get DefaultType(){return sr}static get NAME(){return"carousel"}next(){this._slide(pn)}nextWhenVisible(){!document.hidden&&Ye(this._element)&&this.next()}prev(){this._slide(yt)}pause(){this._isSliding&&Ue(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?S.one(this._element,Si,()=>this.cycle()):this.cycle())}to(t){const s=this._getItems();if(t>s.length-1||t<0)return;if(this._isSliding)return void S.one(this._element,Si,()=>this.to(t));const d=this._getItemIndex(this._getActive());if(d===t)return;const m=t>d?pn:yt;this._slide(m,s[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&S.on(this._element,ni,t=>this._keydown(t)),this._config.pause==="hover"&&(S.on(this._element,rr,()=>this.pause()),S.on(this._element,or,()=>this._maybeEnableCycle())),this._config.touch&&H.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const s of R.find(".carousel-item img",this._element))S.on(s,mn,d=>d.preventDefault());const t={leftCallback:()=>this._slide(this._directionToOrder(Qe)),rightCallback:()=>this._slide(this._directionToOrder(bt)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new H(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const s=Di[t.key];s&&(t.preventDefault(),this._slide(this._directionToOrder(s)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const s=R.findOne(Oi,this._indicatorsElement);s.classList.remove(On),s.removeAttribute("aria-current");const d=R.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);d&&(d.classList.add(On),d.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const s=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=s||this._config.defaultInterval}_slide(t,s=null){if(this._isSliding)return;const d=this._getActive(),m=t===pn,y=s||Kn(this._getItems(),d,m,this._config.wrap);if(y===d)return;const T=this._getItemIndex(y),k=L=>S.trigger(this._element,L,{relatedTarget:y,direction:this._orderToDirection(t),from:this._getItemIndex(d),to:T});if(k(gn).defaultPrevented||!d||!y)return;const O=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(T),this._activeElement=y;const P=m?"carousel-item-start":"carousel-item-end",M=m?"carousel-item-next":"carousel-item-prev";y.classList.add(M),ln(y),d.classList.add(P),y.classList.add(P),this._queueCallback(()=>{y.classList.remove(P,M),y.classList.add(On),d.classList.remove(On,M,P),this._isSliding=!1,k(Si)},d,this._isAnimated()),O&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return R.findOne(Ke,this._element)}_getItems(){return R.find(Je,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return B()?t===Qe?yt:pn:t===Qe?pn:yt}_orderToDirection(t){return B()?t===yt?Qe:bt:t===yt?bt:Qe}static jQueryInterface(t){return this.each(function(){const s=Qt.getOrCreateInstance(this,t);if(typeof t!="number"){if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t]()}}else s.to(t)})}}S.on(document,ii,"[data-bs-slide], [data-bs-slide-to]",function(l){const t=R.getElementFromSelector(this);if(!t||!t.classList.contains(vn))return;l.preventDefault();const s=Qt.getOrCreateInstance(t),d=this.getAttribute("data-bs-slide-to");return d?(s.to(d),void s._maybeEnableCycle()):lt.getDataAttribute(this,"slide")==="next"?(s.next(),void s._maybeEnableCycle()):(s.prev(),void s._maybeEnableCycle())}),S.on(window,Yt,()=>{const l=R.find('[data-bs-ride="carousel"]');for(const t of l)Qt.getOrCreateInstance(t)}),Pe(Qt);const _t=".bs.collapse",yn=`show${_t}`,Li=`shown${_t}`,ri=`hide${_t}`,Ir=`hidden${_t}`,$r=`click${_t}.data-api`,ji="show",Kt="collapse",oi="collapsing",Hr=`:scope .${Kt} .${Kt}`,si='[data-bs-toggle="collapse"]',bn={parent:null,toggle:!0},ar={parent:"(null|element)",toggle:"boolean"};class It extends ct{constructor(t,s){super(t,s),this._isTransitioning=!1,this._triggerArray=[];const d=R.find(si);for(const m of d){const y=R.getSelectorFromElement(m),T=R.find(y).filter(k=>k===this._element);y!==null&&T.length&&this._triggerArray.push(m)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return bn}static get DefaultType(){return ar}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(m=>m!==this._element).map(m=>It.getOrCreateInstance(m,{toggle:!1}))),t.length&&t[0]._isTransitioning||S.trigger(this._element,yn).defaultPrevented)return;for(const m of t)m.hide();const s=this._getDimension();this._element.classList.remove(Kt),this._element.classList.add(oi),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const d=`scroll${s[0].toUpperCase()+s.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(oi),this._element.classList.add(Kt,ji),this._element.style[s]="",S.trigger(this._element,Li)},this._element,!0),this._element.style[s]=`${this._element[d]}px`}hide(){if(this._isTransitioning||!this._isShown()||S.trigger(this._element,ri).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,ln(this._element),this._element.classList.add(oi),this._element.classList.remove(Kt,ji);for(const s of this._triggerArray){const d=R.getElementFromSelector(s);d&&!this._isShown(d)&&this._addAriaAndCollapsedClass([s],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(oi),this._element.classList.add(Kt),S.trigger(this._element,Ir)},this._element,!0)}_isShown(t=this._element){return t.classList.contains(ji)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=st(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(si);for(const s of t){const d=R.getElementFromSelector(s);d&&this._addAriaAndCollapsedClass([s],this._isShown(d))}}_getFirstLevelChildren(t){const s=R.find(Hr,this._config.parent);return R.find(t,this._config.parent).filter(d=>!s.includes(d))}_addAriaAndCollapsedClass(t,s){if(t.length)for(const d of t)d.classList.toggle("collapsed",!s),d.setAttribute("aria-expanded",s)}static jQueryInterface(t){const s={};return typeof t=="string"&&/show|hide/.test(t)&&(s.toggle=!1),this.each(function(){const d=It.getOrCreateInstance(this,s);if(typeof t=="string"){if(d[t]===void 0)throw new TypeError(`No method named "${t}"`);d[t]()}})}}S.on(document,$r,si,function(l){(l.target.tagName==="A"||l.delegateTarget&&l.delegateTarget.tagName==="A")&&l.preventDefault();for(const t of R.getMultipleElementsFromSelector(this))It.getOrCreateInstance(t,{toggle:!1}).toggle()}),Pe(It);var Me="top",$e="bottom",Ge="right",Be="left",$t="auto",Gt=[Me,$e,Ge,Be],Ht="start",Jt="end",Ni="clippingParents",Dn="viewport",_n="popper",lr="reference",ai=Gt.reduce(function(l,t){return l.concat([t+"-"+Ht,t+"-"+Jt])},[]),li=[].concat(Gt,[$t]).reduce(function(l,t){return l.concat([t,t+"-"+Ht,t+"-"+Jt])},[]),ci="beforeRead",Pi="read",nt="afterRead",Zt="beforeMain",Ln="main",en="afterMain",Mi="beforeWrite",cr="write",ur="afterWrite",ui=[ci,Pi,nt,Zt,Ln,en,Mi,cr,ur];function dt(l){return l?(l.nodeName||"").toLowerCase():null}function He(l){if(l==null)return window;if(l.toString()!=="[object Window]"){var t=l.ownerDocument;return t&&t.defaultView||window}return l}function qt(l){return l instanceof He(l).Element||l instanceof Element}function we(l){return l instanceof He(l).HTMLElement||l instanceof HTMLElement}function di(l){return typeof ShadowRoot<"u"&&(l instanceof He(l).ShadowRoot||l instanceof ShadowRoot)}const tn={name:"applyStyles",enabled:!0,phase:"write",fn:function(l){var t=l.state;Object.keys(t.elements).forEach(function(s){var d=t.styles[s]||{},m=t.attributes[s]||{},y=t.elements[s];we(y)&&dt(y)&&(Object.assign(y.style,d),Object.keys(m).forEach(function(T){var k=m[T];k===!1?y.removeAttribute(T):y.setAttribute(T,k===!0?"":k)}))})},effect:function(l){var t=l.state,s={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,s.popper),t.styles=s,t.elements.arrow&&Object.assign(t.elements.arrow.style,s.arrow),function(){Object.keys(t.elements).forEach(function(d){var m=t.elements[d],y=t.attributes[d]||{},T=Object.keys(t.styles.hasOwnProperty(d)?t.styles[d]:s[d]).reduce(function(k,O){return k[O]="",k},{});we(m)&&dt(m)&&(Object.assign(m.style,T),Object.keys(y).forEach(function(k){m.removeAttribute(k)}))})}},requires:["computeStyles"]};function wt(l){return l.split("-")[0]}var nn=Math.max,xt=Math.min,it=Math.round;function jn(){var l=navigator.userAgentData;return l!=null&&l.brands&&Array.isArray(l.brands)?l.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function dr(){return!/^((?!chrome|android).)*safari/i.test(jn())}function Tt(l,t,s){t===void 0&&(t=!1),s===void 0&&(s=!1);var d=l.getBoundingClientRect(),m=1,y=1;t&&we(l)&&(m=l.offsetWidth>0&&it(d.width)/l.offsetWidth||1,y=l.offsetHeight>0&&it(d.height)/l.offsetHeight||1);var T=(qt(l)?He(l):window).visualViewport,k=!dr()&&s,O=(d.left+(k&&T?T.offsetLeft:0))/m,P=(d.top+(k&&T?T.offsetTop:0))/y,M=d.width/m,L=d.height/y;return{width:M,height:L,top:P,right:O+M,bottom:P+L,left:O,x:O,y:P}}function fi(l){var t=Tt(l),s=l.offsetWidth,d=l.offsetHeight;return Math.abs(t.width-s)<=1&&(s=t.width),Math.abs(t.height-d)<=1&&(d=t.height),{x:l.offsetLeft,y:l.offsetTop,width:s,height:d}}function hi(l,t){var s=t.getRootNode&&t.getRootNode();if(l.contains(t))return!0;if(s&&di(s)){var d=t;do{if(d&&l.isSameNode(d))return!0;d=d.parentNode||d.host}while(d)}return!1}function Et(l){return He(l).getComputedStyle(l)}function fr(l){return["table","td","th"].indexOf(dt(l))>=0}function Ft(l){return((qt(l)?l.ownerDocument:l.document)||window.document).documentElement}function Nn(l){return dt(l)==="html"?l:l.assignedSlot||l.parentNode||(di(l)?l.host:null)||Ft(l)}function hr(l){return we(l)&&Et(l).position!=="fixed"?l.offsetParent:null}function Pn(l){for(var t=He(l),s=hr(l);s&&fr(s)&&Et(s).position==="static";)s=hr(s);return s&&(dt(s)==="html"||dt(s)==="body"&&Et(s).position==="static")?t:s||function(d){var m=/firefox/i.test(jn());if(/Trident/i.test(jn())&&we(d)&&Et(d).position==="fixed")return null;var y=Nn(d);for(di(y)&&(y=y.host);we(y)&&["html","body"].indexOf(dt(y))<0;){var T=Et(y);if(T.transform!=="none"||T.perspective!=="none"||T.contain==="paint"||["transform","perspective"].indexOf(T.willChange)!==-1||m&&T.willChange==="filter"||m&&T.filter&&T.filter!=="none")return y;y=y.parentNode}return null}(l)||t}function Mn(l){return["top","bottom"].indexOf(l)>=0?"x":"y"}function In(l,t,s){return nn(l,xt(t,s))}function pr(l){return Object.assign({},{top:0,right:0,bottom:0,left:0},l)}function gr(l,t){return t.reduce(function(s,d){return s[d]=l,s},{})}const mr={name:"arrow",enabled:!0,phase:"main",fn:function(l){var t,s=l.state,d=l.name,m=l.options,y=s.elements.arrow,T=s.modifiersData.popperOffsets,k=wt(s.placement),O=Mn(k),P=[Be,Ge].indexOf(k)>=0?"height":"width";if(y&&T){var M=function(ge,fe){return pr(typeof(ge=typeof ge=="function"?ge(Object.assign({},fe.rects,{placement:fe.placement})):ge)!="number"?ge:gr(ge,Gt))}(m.padding,s),L=fi(y),ee=O==="y"?Me:Be,U=O==="y"?$e:Ge,J=s.rects.reference[P]+s.rects.reference[O]-T[O]-s.rects.popper[P],Q=T[O]-s.rects.reference[O],Z=Pn(y),pe=Z?O==="y"?Z.clientHeight||0:Z.clientWidth||0:0,ye=J/2-Q/2,ie=M[ee],ue=pe-L[P]-M[U],ne=pe/2-L[P]/2+ye,se=In(ie,ne,ue),de=O;s.modifiersData[d]=((t={})[de]=se,t.centerOffset=se-ne,t)}},effect:function(l){var t=l.state,s=l.options.element,d=s===void 0?"[data-popper-arrow]":s;d!=null&&(typeof d!="string"||(d=t.elements.popper.querySelector(d)))&&hi(t.elements.popper,d)&&(t.elements.arrow=d)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function wn(l){return l.split("-")[1]}var qr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ii(l){var t,s=l.popper,d=l.popperRect,m=l.placement,y=l.variation,T=l.offsets,k=l.position,O=l.gpuAcceleration,P=l.adaptive,M=l.roundOffsets,L=l.isFixed,ee=T.x,U=ee===void 0?0:ee,J=T.y,Q=J===void 0?0:J,Z=typeof M=="function"?M({x:U,y:Q}):{x:U,y:Q};U=Z.x,Q=Z.y;var pe=T.hasOwnProperty("x"),ye=T.hasOwnProperty("y"),ie=Be,ue=Me,ne=window;if(P){var se=Pn(s),de="clientHeight",ge="clientWidth";se===He(s)&&Et(se=Ft(s)).position!=="static"&&k==="absolute"&&(de="scrollHeight",ge="scrollWidth"),(m===Me||(m===Be||m===Ge)&&y===Jt)&&(ue=$e,Q-=(L&&se===ne&&ne.visualViewport?ne.visualViewport.height:se[de])-d.height,Q*=O?1:-1),m!==Be&&(m!==Me&&m!==$e||y!==Jt)||(ie=Ge,U-=(L&&se===ne&&ne.visualViewport?ne.visualViewport.width:se[ge])-d.width,U*=O?1:-1)}var fe,Le=Object.assign({position:k},P&&qr),gt=M===!0?function(Pt,et){var St=Pt.x,Ot=Pt.y,Ae=et.devicePixelRatio||1;return{x:it(St*Ae)/Ae||0,y:it(Ot*Ae)/Ae||0}}({x:U,y:Q},He(s)):{x:U,y:Q};return U=gt.x,Q=gt.y,O?Object.assign({},Le,((fe={})[ue]=ye?"0":"",fe[ie]=pe?"0":"",fe.transform=(ne.devicePixelRatio||1)<=1?"translate("+U+"px, "+Q+"px)":"translate3d("+U+"px, "+Q+"px, 0)",fe)):Object.assign({},Le,((t={})[ue]=ye?Q+"px":"",t[ie]=pe?U+"px":"",t.transform="",t))}const $n={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(l){var t=l.state,s=l.options,d=s.gpuAcceleration,m=d===void 0||d,y=s.adaptive,T=y===void 0||y,k=s.roundOffsets,O=k===void 0||k,P={placement:wt(t.placement),variation:wn(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:m,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Ii(Object.assign({},P,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:T,roundOffsets:O})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ii(Object.assign({},P,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:O})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var Hn={passive:!0};const qn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(l){var t=l.state,s=l.instance,d=l.options,m=d.scroll,y=m===void 0||m,T=d.resize,k=T===void 0||T,O=He(t.elements.popper),P=[].concat(t.scrollParents.reference,t.scrollParents.popper);return y&&P.forEach(function(M){M.addEventListener("scroll",s.update,Hn)}),k&&O.addEventListener("resize",s.update,Hn),function(){y&&P.forEach(function(M){M.removeEventListener("scroll",s.update,Hn)}),k&&O.removeEventListener("resize",s.update,Hn)}},data:{}};var vr={left:"right",right:"left",bottom:"top",top:"bottom"};function Fn(l){return l.replace(/left|right|bottom|top/g,function(t){return vr[t]})}var $i={start:"end",end:"start"};function yr(l){return l.replace(/start|end/g,function(t){return $i[t]})}function rn(l){var t=He(l);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function pi(l){return Tt(Ft(l)).left+rn(l).scrollLeft}function gi(l){var t=Et(l),s=t.overflow,d=t.overflowX,m=t.overflowY;return/auto|scroll|overlay|hidden/.test(s+m+d)}function mi(l){return["html","body","#document"].indexOf(dt(l))>=0?l.ownerDocument.body:we(l)&&gi(l)?l:mi(Nn(l))}function Rn(l,t){var s;t===void 0&&(t=[]);var d=mi(l),m=d===((s=l.ownerDocument)==null?void 0:s.body),y=He(d),T=m?[y].concat(y.visualViewport||[],gi(d)?d:[]):d,k=t.concat(T);return m?k:k.concat(Rn(Nn(T)))}function Hi(l){return Object.assign({},l,{left:l.x,top:l.y,right:l.x+l.width,bottom:l.y+l.height})}function br(l,t,s){return t===Dn?Hi(function(d,m){var y=He(d),T=Ft(d),k=y.visualViewport,O=T.clientWidth,P=T.clientHeight,M=0,L=0;if(k){O=k.width,P=k.height;var ee=dr();(ee||!ee&&m==="fixed")&&(M=k.offsetLeft,L=k.offsetTop)}return{width:O,height:P,x:M+pi(d),y:L}}(l,s)):qt(t)?function(d,m){var y=Tt(d,!1,m==="fixed");return y.top=y.top+d.clientTop,y.left=y.left+d.clientLeft,y.bottom=y.top+d.clientHeight,y.right=y.left+d.clientWidth,y.width=d.clientWidth,y.height=d.clientHeight,y.x=y.left,y.y=y.top,y}(t,s):Hi(function(d){var m,y=Ft(d),T=rn(d),k=(m=d.ownerDocument)==null?void 0:m.body,O=nn(y.scrollWidth,y.clientWidth,k?k.scrollWidth:0,k?k.clientWidth:0),P=nn(y.scrollHeight,y.clientHeight,k?k.scrollHeight:0,k?k.clientHeight:0),M=-T.scrollLeft+pi(d),L=-T.scrollTop;return Et(k||y).direction==="rtl"&&(M+=nn(y.clientWidth,k?k.clientWidth:0)-O),{width:O,height:P,x:M,y:L}}(Ft(l)))}function e(l){var t,s=l.reference,d=l.element,m=l.placement,y=m?wt(m):null,T=m?wn(m):null,k=s.x+s.width/2-d.width/2,O=s.y+s.height/2-d.height/2;switch(y){case Me:t={x:k,y:s.y-d.height};break;case $e:t={x:k,y:s.y+s.height};break;case Ge:t={x:s.x+s.width,y:O};break;case Be:t={x:s.x-d.width,y:O};break;default:t={x:s.x,y:s.y}}var P=y?Mn(y):null;if(P!=null){var M=P==="y"?"height":"width";switch(T){case Ht:t[P]=t[P]-(s[M]/2-d[M]/2);break;case Jt:t[P]=t[P]+(s[M]/2-d[M]/2)}}return t}function n(l,t){t===void 0&&(t={});var s=t,d=s.placement,m=d===void 0?l.placement:d,y=s.strategy,T=y===void 0?l.strategy:y,k=s.boundary,O=k===void 0?Ni:k,P=s.rootBoundary,M=P===void 0?Dn:P,L=s.elementContext,ee=L===void 0?_n:L,U=s.altBoundary,J=U!==void 0&&U,Q=s.padding,Z=Q===void 0?0:Q,pe=pr(typeof Z!="number"?Z:gr(Z,Gt)),ye=ee===_n?lr:_n,ie=l.rects.popper,ue=l.elements[J?ye:ee],ne=function(et,St,Ot,Ae){var Vt=St==="clippingParents"?function(ve){var tt=Rn(Nn(ve)),Dt=["absolute","fixed"].indexOf(Et(ve).position)>=0&&we(ve)?Pn(ve):ve;return qt(Dt)?tt.filter(function(Cn){return qt(Cn)&&hi(Cn,Dt)&&dt(Cn)!=="body"}):[]}(et):[].concat(St),Xt=[].concat(Vt,[Ot]),xi=Xt[0],We=Xt.reduce(function(ve,tt){var Dt=br(et,tt,Ae);return ve.top=nn(Dt.top,ve.top),ve.right=xt(Dt.right,ve.right),ve.bottom=xt(Dt.bottom,ve.bottom),ve.left=nn(Dt.left,ve.left),ve},br(et,xi,Ae));return We.width=We.right-We.left,We.height=We.bottom-We.top,We.x=We.left,We.y=We.top,We}(qt(ue)?ue:ue.contextElement||Ft(l.elements.popper),O,M,T),se=Tt(l.elements.reference),de=e({reference:se,element:ie,placement:m}),ge=Hi(Object.assign({},ie,de)),fe=ee===_n?ge:se,Le={top:ne.top-fe.top+pe.top,bottom:fe.bottom-ne.bottom+pe.bottom,left:ne.left-fe.left+pe.left,right:fe.right-ne.right+pe.right},gt=l.modifiersData.offset;if(ee===_n&&gt){var Pt=gt[m];Object.keys(Le).forEach(function(et){var St=[Ge,$e].indexOf(et)>=0?1:-1,Ot=[Me,$e].indexOf(et)>=0?"y":"x";Le[et]+=Pt[Ot]*St})}return Le}function i(l,t){t===void 0&&(t={});var s=t,d=s.placement,m=s.boundary,y=s.rootBoundary,T=s.padding,k=s.flipVariations,O=s.allowedAutoPlacements,P=O===void 0?li:O,M=wn(d),L=M?k?ai:ai.filter(function(J){return wn(J)===M}):Gt,ee=L.filter(function(J){return P.indexOf(J)>=0});ee.length===0&&(ee=L);var U=ee.reduce(function(J,Q){return J[Q]=n(l,{placement:Q,boundary:m,rootBoundary:y,padding:T})[wt(Q)],J},{});return Object.keys(U).sort(function(J,Q){return U[J]-U[Q]})}const r={name:"flip",enabled:!0,phase:"main",fn:function(l){var t=l.state,s=l.options,d=l.name;if(!t.modifiersData[d]._skip){for(var m=s.mainAxis,y=m===void 0||m,T=s.altAxis,k=T===void 0||T,O=s.fallbackPlacements,P=s.padding,M=s.boundary,L=s.rootBoundary,ee=s.altBoundary,U=s.flipVariations,J=U===void 0||U,Q=s.allowedAutoPlacements,Z=t.options.placement,pe=wt(Z),ye=O||(pe!==Z&&J?function(ve){if(wt(ve)===$t)return[];var tt=Fn(ve);return[yr(ve),tt,yr(tt)]}(Z):[Fn(Z)]),ie=[Z].concat(ye).reduce(function(ve,tt){return ve.concat(wt(tt)===$t?i(t,{placement:tt,boundary:M,rootBoundary:L,padding:P,flipVariations:J,allowedAutoPlacements:Q}):tt)},[]),ue=t.rects.reference,ne=t.rects.popper,se=new Map,de=!0,ge=ie[0],fe=0;fe<ie.length;fe++){var Le=ie[fe],gt=wt(Le),Pt=wn(Le)===Ht,et=[Me,$e].indexOf(gt)>=0,St=et?"width":"height",Ot=n(t,{placement:Le,boundary:M,rootBoundary:L,altBoundary:ee,padding:P}),Ae=et?Pt?Ge:Be:Pt?$e:Me;ue[St]>ne[St]&&(Ae=Fn(Ae));var Vt=Fn(Ae),Xt=[];if(y&&Xt.push(Ot[gt]<=0),k&&Xt.push(Ot[Ae]<=0,Ot[Vt]<=0),Xt.every(function(ve){return ve})){ge=Le,de=!1;break}se.set(Le,Xt)}if(de)for(var xi=function(ve){var tt=ie.find(function(Dt){var Cn=se.get(Dt);if(Cn)return Cn.slice(0,ve).every(function(Ar){return Ar})});if(tt)return ge=tt,"break"},We=J?3:1;We>0&&xi(We)!=="break";We--);t.placement!==ge&&(t.modifiersData[d]._skip=!0,t.placement=ge,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function a(l,t,s){return s===void 0&&(s={x:0,y:0}),{top:l.top-t.height-s.y,right:l.right-t.width+s.x,bottom:l.bottom-t.height+s.y,left:l.left-t.width-s.x}}function c(l){return[Me,Ge,$e,Be].some(function(t){return l[t]>=0})}const u={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(l){var t=l.state,s=l.name,d=t.rects.reference,m=t.rects.popper,y=t.modifiersData.preventOverflow,T=n(t,{elementContext:"reference"}),k=n(t,{altBoundary:!0}),O=a(T,d),P=a(k,m,y),M=c(O),L=c(P);t.modifiersData[s]={referenceClippingOffsets:O,popperEscapeOffsets:P,isReferenceHidden:M,hasPopperEscaped:L},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":M,"data-popper-escaped":L})}},p={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(l){var t=l.state,s=l.options,d=l.name,m=s.offset,y=m===void 0?[0,0]:m,T=li.reduce(function(M,L){return M[L]=function(ee,U,J){var Q=wt(ee),Z=[Be,Me].indexOf(Q)>=0?-1:1,pe=typeof J=="function"?J(Object.assign({},U,{placement:ee})):J,ye=pe[0],ie=pe[1];return ye=ye||0,ie=(ie||0)*Z,[Be,Ge].indexOf(Q)>=0?{x:ie,y:ye}:{x:ye,y:ie}}(L,t.rects,y),M},{}),k=T[t.placement],O=k.x,P=k.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=O,t.modifiersData.popperOffsets.y+=P),t.modifiersData[d]=T}},h={name:"popperOffsets",enabled:!0,phase:"read",fn:function(l){var t=l.state,s=l.name;t.modifiersData[s]=e({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},v={name:"preventOverflow",enabled:!0,phase:"main",fn:function(l){var t=l.state,s=l.options,d=l.name,m=s.mainAxis,y=m===void 0||m,T=s.altAxis,k=T!==void 0&&T,O=s.boundary,P=s.rootBoundary,M=s.altBoundary,L=s.padding,ee=s.tether,U=ee===void 0||ee,J=s.tetherOffset,Q=J===void 0?0:J,Z=n(t,{boundary:O,rootBoundary:P,padding:L,altBoundary:M}),pe=wt(t.placement),ye=wn(t.placement),ie=!ye,ue=Mn(pe),ne=ue==="x"?"y":"x",se=t.modifiersData.popperOffsets,de=t.rects.reference,ge=t.rects.popper,fe=typeof Q=="function"?Q(Object.assign({},t.rects,{placement:t.placement})):Q,Le=typeof fe=="number"?{mainAxis:fe,altAxis:fe}:Object.assign({mainAxis:0,altAxis:0},fe),gt=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,Pt={x:0,y:0};if(se){if(y){var et,St=ue==="y"?Me:Be,Ot=ue==="y"?$e:Ge,Ae=ue==="y"?"height":"width",Vt=se[ue],Xt=Vt+Z[St],xi=Vt-Z[Ot],We=U?-ge[Ae]/2:0,ve=ye===Ht?de[Ae]:ge[Ae],tt=ye===Ht?-ge[Ae]:-de[Ae],Dt=t.elements.arrow,Cn=U&&Dt?fi(Dt):{width:0,height:0},Ar=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},ho=Ar[St],po=Ar[Ot],kr=In(0,de[Ae],Cn[Ae]),_s=ie?de[Ae]/2-We-kr-ho-Le.mainAxis:ve-kr-ho-Le.mainAxis,ws=ie?-de[Ae]/2+We+kr+po+Le.mainAxis:tt+kr+po+Le.mainAxis,Yr=t.elements.arrow&&Pn(t.elements.arrow),xs=Yr?ue==="y"?Yr.clientTop||0:Yr.clientLeft||0:0,go=(et=gt?.[ue])!=null?et:0,Ts=Vt+ws-go,mo=In(U?xt(Xt,Vt+_s-go-xs):Xt,Vt,U?nn(xi,Ts):xi);se[ue]=mo,Pt[ue]=mo-Vt}if(k){var vo,Es=ue==="x"?Me:Be,Cs=ue==="x"?$e:Ge,Qn=se[ne],Sr=ne==="y"?"height":"width",yo=Qn+Z[Es],bo=Qn-Z[Cs],Qr=[Me,Be].indexOf(pe)!==-1,_o=(vo=gt?.[ne])!=null?vo:0,wo=Qr?yo:Qn-de[Sr]-ge[Sr]-_o+Le.altAxis,xo=Qr?Qn+de[Sr]+ge[Sr]-_o-Le.altAxis:bo,To=U&&Qr?function(As,ks,Kr){var Eo=In(As,ks,Kr);return Eo>Kr?Kr:Eo}(wo,Qn,xo):In(U?wo:yo,Qn,U?xo:bo);se[ne]=To,Pt[ne]=To-Qn}t.modifiersData[d]=Pt}},requiresIfExists:["offset"]};function w(l,t,s){s===void 0&&(s=!1);var d,m,y=we(t),T=we(t)&&function(L){var ee=L.getBoundingClientRect(),U=it(ee.width)/L.offsetWidth||1,J=it(ee.height)/L.offsetHeight||1;return U!==1||J!==1}(t),k=Ft(t),O=Tt(l,T,s),P={scrollLeft:0,scrollTop:0},M={x:0,y:0};return(y||!y&&!s)&&((dt(t)!=="body"||gi(k))&&(P=(d=t)!==He(d)&&we(d)?{scrollLeft:(m=d).scrollLeft,scrollTop:m.scrollTop}:rn(d)),we(t)?((M=Tt(t,!0)).x+=t.clientLeft,M.y+=t.clientTop):k&&(M.x=pi(k))),{x:O.left+P.scrollLeft-M.x,y:O.top+P.scrollTop-M.y,width:O.width,height:O.height}}function C(l){var t=new Map,s=new Set,d=[];function m(y){s.add(y.name),[].concat(y.requires||[],y.requiresIfExists||[]).forEach(function(T){if(!s.has(T)){var k=t.get(T);k&&m(k)}}),d.push(y)}return l.forEach(function(y){t.set(y.name,y)}),l.forEach(function(y){s.has(y.name)||m(y)}),d}var b={placement:"bottom",modifiers:[],strategy:"absolute"};function A(){for(var l=arguments.length,t=new Array(l),s=0;s<l;s++)t[s]=arguments[s];return!t.some(function(d){return!(d&&typeof d.getBoundingClientRect=="function")})}function I(l){l===void 0&&(l={});var t=l,s=t.defaultModifiers,d=s===void 0?[]:s,m=t.defaultOptions,y=m===void 0?b:m;return function(T,k,O){O===void 0&&(O=y);var P,M,L={placement:"bottom",orderedModifiers:[],options:Object.assign({},b,y),modifiersData:{},elements:{reference:T,popper:k},attributes:{},styles:{}},ee=[],U=!1,J={state:L,setOptions:function(Z){var pe=typeof Z=="function"?Z(L.options):Z;Q(),L.options=Object.assign({},y,L.options,pe),L.scrollParents={reference:qt(T)?Rn(T):T.contextElement?Rn(T.contextElement):[],popper:Rn(k)};var ye,ie,ue=function(ne){var se=C(ne);return ui.reduce(function(de,ge){return de.concat(se.filter(function(fe){return fe.phase===ge}))},[])}((ye=[].concat(d,L.options.modifiers),ie=ye.reduce(function(ne,se){var de=ne[se.name];return ne[se.name]=de?Object.assign({},de,se,{options:Object.assign({},de.options,se.options),data:Object.assign({},de.data,se.data)}):se,ne},{}),Object.keys(ie).map(function(ne){return ie[ne]})));return L.orderedModifiers=ue.filter(function(ne){return ne.enabled}),L.orderedModifiers.forEach(function(ne){var se=ne.name,de=ne.options,ge=de===void 0?{}:de,fe=ne.effect;if(typeof fe=="function"){var Le=fe({state:L,name:se,instance:J,options:ge});ee.push(Le||function(){})}}),J.update()},forceUpdate:function(){if(!U){var Z=L.elements,pe=Z.reference,ye=Z.popper;if(A(pe,ye)){L.rects={reference:w(pe,Pn(ye),L.options.strategy==="fixed"),popper:fi(ye)},L.reset=!1,L.placement=L.options.placement,L.orderedModifiers.forEach(function(fe){return L.modifiersData[fe.name]=Object.assign({},fe.data)});for(var ie=0;ie<L.orderedModifiers.length;ie++)if(L.reset!==!0){var ue=L.orderedModifiers[ie],ne=ue.fn,se=ue.options,de=se===void 0?{}:se,ge=ue.name;typeof ne=="function"&&(L=ne({state:L,options:de,name:ge,instance:J})||L)}else L.reset=!1,ie=-1}}},update:(P=function(){return new Promise(function(Z){J.forceUpdate(),Z(L)})},function(){return M||(M=new Promise(function(Z){Promise.resolve().then(function(){M=void 0,Z(P())})})),M}),destroy:function(){Q(),U=!0}};if(!A(T,k))return J;function Q(){ee.forEach(function(Z){return Z()}),ee=[]}return J.setOptions(O).then(function(Z){!U&&O.onFirstUpdate&&O.onFirstUpdate(Z)}),J}}var V=I(),X=I({defaultModifiers:[qn,h,$n,tn]}),be=I({defaultModifiers:[qn,h,$n,tn,p,r,v,mr,u]});const Se=Object.freeze(Object.defineProperty({__proto__:null,afterMain:en,afterRead:nt,afterWrite:ur,applyStyles:tn,arrow:mr,auto:$t,basePlacements:Gt,beforeMain:Zt,beforeRead:ci,beforeWrite:Mi,bottom:$e,clippingParents:Ni,computeStyles:$n,createPopper:be,createPopperBase:V,createPopperLite:X,detectOverflow:n,end:Jt,eventListeners:qn,flip:r,hide:u,left:Be,main:Ln,modifierPhases:ui,offset:p,placements:li,popper:_n,popperGenerator:I,popperOffsets:h,preventOverflow:v,read:Pi,reference:lr,right:Ge,start:Ht,top:Me,variationPlacements:ai,viewport:Dn,write:cr},Symbol.toStringTag,{value:"Module"})),ft="dropdown",qe=".bs.dropdown",te=".data-api",on="ArrowUp",re="ArrowDown",xe=`hide${qe}`,Wn=`hidden${qe}`,qi=`show${qe}`,Ct=`shown${qe}`,xn=`click${qe}${te}`,At=`keydown${qe}${te}`,Rt=`keyup${qe}${te}`,ht="show",je='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Ne=`${je}.${ht}`,_e=".dropdown-menu",ze=B()?"top-end":"top-start",Wt=B()?"top-start":"top-end",Ve=B()?"bottom-end":"bottom-start",W=B()?"bottom-start":"bottom-end",Ee=B()?"left-start":"right-start",Ce=B()?"right-start":"left-start",me={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},kt={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Fe extends ct{constructor(t,s){super(t,s),this._popper=null,this._parent=this._element.parentNode,this._menu=R.next(this._element,_e)[0]||R.prev(this._element,_e)[0]||R.findOne(_e,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return me}static get DefaultType(){return kt}static get NAME(){return ft}toggle(){return this._isShown()?this.hide():this.show()}show(){if(at(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!S.trigger(this._element,qi,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const s of[].concat(...document.body.children))S.on(s,"mouseover",Mt);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(ht),this._element.classList.add(ht),S.trigger(this._element,Ct,t)}}hide(){if(at(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!S.trigger(this._element,xe,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))S.off(s,"mouseover",Mt);this._popper&&this._popper.destroy(),this._menu.classList.remove(ht),this._element.classList.remove(ht),this._element.setAttribute("aria-expanded","false"),lt.removeDataAttribute(this._menu,"popper"),S.trigger(this._element,Wn,t)}}_getConfig(t){if(typeof(t=super._getConfig(t)).reference=="object"&&!mt(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${ft.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(Se===void 0)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let t=this._element;this._config.reference==="parent"?t=this._parent:mt(this._config.reference)?t=st(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const s=this._getPopperConfig();this._popper=be(t,this._menu,s)}_isShown(){return this._menu.classList.contains(ht)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Ee;if(t.classList.contains("dropstart"))return Ce;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const s=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains("dropup")?s?Wt:ze:s?W:Ve}_detectNavbar(){return this._element.closest(".navbar")!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(s=>Number.parseInt(s,10)):typeof t=="function"?s=>t(s,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(lt.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...q(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:s}){const d=R.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(m=>Ye(m));d.length&&Kn(d,s,t===re,!d.includes(s)).focus()}static jQueryInterface(t){return this.each(function(){const s=Fe.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t]()}})}static clearMenus(t){if(t.button===2||t.type==="keyup"&&t.key!=="Tab")return;const s=R.find(Ne);for(const d of s){const m=Fe.getInstance(d);if(!m||m._config.autoClose===!1)continue;const y=t.composedPath(),T=y.includes(m._menu);if(y.includes(m._element)||m._config.autoClose==="inside"&&!T||m._config.autoClose==="outside"&&T||m._menu.contains(t.target)&&(t.type==="keyup"&&t.key==="Tab"||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const k={relatedTarget:m._element};t.type==="click"&&(k.clickEvent=t),m._completeHide(k)}}static dataApiKeydownHandler(t){const s=/input|textarea/i.test(t.target.tagName),d=t.key==="Escape",m=[on,re].includes(t.key);if(!m&&!d||s&&!d)return;t.preventDefault();const y=this.matches(je)?this:R.prev(this,je)[0]||R.next(this,je)[0]||R.findOne(je,t.delegateTarget.parentNode),T=Fe.getOrCreateInstance(y);if(m)return t.stopPropagation(),T.show(),void T._selectMenuItem(t);T._isShown()&&(t.stopPropagation(),T.hide(),y.focus())}}S.on(document,At,je,Fe.dataApiKeydownHandler),S.on(document,At,_e,Fe.dataApiKeydownHandler),S.on(document,xn,Fe.clearMenus),S.on(document,Rt,Fe.clearMenus),S.on(document,xn,je,function(l){l.preventDefault(),Fe.getOrCreateInstance(this).toggle()}),Pe(Fe);const Bn="backdrop",Bt="show",vi=`mousedown.bs.${Bn}`,zt={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},_r={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Tn extends dn{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return zt}static get DefaultType(){return _r}static get NAME(){return Bn}show(t){if(!this._config.isVisible)return void q(t);this._append();const s=this._getElement();this._config.isAnimated&&ln(s),s.classList.add(Bt),this._emulateAnimation(()=>{q(t)})}hide(t){this._config.isVisible?(this._getElement().classList.remove(Bt),this._emulateAnimation(()=>{this.dispose(),q(t)})):q(t)}dispose(){this._isAppended&&(S.off(this._element,vi),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=st(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),S.on(t,vi,()=>{q(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){Ki(t,this._getElement(),this._config.isAnimated)}}const sn=".bs.focustrap",yi=`focusin${sn}`,Fi=`keydown.tab${sn}`,zn="backward",Ri={autofocus:!0,trapElement:null},Wi={autofocus:"boolean",trapElement:"element"};class bi extends dn{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Ri}static get DefaultType(){return Wi}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),S.off(document,sn),S.on(document,yi,t=>this._handleFocusin(t)),S.on(document,Fi,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,S.off(document,sn))}_handleFocusin(t){const{trapElement:s}=this._config;if(t.target===document||t.target===s||s.contains(t.target))return;const d=R.focusableChildren(s);d.length===0?s.focus():this._lastTabNavDirection===zn?d[d.length-1].focus():d[0].focus()}_handleKeydown(t){t.key==="Tab"&&(this._lastTabNavDirection=t.shiftKey?zn:"forward")}}const Bi=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",f=".sticky-top",g="padding-right",_="margin-right";class x{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,g,s=>s+t),this._setElementAttributes(Bi,g,s=>s+t),this._setElementAttributes(f,_,s=>s-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,g),this._resetElementAttributes(Bi,g),this._resetElementAttributes(f,_)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,s,d){const m=this.getWidth();this._applyManipulationCallback(t,y=>{if(y!==this._element&&window.innerWidth>y.clientWidth+m)return;this._saveInitialAttribute(y,s);const T=window.getComputedStyle(y).getPropertyValue(s);y.style.setProperty(s,`${d(Number.parseFloat(T))}px`)})}_saveInitialAttribute(t,s){const d=t.style.getPropertyValue(s);d&&lt.setDataAttribute(t,s,d)}_resetElementAttributes(t,s){this._applyManipulationCallback(t,d=>{const m=lt.getDataAttribute(d,s);m!==null?(lt.removeDataAttribute(d,s),d.style.setProperty(s,m)):d.style.removeProperty(s)})}_applyManipulationCallback(t,s){if(mt(t))s(t);else for(const d of R.find(t,this._element))s(d)}}const E=".bs.modal",j=`hide${E}`,N=`hidePrevented${E}`,$=`hidden${E}`,D=`show${E}`,ae=`shown${E}`,Y=`resize${E}`,G=`click.dismiss${E}`,oe=`mousedown.dismiss${E}`,z=`keydown.dismiss${E}`,Oe=`click${E}.data-api`,Ie="modal-open",De="show",Re="modal-static",Ze={backdrop:!0,focus:!0,keyboard:!0},pt={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class le extends ct{constructor(t,s){super(t,s),this._dialog=R.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new x,this._addEventListeners()}static get Default(){return Ze}static get DefaultType(){return pt}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||S.trigger(this._element,D,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Ie),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){this._isShown&&!this._isTransitioning&&(S.trigger(this._element,j).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(De),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){S.off(window,E),S.off(this._dialog,E),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Tn({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new bi({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const s=R.findOne(".modal-body",this._dialog);s&&(s.scrollTop=0),ln(this._element),this._element.classList.add(De),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,S.trigger(this._element,ae,{relatedTarget:t})},this._dialog,this._isAnimated())}_addEventListeners(){S.on(this._element,z,t=>{t.key==="Escape"&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),S.on(window,Y,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),S.on(this._element,oe,t=>{S.one(this._element,G,s=>{this._element===t.target&&this._element===s.target&&(this._config.backdrop!=="static"?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Ie),this._resetAdjustments(),this._scrollBar.reset(),S.trigger(this._element,$)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(S.trigger(this._element,N).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,s=this._element.style.overflowY;s==="hidden"||this._element.classList.contains(Re)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Re),this._queueCallback(()=>{this._element.classList.remove(Re),this._queueCallback(()=>{this._element.style.overflowY=s},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,s=this._scrollBar.getWidth(),d=s>0;if(d&&!t){const m=B()?"paddingLeft":"paddingRight";this._element.style[m]=`${s}px`}if(!d&&t){const m=B()?"paddingRight":"paddingLeft";this._element.style[m]=`${s}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,s){return this.each(function(){const d=le.getOrCreateInstance(this,t);if(typeof t=="string"){if(d[t]===void 0)throw new TypeError(`No method named "${t}"`);d[t](s)}})}}S.on(document,Oe,'[data-bs-toggle="modal"]',function(l){const t=R.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&l.preventDefault(),S.one(t,D,d=>{d.defaultPrevented||S.one(t,$,()=>{Ye(this)&&this.focus()})});const s=R.findOne(".modal.show");s&&le.getInstance(s).hide(),le.getOrCreateInstance(t).toggle(this)}),An(le),Pe(le);const Xe=".bs.offcanvas",rt=".data-api",_i=`load${Xe}${rt}`,zi="show",Vi="showing",wr="hiding",Gr=".offcanvas.show",Co=`show${Xe}`,Ao=`shown${Xe}`,ko=`hide${Xe}`,Jr=`hidePrevented${Xe}`,Zr=`hidden${Xe}`,So=`resize${Xe}`,Oo=`click${Xe}${rt}`,Do=`keydown.dismiss${Xe}`,Lo={backdrop:!0,keyboard:!0,scroll:!1},jo={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class an extends ct{constructor(t,s){super(t,s),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Lo}static get DefaultType(){return jo}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||S.trigger(this._element,Co,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||new x().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Vi),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(zi),this._element.classList.remove(Vi),S.trigger(this._element,Ao,{relatedTarget:t})},this._element,!0))}hide(){this._isShown&&(S.trigger(this._element,ko).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(wr),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(zi,wr),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new x().reset(),S.trigger(this._element,Zr)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=!!this._config.backdrop;return new Tn({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{this._config.backdrop!=="static"?this.hide():S.trigger(this._element,Jr)}:null})}_initializeFocusTrap(){return new bi({trapElement:this._element})}_addEventListeners(){S.on(this._element,Do,t=>{t.key==="Escape"&&(this._config.keyboard?this.hide():S.trigger(this._element,Jr))})}static jQueryInterface(t){return this.each(function(){const s=an.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t](this)}})}}S.on(document,Oo,'[data-bs-toggle="offcanvas"]',function(l){const t=R.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&l.preventDefault(),at(this))return;S.one(t,Zr,()=>{Ye(this)&&this.focus()});const s=R.findOne(Gr);s&&s!==t&&an.getInstance(s).hide(),an.getOrCreateInstance(t).toggle(this)}),S.on(window,_i,()=>{for(const l of R.find(Gr))an.getOrCreateInstance(l).show()}),S.on(window,So,()=>{for(const l of R.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(l).position!=="fixed"&&an.getOrCreateInstance(l).hide()}),An(an),Pe(an);const eo={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},No=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Po=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Mo=(l,t)=>{const s=l.nodeName.toLowerCase();return t.includes(s)?!No.has(s)||!!Po.test(l.nodeValue):t.filter(d=>d instanceof RegExp).some(d=>d.test(s))},Io={allowList:eo,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},$o={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Ho={entry:"(string|element|function|null)",selector:"(string|element)"};class qo extends dn{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Io}static get DefaultType(){return $o}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[m,y]of Object.entries(this._config.content))this._setContent(t,y,m);const s=t.children[0],d=this._resolvePossibleFunction(this._config.extraClass);return d&&s.classList.add(...d.split(" ")),s}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[s,d]of Object.entries(t))super._typeCheckConfig({selector:s,entry:d},Ho)}_setContent(t,s,d){const m=R.findOne(d,t);m&&((s=this._resolvePossibleFunction(s))?mt(s)?this._putElementInTemplate(st(s),m):this._config.html?m.innerHTML=this._maybeSanitize(s):m.textContent=s:m.remove())}_maybeSanitize(t){return this._config.sanitize?function(s,d,m){if(!s.length)return s;if(m&&typeof m=="function")return m(s);const y=new window.DOMParser().parseFromString(s,"text/html"),T=[].concat(...y.body.querySelectorAll("*"));for(const k of T){const O=k.nodeName.toLowerCase();if(!Object.keys(d).includes(O)){k.remove();continue}const P=[].concat(...k.attributes),M=[].concat(d["*"]||[],d[O]||[]);for(const L of P)Mo(L,M)||k.removeAttribute(L.nodeName)}return y.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return q(t,[void 0,this])}_putElementInTemplate(t,s){if(this._config.html)return s.innerHTML="",void s.append(t);s.textContent=t.textContent}}const Fo=new Set(["sanitize","allowList","sanitizeFn"]),Fr="fade",xr="show",Ro=".tooltip-inner",to=".modal",no="hide.bs.modal",Xi="hover",Rr="focus",Wo={AUTO:"auto",TOP:"top",RIGHT:B()?"left":"right",BOTTOM:"bottom",LEFT:B()?"right":"left"},Bo={allowList:eo,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},zo={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Vn extends ct{constructor(t,s){if(Se===void 0)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,s),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Bo}static get DefaultType(){return zo}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),S.off(this._element.closest(to),no,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=S.trigger(this._element,this.constructor.eventName("show")),s=(Ei(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!s)return;this._disposePopper();const d=this._getTipElement();this._element.setAttribute("aria-describedby",d.getAttribute("id"));const{container:m}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(m.append(d),S.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(d),d.classList.add(xr),"ontouchstart"in document.documentElement)for(const y of[].concat(...document.body.children))S.on(y,"mouseover",Mt);this._queueCallback(()=>{S.trigger(this._element,this.constructor.eventName("shown")),this._isHovered===!1&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!S.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(xr),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))S.off(t,"mouseover",Mt);this._activeTrigger.click=!1,this._activeTrigger[Rr]=!1,this._activeTrigger[Xi]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),S.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const s=this._getTemplateFactory(t).toHtml();if(!s)return null;s.classList.remove(Fr,xr),s.classList.add(`bs-${this.constructor.NAME}-auto`);const d=(m=>{do m+=Math.floor(1e6*Math.random());while(document.getElementById(m));return m})(this.constructor.NAME).toString();return s.setAttribute("id",d),this._isAnimated()&&s.classList.add(Fr),s}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new qo({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Ro]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Fr)}_isShown(){return this.tip&&this.tip.classList.contains(xr)}_createPopper(t){const s=q(this._config.placement,[this,t,this._element]),d=Wo[s.toUpperCase()];return be(this._element,t,this._getPopperConfig(d))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(s=>Number.parseInt(s,10)):typeof t=="function"?s=>t(s,this._element):t}_resolvePossibleFunction(t){return q(t,[this._element,this._element])}_getPopperConfig(t){const s={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:d=>{this._getTipElement().setAttribute("data-popper-placement",d.state.placement)}}]};return{...s,...q(this._config.popperConfig,[void 0,s])}}_setListeners(){const t=this._config.trigger.split(" ");for(const s of t)if(s==="click")S.on(this._element,this.constructor.eventName("click"),this._config.selector,d=>{this._initializeOnDelegatedTarget(d).toggle()});else if(s!=="manual"){const d=s===Xi?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),m=s===Xi?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");S.on(this._element,d,this._config.selector,y=>{const T=this._initializeOnDelegatedTarget(y);T._activeTrigger[y.type==="focusin"?Rr:Xi]=!0,T._enter()}),S.on(this._element,m,this._config.selector,y=>{const T=this._initializeOnDelegatedTarget(y);T._activeTrigger[y.type==="focusout"?Rr:Xi]=T._element.contains(y.relatedTarget),T._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},S.on(this._element.closest(to),no,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,s){clearTimeout(this._timeout),this._timeout=setTimeout(t,s)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const s=lt.getDataAttributes(this._element);for(const d of Object.keys(s))Fo.has(d)&&delete s[d];return t={...s,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:st(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[s,d]of Object.entries(this._config))this.constructor.Default[s]!==d&&(t[s]=d);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const s=Vn.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t]()}})}}Pe(Vn);const Vo=".popover-header",Xo=".popover-body",Uo={...Vn.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Yo={...Vn.DefaultType,content:"(null|string|element|function)"};class Tr extends Vn{static get Default(){return Uo}static get DefaultType(){return Yo}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Vo]:this._getTitle(),[Xo]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const s=Tr.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t]()}})}}Pe(Tr);const Wr=".bs.scrollspy",Qo=`activate${Wr}`,io=`click${Wr}`,Ko=`load${Wr}.data-api`,wi="active",Br="[href]",ro=".nav-link",Go=`${ro}, .nav-item > ${ro}, .list-group-item`,Jo={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Zo={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ui extends ct{constructor(t,s){super(t,s),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Jo}static get DefaultType(){return Zo}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=st(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(s=>Number.parseFloat(s))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(S.off(this._config.target,io),S.on(this._config.target,io,Br,t=>{const s=this._observableSections.get(t.target.hash);if(s){t.preventDefault();const d=this._rootElement||window,m=s.offsetTop-this._element.offsetTop;if(d.scrollTo)return void d.scrollTo({top:m,behavior:"smooth"});d.scrollTop=m}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(s=>this._observerCallback(s),t)}_observerCallback(t){const s=T=>this._targetLinks.get(`#${T.target.id}`),d=T=>{this._previousScrollData.visibleEntryTop=T.target.offsetTop,this._process(s(T))},m=(this._rootElement||document.documentElement).scrollTop,y=m>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=m;for(const T of t){if(!T.isIntersecting){this._activeTarget=null,this._clearActiveClass(s(T));continue}const k=T.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(y&&k){if(d(T),!m)return}else y||k||d(T)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=R.find(Br,this._config.target);for(const s of t){if(!s.hash||at(s))continue;const d=R.findOne(decodeURI(s.hash),this._element);Ye(d)&&(this._targetLinks.set(decodeURI(s.hash),s),this._observableSections.set(s.hash,d))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(wi),this._activateParents(t),S.trigger(this._element,Qo,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))R.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(wi);else for(const s of R.parents(t,".nav, .list-group"))for(const d of R.prev(s,Go))d.classList.add(wi)}_clearActiveClass(t){t.classList.remove(wi);const s=R.find(`${Br}.${wi}`,t);for(const d of s)d.classList.remove(wi)}static jQueryInterface(t){return this.each(function(){const s=Ui.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t]()}})}}S.on(window,Ko,()=>{for(const l of R.find('[data-bs-spy="scroll"]'))Ui.getOrCreateInstance(l)}),Pe(Ui);const Xn=".bs.tab",es=`hide${Xn}`,ts=`hidden${Xn}`,ns=`show${Xn}`,is=`shown${Xn}`,rs=`click${Xn}`,os=`keydown${Xn}`,ss=`load${Xn}`,as="ArrowLeft",oo="ArrowRight",ls="ArrowUp",so="ArrowDown",zr="Home",ao="End",Un="active",lo="fade",Vr="show",co=".dropdown-toggle",Xr=`:not(${co})`,uo='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Ur=`.nav-link${Xr}, .list-group-item${Xr}, [role="tab"]${Xr}, ${uo}`,cs=`.${Un}[data-bs-toggle="tab"], .${Un}[data-bs-toggle="pill"], .${Un}[data-bs-toggle="list"]`;class Yn extends ct{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),S.on(this._element,os,s=>this._keydown(s)))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const s=this._getActiveElem(),d=s?S.trigger(s,es,{relatedTarget:t}):null;S.trigger(t,ns,{relatedTarget:s}).defaultPrevented||d&&d.defaultPrevented||(this._deactivate(s,t),this._activate(t,s))}_activate(t,s){t&&(t.classList.add(Un),this._activate(R.getElementFromSelector(t)),this._queueCallback(()=>{t.getAttribute("role")==="tab"?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),S.trigger(t,is,{relatedTarget:s})):t.classList.add(Vr)},t,t.classList.contains(lo)))}_deactivate(t,s){t&&(t.classList.remove(Un),t.blur(),this._deactivate(R.getElementFromSelector(t)),this._queueCallback(()=>{t.getAttribute("role")==="tab"?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),S.trigger(t,ts,{relatedTarget:s})):t.classList.remove(Vr)},t,t.classList.contains(lo)))}_keydown(t){if(![as,oo,ls,so,zr,ao].includes(t.key))return;t.stopPropagation(),t.preventDefault();const s=this._getChildren().filter(m=>!at(m));let d;if([zr,ao].includes(t.key))d=s[t.key===zr?0:s.length-1];else{const m=[oo,so].includes(t.key);d=Kn(s,t.target,m,!0)}d&&(d.focus({preventScroll:!0}),Yn.getOrCreateInstance(d).show())}_getChildren(){return R.find(Ur,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,s){this._setAttributeIfNotExists(t,"role","tablist");for(const d of s)this._setInitialAttributesOnChild(d)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const s=this._elemIsActive(t),d=this._getOuterElement(t);t.setAttribute("aria-selected",s),d!==t&&this._setAttributeIfNotExists(d,"role","presentation"),s||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const s=R.getElementFromSelector(t);s&&(this._setAttributeIfNotExists(s,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(s,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,s){const d=this._getOuterElement(t);if(!d.classList.contains("dropdown"))return;const m=(y,T)=>{const k=R.findOne(y,d);k&&k.classList.toggle(T,s)};m(co,Un),m(".dropdown-menu",Vr),d.setAttribute("aria-expanded",s)}_setAttributeIfNotExists(t,s,d){t.hasAttribute(s)||t.setAttribute(s,d)}_elemIsActive(t){return t.classList.contains(Un)}_getInnerElement(t){return t.matches(Ur)?t:R.findOne(Ur,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each(function(){const s=Yn.getOrCreateInstance(this);if(typeof t=="string"){if(s[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);s[t]()}})}}S.on(document,rs,uo,function(l){["A","AREA"].includes(this.tagName)&&l.preventDefault(),at(this)||Yn.getOrCreateInstance(this).show()}),S.on(window,ss,()=>{for(const l of R.find(cs))Yn.getOrCreateInstance(l)}),Pe(Yn);const En=".bs.toast",us=`mouseover${En}`,ds=`mouseout${En}`,fs=`focusin${En}`,hs=`focusout${En}`,ps=`hide${En}`,gs=`hidden${En}`,ms=`show${En}`,vs=`shown${En}`,fo="hide",Er="show",Cr="showing",ys={animation:"boolean",autohide:"boolean",delay:"number"},bs={animation:!0,autohide:!0,delay:5e3};class Yi extends ct{constructor(t,s){super(t,s),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return bs}static get DefaultType(){return ys}static get NAME(){return"toast"}show(){S.trigger(this._element,ms).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(fo),ln(this._element),this._element.classList.add(Er,Cr),this._queueCallback(()=>{this._element.classList.remove(Cr),S.trigger(this._element,vs),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(S.trigger(this._element,ps).defaultPrevented||(this._element.classList.add(Cr),this._queueCallback(()=>{this._element.classList.add(fo),this._element.classList.remove(Cr,Er),S.trigger(this._element,gs)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Er),super.dispose()}isShown(){return this._element.classList.contains(Er)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,s){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=s;break;case"focusin":case"focusout":this._hasKeyboardInteraction=s}if(s)return void this._clearTimeout();const d=t.relatedTarget;this._element===d||this._element.contains(d)||this._maybeScheduleHide()}_setListeners(){S.on(this._element,us,t=>this._onInteraction(t,!0)),S.on(this._element,ds,t=>this._onInteraction(t,!1)),S.on(this._element,fs,t=>this._onInteraction(t,!0)),S.on(this._element,hs,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const s=Yi.getOrCreateInstance(this,t);if(typeof t=="string"){if(s[t]===void 0)throw new TypeError(`No method named "${t}"`);s[t](this)}})}}return An(Yi),Pe(Yi),{Alert:hn,Button:Ut,Carousel:Qt,Collapse:It,Dropdown:Fe,Modal:le,Offcanvas:an,Popover:Tr,ScrollSpy:Ui,Tab:Yn,Toast:Yi,Tooltip:Vn}});
