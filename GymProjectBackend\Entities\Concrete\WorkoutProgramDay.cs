using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class WorkoutProgramDay : ICompanyEntity
    {
        [Key]
        public int WorkoutProgramDayID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int CompanyID { get; set; }
        public int DayNumber { get; set; } // 1, 2, 3, 4, 5, 6, 7
        public string DayName { get; set; } // <PERSON><PERSON><PERSON><PERSON>s-Triceps, Sırt-Biceps, Dinlenme vb.
        public bool IsRestDay { get; set; } // Dinlenme günü mü?
        public DateTime? CreationDate { get; set; }
    }
}
