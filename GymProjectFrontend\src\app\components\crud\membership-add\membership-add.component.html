<div class="content">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    >
  </app-loading-spinner>

  <div class="col-md-12" [class.content-blur]="isLoading">
    <div class="card">
      <div class="card-header">
        <h5 class="title">Üyelik Paneli</h5>
      </div>
      <!-- Üyelik türü yoksa genel uyarı mesajı -->
      <div class="alert alert-warning m-3" *ngIf="membershipTypes && membershipTypes.length === 0">
        <div class="d-flex align-items-center">
          <i class="fas fa-exclamation-triangle me-3 fa-2x"></i>
          <div>
            <h5 class="mb-1">Üyelik Türü Bulunamadı</h5>
            <p class="mb-2">Üyelik eklemek için önce üyelik türü tanımlamanız gerekmektedir.</p>
            <button type="button" class="btn btn-primary btn-sm" (click)="navigateToMembershipTypeAdd()">
              <i class="fas fa-plus-circle me-1"></i> Üyelik Türü Ekle
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <form [formGroup]="membershipAddForm">
          <div class="row">
            <div class="col-md-4 mb-3">
              <label for="memberID">Ad - Telefon*</label>
              <div class="form-group">
                <input
                  type="text"
                  id="memberID"
                  formControlName="memberID"
                  class="form-control"
                  [matAutocomplete]="autoMember"
                  required
                />
                <mat-autocomplete #autoMember="matAutocomplete" [displayWith]="displayMember">
                  <mat-option *ngFor="let member of filteredMembers | async" [value]="member">
                    {{ member.name }} - {{ member.phoneNumber }}
                  </mat-option>
                </mat-autocomplete>
              </div>
            </div>

            <div class="col-md-4 mb-3">
              <label for="membershipTypeID">Üyelik Türü*</label>
              <div class="form-group">
                <input
                  type="text"
                  id="membershipTypeID"
                  formControlName="membershipTypeID"
                  class="form-control"
                  [class.is-invalid]="membershipAddForm.get('membershipTypeID')?.invalid && membershipAddForm.get('membershipTypeID')?.touched"
                  [matAutocomplete]="autoMembershipType"
                  (click)="showBranchList = true"
                  placeholder="Üyelik türü yazın veya seçin..."
                  required
                />
                <mat-autocomplete #autoMembershipType="matAutocomplete" [displayWith]="displayMembershipType">
                  <div *ngIf="showBranchList">
                    <mat-option
                      *ngFor="let membershipType of filteredMembershipTypes | async"
                      [value]="membershipType.branch + ' - ' + membershipType.typeName"
                    >
                      {{ membershipType.branch }} - {{ membershipType.typeName }}
                    </mat-option>

                    <!-- Üyelik türü yoksa bilgilendirme mesajı ve yönlendirme butonu -->
                    <mat-option *ngIf="membershipTypes && membershipTypes.length === 0" [disabled]="true" class="no-membership-type-option">
                      <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Henüz üyelik türü tanımlanmamış. Önce üyelik türü eklemelisiniz.
                      </div>
                      <button type="button" class="btn btn-primary btn-sm mt-2" (click)="navigateToMembershipTypeAdd()">
                        <i class="fas fa-plus-circle me-1"></i> Üyelik Türü Ekle
                      </button>
                    </mat-option>

                    <!-- Filtreleme sonucu bulunamadığında -->
                    <mat-option *ngIf="(filteredMembershipTypes | async)?.length === 0 && membershipTypes.length > 0" [disabled]="true">
                      <div class="text-muted text-center py-2">
                        <i class="fas fa-search me-2"></i>
                        Aradığınız kriterlere uygun üyelik türü bulunamadı
                      </div>
                    </mat-option>
                  </div>
                </mat-autocomplete>

                <!-- Hata mesajları -->
                <div class="invalid-feedback" *ngIf="membershipAddForm.get('membershipTypeID')?.invalid && membershipAddForm.get('membershipTypeID')?.touched">
                  <div *ngIf="membershipAddForm.get('membershipTypeID')?.errors?.['required']">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    Üyelik türü seçimi zorunludur
                  </div>
                  <div *ngIf="membershipAddForm.get('membershipTypeID')?.errors?.['invalidSelection']">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Lütfen listeden geçerli bir üyelik türü seçiniz
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4 mb-3">
              <div class="form-group">
                <label>Başlangıç Tarihi*</label>
                <input
                  type="date"
                  class="form-control"
                  formControlName="startDate"
                  required
                >
              </div>
              <div class="last-membership-info" *ngIf="lastMembershipInfo">
                <small>{{ lastMembershipInfo }}</small>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <label for="day">Gün Sayısı</label>
              <div class="form-group">
                <input
                  type="number"
                  id="day"
                  formControlName="day"
                  class="form-control"
                />
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <label for="price">Ücret</label>
              <div class="form-group">
                <input
                  type="number"
                  id="price"
                  formControlName="price"
                  class="form-control"
                />
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <label for="PaymentMethod">Ödeme Türü*</label>
              <div class="form-group">
                <select
                  id="PaymentMethod"
                  formControlName="PaymentMethod"
                  class="form-control"
                  required
                >
                  <option value="">Seçiniz</option>
                  <option value="Nakit">Nakit</option>
                  <option value="Kredi Kartı">Kredi Kartı</option>
                  <option value="Havale - EFT">Havale - EFT</option>
                  <option value="Borç">Borç</option>
                </select>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="card-footer">
        <button
          class="btn btn-fill btn-primary"
          (click)="add()"
          [disabled]="isSubmitting || (membershipTypes && membershipTypes.length === 0)">
          {{ isSubmitting ? 'Üyelik Ekleniyor...' : 'Ekle' }}
        </button>
        
      </div>
    </div>
  </div>
</div>