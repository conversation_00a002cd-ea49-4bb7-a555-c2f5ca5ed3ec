using Core.Utilities.Paging;

namespace Entities.DTOs
{
    public class MembershipTypePagingParameters : PagingParameters
    {
        public string SearchText { get; set; } = "";
        public string Branch { get; set; } = "";
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public int? MinDuration { get; set; }
        public int? MaxDuration { get; set; }
    }
}
