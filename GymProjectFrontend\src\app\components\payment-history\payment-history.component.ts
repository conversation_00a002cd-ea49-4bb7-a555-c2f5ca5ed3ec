import { <PERSON>mponent, On<PERSON>nit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { PaymentHistoryService } from '../../services/payment-history.service';
import { PaymentHistory } from '../../models/paymentHistory'; // PaymentHistory zaten import edilmiş
import { ToastrService } from 'ngx-toastr';
import { FormControl } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { ListResponseModel } from '../../models/listResponseModel'; // ListResponseModel import edildi
import {
  map,
  startWith,
  takeUntil
} from 'rxjs/operators';
import { MemberService } from '../../services/member.service';
import { Member } from '../../models/member';
import { DebtPaymentService } from '../../services/debt-payment-service.service';
import { DialogService } from '../../services/dialog.service';
import { ChartUtilsService } from '../../services/chart-utils.service';
import { RateLimitService } from '../../services/rate-limit.service';
import { Workbook } from 'exceljs'; // exceljs importu
import * as fs from 'file-saver'; // file-saver importu
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

@Component({
  selector: 'app-payment-history',
  templateUrl: './payment-history.component.html',
  styleUrls: ['./payment-history.component.css'],
  standalone: false
})
export class PaymentHistoryComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('paymentChart') paymentChartRef: ElementRef;
  @ViewChild('monthlyTrendChart') monthlyTrendChartRef: ElementRef;
  paymentHistories: PaymentHistory[] = [];
  totalCash: number = 0;
  totalCreditCard: number = 0;
  totalTransfer: number = 0;
  totalDebt: number = 0;
  totalAmount: number = 0;
  startDate: string = '';
  endDate: string = '';
  private initialStartDate: string = '';
  private initialEndDate: string = '';
  currentPage: number = 1;
  totalPages: number = 0;
  totalItems: number = 0;
  itemsPerPage: number = 25; // Başlangıçta 25 kayıt
  pageSizeOptions: number[] = [25, 50, 100]; // Sayfa boyutu seçenekleri
  isLoading: boolean = false;
  isInitialLoading: boolean = true; // Sayfa ilk yüklenirken spinner göstermek için
  isSearching: boolean = false; // Arama spam koruması için

  // Spam koruması
  private lastExportTime = 0;
  private exportCooldown = 3000; // 3 saniye cooldown
  isExporting = false;
  selectedMonth: string = '';

  // Charts
  paymentChart: Chart;
  monthlyTrendChart: Chart;

  private destroy$ = new Subject<void>();

  memberSearchControl = new FormControl();
  filteredMembers: Observable<Member[]>;
  members: Member[] = [];

  currentMonthStart: Date;
  currentMonthEnd: Date;
  isFiltered: boolean = false;
  selectedMember: Member | null = null;

  constructor(
    private paymentHistoryService: PaymentHistoryService,
    private memberService: MemberService,
    private debtPaymentService: DebtPaymentService,
    private toastrService: ToastrService,
    private dialogService: DialogService,
    private chartUtils: ChartUtilsService,
    private rateLimitService: RateLimitService
  ) {
    this.setCurrentMonthDates();
    // Başlangıç değerlerini sakla
    this.initialStartDate = this.startDate;
    this.initialEndDate = this.endDate;
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngAfterViewInit(): void {
    // Charts will be initialized after initial data loading is complete
    // See checkInitialDataLoadingComplete method
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Destroy charts to prevent memory leaks
    if (this.paymentChart) {
      this.paymentChart.destroy();
    }
    if (this.monthlyTrendChart) {
      this.monthlyTrendChart.destroy();
    }
  }

  setCurrentMonthDates() {
    const now = new Date();
    this.currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    this.currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  }

  initializeCharts() {
    const paymentChartElement = document.getElementById('paymentChart') as HTMLCanvasElement;
    const monthlyTrendChartElement = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;

    if (!paymentChartElement || !monthlyTrendChartElement) {
      console.warn('Chart elements not found in DOM, retrying...');
      // DOM elementleri henüz hazır değilse, kısa bir süre sonra tekrar dene
      setTimeout(() => {
        this.initializeCharts();
      }, 200);
      return;
    }

    try {
      // Payment distribution chart
      const paymentData = {
        totalCash: this.totalCash,
        totalCreditCard: this.totalCreditCard,
        totalTransfer: this.totalTransfer,
        totalDebt: this.totalDebt
      };

      const paymentChartConfig = this.chartUtils.getPaymentStatsConfig(paymentData) as ChartConfiguration;

      // Eğer chart zaten varsa, önce destroy et
      if (this.paymentChart) {
        this.paymentChart.destroy();
      }

      this.paymentChart = new Chart(paymentChartElement, paymentChartConfig);

      // Monthly trend chart
      this.loadMonthlyTrendData();
    } catch (error) {
      console.error('Chart initialization error:', error);
      // Hata durumunda tekrar deneme
      setTimeout(() => {
        this.initializeCharts();
      }, 500);
    }
  }

  loadMonthlyTrendData() {
    // Mevcut yılın aylık gelir trendini al
    const currentYear = new Date().getFullYear();

    // Monthly revenue API çağrısını yap
    this.paymentHistoryService.getMonthlyRevenue(currentYear).subscribe({
      next: (response) => {
        if (response.success) {
          const monthlyData = {
            monthlyRevenue: response.data.monthlyRevenue
          };

          const monthlyChartConfig = this.chartUtils.getMonthlyRevenueConfig(monthlyData) as ChartConfiguration;
          const monthlyTrendChartElement = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;

          if (!monthlyTrendChartElement) {
            console.warn('Monthly trend chart element not found');
            return;
          }

          try {
            // Eğer grafik zaten oluşturulmuşsa, güncelle
            if (this.monthlyTrendChart) {
              this.monthlyTrendChart.data.datasets[0].data = monthlyData.monthlyRevenue;
              this.monthlyTrendChart.update();
            } else {
              // Grafik henüz oluşturulmamışsa, yeni bir tane oluştur
              this.monthlyTrendChart = new Chart(monthlyTrendChartElement, monthlyChartConfig);

              // Chart event handler'larını ekle
              this.addChartEventHandlers();
            }
          } catch (error) {
            console.error('Monthly trend chart creation error:', error);
            this.showEmptyMonthlyTrendChart();
          }
        } else {
          this.toastrService.error('Aylık gelir trendi yüklenirken bir hata oluştu', 'Hata');
          // Hata durumunda boş bir grafik göster
          this.showEmptyMonthlyTrendChart();
        }
      },
      error: (error) => {
        console.error('Aylık gelir trendi yüklenirken bir hata oluştu:', error);
        this.toastrService.error('Aylık gelir trendi yüklenirken bir hata oluştu', 'Hata');
        // Hata durumunda boş bir grafik göster
        this.showEmptyMonthlyTrendChart();
      }
    });
  }

  showEmptyMonthlyTrendChart() {
    const monthlyTrendChartElement = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;

    if (!monthlyTrendChartElement) {
      console.warn('Monthly trend chart element not found for empty chart');
      return;
    }

    try {
      const emptyData = {
        monthlyRevenue: Array(12).fill(0)
      };

      const monthlyChartConfig = this.chartUtils.getMonthlyRevenueConfig(emptyData) as ChartConfiguration;

      // Eğer chart zaten varsa, önce destroy et
      if (this.monthlyTrendChart) {
        this.monthlyTrendChart.destroy();
      }

      this.monthlyTrendChart = new Chart(monthlyTrendChartElement, monthlyChartConfig);

      // Chart event handler'larını ekle
      this.addChartEventHandlers();
    } catch (error) {
      console.error('Empty monthly trend chart creation error:', error);
    }
  }



  exportToExcel() {
    // Spam koruması
    const now = Date.now();
    if (now - this.lastExportTime < this.exportCooldown) {
      this.toastrService.warning('Çok hızlı indirmeye çalışıyorsunuz. Lütfen bekleyin.', 'Uyarı');
      return;
    }

    if (this.isExporting) {
      this.toastrService.warning('Export işlemi devam ediyor...', 'Uyarı');
      return;
    }

    this.lastExportTime = now;
    this.isExporting = true;

    // Rate limit kontrolü
    this.rateLimitService.getRemainingFileDownloads().subscribe({
      next: (rateLimitResponse) => {
        if (rateLimitResponse.success && rateLimitResponse.remainingDownloads <= 0) {
          this.toastrService.error('Dosya indirme limitini aştınız. 10 dakikada en fazla 5 dosya indirebilirsiniz.', 'Limit Aşıldı');
          this.isExporting = false;
          return;
        }

        this.performExcelExport();
      },
      error: (error) => {
        console.error('Rate limit kontrolü başarısız:', error);
        this.performExcelExport(); // Rate limit kontrolü başarısız olursa yine de devam et
      }
    });
  }

  private performExcelExport() {
    this.isLoading = true; // Yükleniyor durumunu başlat
    this.toastrService.info('Excel dosyası hazırlanıyor...', 'Bilgi');

    // Aktif filtreleri kontrol et
    const filtersActive = this.hasActiveFilters();

    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (filtersActive) {
      // Filtre varsa, filtreleme sonucunu indir
      startDate = this.startDate ? new Date(this.startDate) : undefined;
      endDate = this.endDate ? new Date(this.endDate) : undefined;
    } else {
      // Filtre yoksa, mevcut ayın 1'inden bugüne kadar olan verileri indir
      const now = new Date();
      startDate = new Date(now.getFullYear(), now.getMonth(), 1); // Ayın ilk günü
      endDate = new Date(); // Bugün
    }

    const parameters = {
      searchText: this.selectedMember?.name || '', // Sadece isimle arama yapıldığını varsayıyoruz
      startDate: startDate,
      endDate: endDate,
      // paymentMethod: this.selectedPaymentMethod // Gerekirse eklenebilir
    };

    // Her zaman filtrelenmiş veriyi çeken servisi kullan (filtre yoksa tüm veriyi getirir)
    const dataObservable: Observable<ListResponseModel<PaymentHistory>> =
        this.paymentHistoryService.getAllPaymentHistoryFiltered(parameters);

    dataObservable.subscribe({
      next: (response) => {
        if (response.success) {
          let paymentsToExport = response.data; // Filtrelenmiş veya tüm veriyi al

          if (!paymentsToExport || paymentsToExport.length === 0) {
            this.toastrService.warning('Dışa aktarılacak veri bulunamadı.', 'Uyarı');
            this.isLoading = false;
            this.isExporting = false;
            return;
          }

          try {
            // Yeni bir Workbook oluştur
            const workbook = new Workbook();
            const worksheet = workbook.addWorksheet('Ödemeler');

            // Sütun başlıklarını tanımla
            worksheet.columns = [
              { header: 'Ödeme Tarihi', key: 'paymentDate', width: 25 },
              { header: 'Üye', key: 'name', width: 30 },
              { header: 'Telefon', key: 'phoneNumber', width: 20 },
              { header: 'Üyelik Türü', key: 'membershipType', width: 20 },
              { header: 'Ödeme Tipi', key: 'paymentMethod', width: 25 },
              { header: 'Tutar', key: 'paymentAmount', width: 15, style: { numFmt: '#,##0.00 ₺' } }, // Para formatı
              { header: '', key: 'empty1', width: 5 }, // Boş sütun
              { header: 'ÖZET', key: 'summary', width: 20 }, // Özet başlığı
              { header: 'TUTAR', key: 'summaryAmount', width: 15, style: { numFmt: '#,##0.00 ₺' } } // Özet tutar
            ];

            // Veri satırlarını eklemeden önce tarihe göre sırala (en yeni en üstte)
            paymentsToExport.sort((a: PaymentHistory, b: PaymentHistory) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime());

            // Export edilecek verilerden toplamları hesapla
            const exportTotals = this.calculateExportTotals(paymentsToExport);

            // Sıralanmış veriyi ekle
            let rowIndex = 2; // Başlık satırından sonra
            paymentsToExport.forEach((p: PaymentHistory) => {
              worksheet.addRow({
                paymentDate: new Date(p.paymentDate).toLocaleString(),
                name: p.name,
                phoneNumber: p.phoneNumber,
                membershipType: p.membershipType,
                paymentMethod: p.paymentMethod,
                paymentAmount: p.paymentAmount,
                empty1: '',
                summary: '',
                summaryAmount: ''
              });
              rowIndex++;
            });

            // Sağ tarafta özet tablosu ekle
            this.addSummaryTable(worksheet, exportTotals, rowIndex);

            // Dosya adını filtre durumuna göre oluştur
            const now = new Date();
            const dateSuffix = now.toISOString().split('T')[0];

            let fileName: string;
            if (filtersActive) {
              fileName = `Ödeme_Raporu_Filtrelenmiş_${dateSuffix}.xlsx`;
            } else {
              const currentMonth = now.toLocaleString('tr-TR', { month: 'long', year: 'numeric' });
              fileName = `${currentMonth}_Gelir_Dökümü_${dateSuffix}.xlsx`;
            }

            // Workbook'u buffer'a yaz ve dosyayı indir
            workbook.xlsx.writeBuffer().then((buffer) => {
              const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
              fs.saveAs(blob, fileName);

              // Başarılı indirme kaydı
              this.rateLimitService.recordFileDownload().subscribe({
                next: (recordResponse) => {
                  this.toastrService.success(`Excel dosyası başarıyla indirildi.`, 'Başarılı');
                },
                error: (recordError) => {
                  console.error('File download record error:', recordError);
                  this.toastrService.success(`Excel dosyası başarıyla indirildi.`, 'Başarılı');
                }
              });
            }).catch(err => {
                console.error('Excel buffer error:', err);
                this.toastrService.error('Excel dosyası oluşturulurken bir hata oluştu (buffer)', 'Hata');
            }).finally(() => {
              this.isLoading = false; // Yükleniyor durumunu bitir
              this.isExporting = false; // Export durumunu bitir
            });

          } catch (error) {
            console.error('Excel export error:', error);
            this.toastrService.error('Excel dosyası oluşturulurken bir hata oluştu', 'Hata');
            this.isLoading = false; // Hata durumunda yükleniyor durumunu bitir
            this.isExporting = false; // Hata durumunda export durumunu bitir
          }
        } else {
          this.toastrService.error(response.message || 'Ödeme geçmişi alınırken bir hata oluştu.', 'Hata');
          this.isLoading = false; // Hata durumunda yükleniyor durumunu bitir
          this.isExporting = false; // Hata durumunda export durumunu bitir
        }
      },
      error: (error) => {
        console.error('Error fetching payment histories for export:', error);
        if (error.status === 429) {
          this.toastrService.error('Çok fazla istek gönderildi. Lütfen biraz bekleyip tekrar deneyin.', 'Hata');
        } else {
          this.toastrService.error('Ödeme geçmişi alınırken bir sunucu hatası oluştu.', 'Hata');
        }
        this.isLoading = false; // Hata durumunda yükleniyor durumunu bitir
        this.isExporting = false; // Hata durumunda export durumunu bitir
      }
    });
  }
  resetFilters() {
    this.memberSearchControl.reset();
    this.startDate = this.initialStartDate;
    this.endDate = this.initialEndDate;
    this.selectedMember = null;
    this.isFiltered = false;
    this.isSearching = false; // Arama durumunu da sıfırla
    this.currentPage = 1;
    this.loadPayments();
    this.toastrService.info('Filtreler sıfırlandı', 'Bilgi');
  }

  hasActiveFilters(): boolean {
    return !!(this.startDate || this.endDate || this.selectedMember);
  }

  searchMember() {
    // Bu metod artık kullanılmıyor, performSearch() kullanılıyor
    this.performSearch();
  }

  clearMemberFilter() {
    this.memberSearchControl.reset();
    this.selectedMember = null;
    this.isSearching = false; // Arama durumunu da sıfırla
    this.loadPayments();
  }

  clearStartDateFilter() {
    this.startDate = '';
    // Otomatik arama yapma, sadece değeri temizle
  }

  clearEndDateFilter() {
    this.endDate = '';
    // Otomatik arama yapma, sadece değeri temizle
  }

  getPaymentMethodClass(method: string): string {
    if (method.includes('Nakit')) return 'payment-cash';
    if (method.includes('Kredi Kartı')) return 'payment-credit-card';
    if (method.includes('Havale')) return 'payment-transfer';
    if (method.includes('Borç') && !method.includes('Ödemesi')) return 'payment-debt';
    return '';
  }

  getInitials(name: string): string {
    if (!name) return '';

    const nameParts = name.split(' ');
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();

    return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  }

  getAvatarColor(name: string): string {
    if (!name) return '#4361ee';

    // Generate a consistent color based on the name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    const colors = [
      '#4361ee', '#3f37c9', '#4895ef', '#4cc9f0',
      '#560bad', '#7209b7', '#b5179e', '#f72585',
      '#3a0ca3', '#4361ee', '#4895ef', '#4cc9f0'
    ];

    return colors[Math.abs(hash) % colors.length];
  }

  getPaginationRange(): number[] {
    const range = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }

  // Tüm başlangıç verilerini yükleyen metot
  loadInitialData() {
    this.isInitialLoading = true;
    let membersLoaded = false;
    let paymentsLoaded = false;

    // Üyeleri yükle
    this.memberService.getMembers().subscribe({
      next: (response) => {
        this.members = response.data;
        membersLoaded = true;
        this.checkInitialDataLoadingComplete(membersLoaded, paymentsLoaded);

        // Üyeler yüklendikten sonra autocomplete'i setup et
        this.setupMemberAutocomplete();
      },
      error: (error) => {
        console.error('Üyeler yüklenirken hata oluştu:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu', 'Hata');
        membersLoaded = true;
        this.checkInitialDataLoadingComplete(membersLoaded, paymentsLoaded);
      },
    });

    // Ödemeleri yükle - callback ile takip et
    this.loadInitialPayments(() => {
      paymentsLoaded = true;
      this.checkInitialDataLoadingComplete(membersLoaded, paymentsLoaded);
    });
  }

  // Başlangıç veri yükleme tamamlanma kontrolü
  checkInitialDataLoadingComplete(membersLoaded: boolean, paymentsLoaded: boolean) {
    if (membersLoaded && paymentsLoaded) {
      this.isInitialLoading = false;
      // Initial loading tamamlandıktan sonra chart'ları initialize et
      setTimeout(() => {
        this.initializeCharts();
      }, 100);
    }
  }

  // İlk yükleme için özel payment loading metodu
  loadInitialPayments(callback: () => void) {
    const parameters = {
      pageNumber: this.currentPage,
      pageSize: this.itemsPerPage,
      searchText: '',
      startDate: this.startDate ? new Date(this.startDate) : undefined,
      endDate: this.endDate ? new Date(this.endDate) : undefined
    };

    this.paymentHistoryService.getPaymentHistoryPaginated(parameters).subscribe({
      next: (response) => {
        if (response.success) {
          this.paymentHistories = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
          this.loadTotals(parameters);
          this.updateCharts();
        }
        callback(); // İşlem tamamlandığını bildir
      },
      error: (error) => {
        console.error('Ödeme geçmişi yüklenirken hata oluştu:', error);
        this.toastrService.error('Ödeme geçmişi yüklenirken bir hata oluştu.', 'Hata');
        callback(); // Hata durumunda da callback'i çağır
      }
    });
  }

  private loadMembers() {
    this.memberService.getMembers().subscribe({
      next: (response) => {
        this.members = response.data;
      },
      error: (error) => {
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu', 'Hata');
      },
    });
  }

  private setupMemberAutocomplete() {
    this.filteredMembers = this.memberSearchControl.valueChanges.pipe(
      startWith(''),
      map((value) => {
        const name = typeof value === 'string' ? value : value?.name;
        return name ? this._filterMembers(name) : this.members.slice();
      })
    );

    this.memberSearchControl.valueChanges.subscribe((member) => {
      if (member && typeof member !== 'string') {
        this.selectedMember = member;
      }
    });
  }

  private _filterMembers(value: string): Member[] {
    const filterValue = value.toLowerCase();
    return this.members.filter(
      (member) =>
        member.name.toLowerCase().includes(filterValue) ||
        member.phoneNumber.toLowerCase().includes(filterValue)
    );
  }

  displayMember(member: Member): string {
    return member ? `${member.name} - ${member.phoneNumber}` : '';
  }

  private loadMemberPayments(member: Member) {
    this.currentPage = 1;
    const parameters = {
      pageNumber: this.currentPage,
      pageSize: this.itemsPerPage,
      searchText: member.name,
      startDate: this.startDate ? new Date(this.startDate) : undefined,
      endDate: this.endDate ? new Date(this.endDate) : undefined,
    };

    // Önce tüm değerleri sıfırla
    this.totalCash = 0;
    this.totalCreditCard = 0;
    this.totalTransfer = 0;
    this.totalDebt = 0;
    this.totalAmount = 0;

    this.loadPayments(parameters);
  }

  onFilterChange(): void {
    // Bu metod artık otomatik arama yapmıyor
    // Sadece isFiltered flag'ini güncelle
    this.isFiltered = this.startDate !== '' || this.endDate !== '';
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadPayments();
    }
  }

  onPageSizeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const newPageSize = parseInt(target.value);
    this.itemsPerPage = newPageSize;
    this.currentPage = 1; // Sayfa boyutu değiştiğinde ilk sayfaya dön
    this.loadPayments();
  }

  onDateChange(): void {
    // Tarih değiştiğinde sadece console.log, otomatik arama yapma
    console.log('Date changed - Start:', this.startDate, 'End:', this.endDate);
  }

  shouldShowSearchButton(): boolean {
    // Ara butonu şu durumlarda görünür:
    // 1. Üye arama textbox'ında değer varsa
    // 2. Başlangıç tarihi değiştiyse
    // 3. Bitiş tarihi değiştiyse
    return !!(this.memberSearchControl.value) ||
           (this.startDate !== this.initialStartDate) ||
           (this.endDate !== this.initialEndDate);
  }

  performSearch(): void {
    // Spam koruması - eğer zaten arama yapılıyorsa çık
    if (this.isSearching) {
      return;
    }

    this.isSearching = true;

    const selectedMember = this.memberSearchControl.value;
    if (selectedMember && (selectedMember.name || selectedMember.phoneNumber)) {
      // Üye seçilmişse üye bazlı arama yap
      this.selectedMember = selectedMember;
      this.loadMemberPaymentsWithDelay(selectedMember);
    } else {
      // Üye seçilmemişse tarih bazlı arama yap
      this.selectedMember = null;
      this.loadPaymentsWithDelay();
    }
  }

  private loadMemberPaymentsWithDelay(member: any): void {
    this.currentPage = 1;
    const parameters = {
      pageNumber: this.currentPage,
      pageSize: this.itemsPerPage,
      searchText: member.name,
      startDate: this.startDate ? new Date(this.startDate) : undefined,
      endDate: this.endDate ? new Date(this.endDate) : undefined,
    };

    // Önce tüm değerleri sıfırla
    this.totalCash = 0;
    this.totalCreditCard = 0;
    this.totalTransfer = 0;
    this.totalDebt = 0;
    this.totalAmount = 0;

    this.loadPaymentsWithSpamProtection(parameters);
  }

  private loadPaymentsWithDelay(): void {
    this.currentPage = 1;
    this.isFiltered = this.startDate !== '' || this.endDate !== '';
    this.loadPaymentsWithSpamProtection();
  }

  private loadPaymentsWithSpamProtection(parameters?: any): void {
    this.isLoading = true;

    if (!parameters) {
        parameters = {
            pageNumber: this.currentPage,
            pageSize: this.itemsPerPage,
            searchText: this.selectedMember?.name || '',
            startDate: this.startDate ? new Date(this.startDate) : undefined,
            endDate: this.endDate ? new Date(this.endDate) : undefined
        };
    }

    this.paymentHistoryService.getPaymentHistoryPaginated(parameters).subscribe({
        next: (response) => {
            if (response.success) {
                this.paymentHistories = response.data.data;
                this.totalPages = response.data.totalPages;
                this.totalItems = response.data.totalCount;

                // Eğer parameters.searchText varsa (yani üye seçilmişse)
                // calculateTotalsFromCurrentData metodunu çağır
                if (parameters.searchText) {
                    this.calculateTotalsFromCurrentData();
                } else {
                    // Seçili üye yoksa, API'den toplamları al
                    this.loadTotals(parameters);
                }

                // Update charts if they exist
                this.updateCharts();
            }
            this.isLoading = false;

            // 1 saniye sonra arama butonunu tekrar aktif et
            setTimeout(() => {
              this.isSearching = false;
            }, 1000);
        },
        error: (error) => {
            this.toastrService.error('Ödeme geçmişi yüklenirken bir hata oluştu.', 'Hata');
            this.isLoading = false;

            // Hata durumunda da butonu tekrar aktif et
            setTimeout(() => {
              this.isSearching = false;
            }, 1000);
        }
    });
  }

  loadPayments(parameters?: any) {
    this.isLoading = true;

    if (!parameters) {
        parameters = {
            pageNumber: this.currentPage,
            pageSize: this.itemsPerPage,
            searchText: this.selectedMember?.name || '',
            startDate: this.startDate ? new Date(this.startDate) : undefined,
            endDate: this.endDate ? new Date(this.endDate) : undefined
        };
    }

    this.paymentHistoryService.getPaymentHistoryPaginated(parameters).subscribe({
        next: (response) => {
            if (response.success) {
                this.paymentHistories = response.data.data;
                this.totalPages = response.data.totalPages;
                this.totalItems = response.data.totalCount;

                // Eğer parameters.searchText varsa (yani üye seçilmişse)
                // calculateTotalsFromCurrentData metodunu çağır
                if (parameters.searchText) {
                    this.calculateTotalsFromCurrentData();
                } else {
                    // Seçili üye yoksa, API'den toplamları al
                    this.loadTotals(parameters);
                }

                // Update charts if they exist
                this.updateCharts();
            }
            this.isLoading = false;
        },
        error: (error) => {
            this.toastrService.error('Ödeme geçmişi yüklenirken bir hata oluştu.', 'Hata');
            this.isLoading = false;
        }
    });
  }

  updateCharts() {
    setTimeout(() => {
      if (this.paymentChart) {
        const paymentData = {
          totalCash: this.totalCash,
          totalCreditCard: this.totalCreditCard,
          totalTransfer: this.totalTransfer,
          totalDebt: this.totalDebt
        };

        this.paymentChart.data.datasets[0].data = [
          paymentData.totalCash,
          paymentData.totalCreditCard,
          paymentData.totalTransfer,
          paymentData.totalDebt
        ];
        this.paymentChart.update();
      } else {
        this.initializeCharts();
      }
    }, 100);
  }

  // Chart event handler'ları
  addChartEventHandlers(): void {
    if (this.monthlyTrendChart) {
      // Chart'a click ve hover event'lerini ekle
      this.monthlyTrendChart.options.onClick = (event: any, elements: any[]) => {
        this.onChartClick(event, this.monthlyTrendChart!);
      };

      this.monthlyTrendChart.options.onHover = (event: any, elements: any[]) => {
        this.onChartHover(event, this.monthlyTrendChart!);
      };
    }
  }

  // Grafik tıklama olayları
  onChartClick(event: any, chart: Chart): void {
    const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);

    if (points.length) {
      const firstPoint = points[0];
      const label = chart.data.labels?.[firstPoint.index];
      const value = chart.data.datasets[firstPoint.datasetIndex].data[firstPoint.index];

      // Tıklanan veri noktası hakkında detay göster
      this.toastrService.info(
        `${label}: ${new Intl.NumberFormat('tr-TR', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(value as number)} ₺`,
        'Grafik Detayı'
      );
    }
  }

  // Grafik hover efektleri
  onChartHover(event: any, chart: Chart): void {
    const canvas = chart.canvas;
    if (canvas) {
      canvas.style.cursor = chart.getElementsAtEventForMode(
        event,
        'nearest',
        { intersect: true },
        true
      ).length > 0 ? 'pointer' : 'default';
    }
  }

  private calculateTotalsFromCurrentData() {
    // Normal ödemeler için toplamları hesapla
    const regularPayments = this.paymentHistories.filter(p =>
      !p.paymentMethod.includes('Borç') &&
      !p.paymentMethod.includes('Borç Ödemesi'));

    this.totalCash = regularPayments
      .filter(p => p.paymentMethod === 'Nakit')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    this.totalCreditCard = regularPayments
      .filter(p => p.paymentMethod === 'Kredi Kartı')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    this.totalTransfer = regularPayments
      .filter(p => p.paymentMethod === 'Havale - EFT')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    // Borç ödemeleri için toplamları ekle
    this.totalCash += this.paymentHistories
      .filter(p => p.paymentMethod === 'Nakit (Borç Ödemesi)')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    this.totalCreditCard += this.paymentHistories
      .filter(p => p.paymentMethod === 'Kredi Kartı (Borç Ödemesi)')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    this.totalTransfer += this.paymentHistories
      .filter(p => p.paymentMethod === 'Havale - EFT (Borç Ödemesi)')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    // İlk borç miktarını bul
    const initialDebt = this.paymentHistories
      .filter(p => p.paymentMethod === 'Borç')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    // Toplam borç ödemelerini hesapla
    const totalDebtPayments = this.paymentHistories
      .filter(p => p.paymentMethod.includes('Borç Ödemesi'))
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    // Kalan borç miktarı
    this.totalDebt = initialDebt - totalDebtPayments;

    // Genel toplam (borç ödemeleri hariç tüm ödemeler)
    this.totalAmount = this.totalCash + this.totalCreditCard + this.totalTransfer;
  }

  // Excel export için toplamları hesapla
  private calculateExportTotals(payments: PaymentHistory[]) {
    // Normal ödemeler (borç hariç)
    const regularPayments = payments.filter(p =>
      !p.paymentMethod.includes('Borç') &&
      !p.paymentMethod.includes('Borç Ödemesi'));

    const cashTotal = regularPayments
      .filter(p => p.paymentMethod === 'Nakit')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    const creditCardTotal = regularPayments
      .filter(p => p.paymentMethod === 'Kredi Kartı')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    const transferTotal = regularPayments
      .filter(p => p.paymentMethod === 'Havale - EFT')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    // Borç ödemeleri için toplamları ekle
    const cashDebtPayments = payments
      .filter(p => p.paymentMethod === 'Nakit (Borç Ödemesi)')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    const creditCardDebtPayments = payments
      .filter(p => p.paymentMethod === 'Kredi Kartı (Borç Ödemesi)')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    const transferDebtPayments = payments
      .filter(p => p.paymentMethod === 'Havale - EFT (Borç Ödemesi)')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    // Borç miktarı
    const debtTotal = payments
      .filter(p => p.paymentMethod === 'Borç')
      .reduce((sum, payment) => sum + payment.paymentAmount, 0);

    return {
      cash: cashTotal + cashDebtPayments,
      creditCard: creditCardTotal + creditCardDebtPayments,
      transfer: transferTotal + transferDebtPayments,
      debt: debtTotal,
      totalWithoutDebt: cashTotal + cashDebtPayments + creditCardTotal + creditCardDebtPayments + transferTotal + transferDebtPayments
    };
  }

  // Excel'e özet tablosu ekle
  private addSummaryTable(worksheet: any, totals: any, startRow: number) {
    // Özet tablosu başlığını ekle
    const summaryStartRow = 2; // İlk satırdan başla

    // Başlık satırını formatla
    const headerRow = worksheet.getRow(1);
    headerRow.getCell(8).value = 'ÖZET';
    headerRow.getCell(9).value = 'TUTAR';
    headerRow.getCell(8).font = { bold: true, size: 12 };
    headerRow.getCell(9).font = { bold: true, size: 12 };
    headerRow.getCell(8).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };
    headerRow.getCell(9).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };

    // Özet verilerini ekle
    let currentRow = summaryStartRow;

    // Nakit
    worksheet.getCell(`H${currentRow}`).value = 'Nakit';
    worksheet.getCell(`I${currentRow}`).value = totals.cash;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    currentRow++;

    // Kredi Kartı
    worksheet.getCell(`H${currentRow}`).value = 'Kredi Kartı';
    worksheet.getCell(`I${currentRow}`).value = totals.creditCard;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    currentRow++;

    // Havale-EFT
    worksheet.getCell(`H${currentRow}`).value = 'Havale - EFT';
    worksheet.getCell(`I${currentRow}`).value = totals.transfer;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    currentRow++;

    // Borç
    worksheet.getCell(`H${currentRow}`).value = 'Borç';
    worksheet.getCell(`I${currentRow}`).value = totals.debt;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    worksheet.getCell(`H${currentRow}`).font = { color: { argb: 'FFFF0000' } }; // Kırmızı renk
    worksheet.getCell(`I${currentRow}`).font = { color: { argb: 'FFFF0000' } };
    currentRow++;

    // Boş satır
    currentRow++;

    // Toplam (Borç Hariç)
    worksheet.getCell(`H${currentRow}`).value = 'Toplam (Borç Hariç)';
    worksheet.getCell(`I${currentRow}`).value = totals.totalWithoutDebt;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    worksheet.getCell(`H${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } }; // Yeşil ve kalın
    worksheet.getCell(`I${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } };
    worksheet.getCell(`H${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };
    worksheet.getCell(`I${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };

    // Özet tablosu etrafına border ekle
    for (let row = 1; row <= currentRow; row++) {
      for (let col = 8; col <= 9; col++) {
        const cell = worksheet.getCell(row, col);
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      }
    }
  }

  loadTotals(parameters: any) {
    // Eğer seçili bir üye varsa, sadece o üyenin ödemelerinin toplamını hesapla
    if (this.selectedMember) {
      // Mevcut ödeme listesinden toplam değerleri hesapla
      this.totalCash = this.paymentHistories
        .filter((p) => p.paymentMethod === 'Nakit')
        .reduce((sum, payment) => sum + payment.paymentAmount, 0);

      this.totalCreditCard = this.paymentHistories
        .filter((p) => p.paymentMethod === 'Kredi Kartı')
        .reduce((sum, payment) => sum + payment.paymentAmount, 0);

      this.totalTransfer = this.paymentHistories
        .filter((p) => p.paymentMethod === 'Havale - EFT')
        .reduce((sum, payment) => sum + payment.paymentAmount, 0);

      this.totalDebt = this.paymentHistories
        .filter((p) => p.paymentMethod.includes('Borç'))
        .reduce((sum, payment) => sum + payment.paymentAmount, 0);

      this.totalAmount =
        this.totalCash + this.totalCreditCard + this.totalTransfer;
    } else {
      // Seçili üye yoksa normal API çağrısını yap
      this.paymentHistoryService.getPaymentTotals(parameters).subscribe({
        next: (response) => {
          if (response.success) {
            this.totalCash = response.data.cash;
            this.totalCreditCard = response.data.creditCard;
            this.totalTransfer = response.data.transfer;
            this.totalDebt = response.data.debt;
            this.totalAmount = response.data.total;

            // Update charts with new data
            this.updateCharts();
          }
        },
        error: (error) => {
          console.error('Error loading totals:', error);
        },
      });
    }
  }

  getTotalValuesTitle(): string {
    if (this.selectedMember) {
      return `Toplam Değerler (${this.selectedMember.name})`;
    }
    if (this.isFiltered) {
      return 'Toplam Değerler (Filtrelenmiş)';
    }
    return `Toplam Değerler (${this.currentMonthStart.toLocaleString(
      'default',
      { month: 'long' }
    )} ${this.currentMonthStart.getFullYear()})`;
  }

  deletePayment(payment: PaymentHistory) {
    const isDebtPayment = payment.paymentMethod.includes('Borç Ödemesi');

    this.dialogService.confirmPaymentDelete(
      payment.name,
      payment.paymentAmount,
      isDebtPayment
    ).subscribe(result => {
      if (result) {
        this.isLoading = true;

        if (isDebtPayment) {
          this.debtPaymentService.delete(payment.paymentID).subscribe({
            next: (response) => {
              this.toastrService.success('Borç ödemesi başarıyla silindi');
              this.loadPayments();
            },
            error: (error) => {
              this.toastrService.error('Borç ödemesi silinirken bir hata oluştu');
              this.isLoading = false;
            },
          });
        } else {
          this.paymentHistoryService.delete(payment.paymentID).subscribe({
            next: (response) => {
              this.toastrService.success('Ödeme başarıyla silindi');
              this.loadPayments();
            },
            error: (error) => {
              this.toastrService.error('Ödeme silinirken bir hata oluştu');
              this.isLoading = false;
            },
          });
        }
      }
    });
  }

  clearFilters() {
    this.memberSearchControl.reset();
    this.startDate = '';
    this.endDate = '';
    this.selectedMember = null;
    this.isFiltered = false;
    this.currentPage = 1;
    this.loadPayments();
  }

  getCurrentMonthText(): string {
    if (!this.selectedMonth) return '';
    const [year, month] = this.selectedMonth.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleString('tr-TR', { month: 'long', year: 'numeric' });
  }
}
