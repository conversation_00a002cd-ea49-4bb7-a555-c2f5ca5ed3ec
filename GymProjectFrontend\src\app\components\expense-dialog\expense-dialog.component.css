/* Expense Dialog specific styles can be added here if needed */
.expense-form {
  padding-top: 10px; /* Add some padding to the top of the form */
}

/* Ensure spinner aligns well within the button */
.mat-mdc-button-persistent-ripple {
    display: flex;
    align-items: center;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: .2em;
    margin-right: 0.5rem; /* Add space between spinner and text */
}

/* Dark Mode Styles for Expense Dialog */
[data-theme="dark"] .mat-mdc-dialog-container {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .mat-mdc-dialog-title {
  color: var(--text-primary) !important;
}

/* Form Field Styles for Dark Mode */
[data-theme="dark"] .mat-mdc-form-field {
  --mdc-filled-text-field-container-color: transparent !important;
  --mdc-outlined-text-field-outline-color: var(--border-color) !important;
  --mdc-outlined-text-field-hover-outline-color: var(--primary) !important;
  --mdc-outlined-text-field-focus-outline-color: var(--primary) !important;
}

[data-theme="dark"] .mat-mdc-form-field .mdc-notched-outline__leading,
[data-theme="dark"] .mat-mdc-form-field .mdc-notched-outline__notch,
[data-theme="dark"] .mat-mdc-form-field .mdc-notched-outline__trailing {
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .mat-mdc-form-field:hover .mdc-notched-outline__leading,
[data-theme="dark"] .mat-mdc-form-field:hover .mdc-notched-outline__notch,
[data-theme="dark"] .mat-mdc-form-field:hover .mdc-notched-outline__trailing {
  border-color: var(--primary) !important;
}

[data-theme="dark"] .mat-mdc-form-field .mdc-floating-label {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .mat-mdc-form-field .mdc-floating-label--float-above {
  color: var(--primary) !important;
}

/* Input and Textarea Styles */
[data-theme="dark"] .mat-mdc-input-element {
  color: var(--text-primary) !important;
  background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .mat-mdc-text-field-wrapper {
  background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .mat-mdc-form-field-flex {
  background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .mat-mdc-input-element::placeholder {
  color: var(--text-secondary) !important;
  opacity: 0.7 !important;
}

/* Select Styles */
[data-theme="dark"] .mat-mdc-select-value {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .mat-mdc-select-arrow {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .mat-mdc-select-trigger {
  background-color: var(--bg-secondary) !important;
}

/* Hint and Error Styles */
[data-theme="dark"] .mat-mdc-form-field .mat-mdc-form-field-hint {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .mat-mdc-form-field .mat-mdc-form-field-error {
  color: var(--danger) !important;
}

/* Button Styles */
[data-theme="dark"] .mat-mdc-outlined-button {
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .mat-mdc-outlined-button:hover {
  background-color: var(--bg-secondary) !important;
  border-color: var(--primary) !important;
}

[data-theme="dark"] .mat-mdc-raised-button.mat-primary {
  background-color: var(--primary) !important;
  color: white !important;
}

/* Datepicker Icon */
[data-theme="dark"] .mat-datepicker-toggle .mat-icon {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .mat-datepicker-toggle:hover .mat-icon {
  color: var(--primary) !important;
}

/* Text Prefix (₺ symbol) */
[data-theme="dark"] .mat-mdc-form-field .mat-mdc-form-field-text-prefix {
  color: var(--text-secondary) !important;
}