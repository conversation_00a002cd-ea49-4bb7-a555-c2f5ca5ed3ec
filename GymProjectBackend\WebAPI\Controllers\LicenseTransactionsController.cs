﻿using Business.Abstract;
using Entities.Concrete;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class LicenseTransactionsController : ControllerBase
    {
        private readonly ILicenseTransactionService _licenseTransactionService;

        public LicenseTransactionsController(ILicenseTransactionService licenseTransactionService)
        {
            _licenseTransactionService = licenseTransactionService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll([FromQuery] int? userID = null, [FromQuery] string startDate = null, [FromQuery] string endDate = null, [FromQuery] int page = 1, [FromQuery] int pageSize = 20)
        {
            var result = _licenseTransactionService.GetAllFiltered(userID, startDate, endDate, page, pageSize);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getbyuserid")]
        public IActionResult GetByUserId(int userId)
        {
            var result = _licenseTransactionService.GetByUserId(userId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _licenseTransactionService.Delete(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("gettotals")]
        public IActionResult GetTotals()
        {
            var result = _licenseTransactionService.GetTotals();
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getmonthlyrevenue")]
        public IActionResult GetMonthlyRevenue([FromQuery] int year = 0)
        {
            if (year == 0)
            {
                year = DateTime.Now.Year;
            }
            var result = _licenseTransactionService.GetMonthlyRevenue(year);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(LicenseTransaction licenseTransaction)
        {
            var result = _licenseTransactionService.Add(licenseTransaction);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}