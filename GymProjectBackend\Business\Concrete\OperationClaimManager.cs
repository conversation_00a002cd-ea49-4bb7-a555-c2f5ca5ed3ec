﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class OperationClaimManager : IOperationClaimService
    {
        IOperationClaimDal _operationClaimDal;

        public OperationClaimManager(IOperationClaimDal operationClaimDal)
        {
            _operationClaimDal = operationClaimDal;
        }
        [SecuredOperation("owner")]
        [SmartCacheRemoveAspect("OperationClaim")]
        public IResult Add(OperationClaim operationClaim)
        {
            _operationClaimDal.Add(operationClaim);
            return new SuccessResult(Messages.OperationClaimAdded);
        }
        [SecuredOperation("owner")]
        [SmartCacheRemoveAspect("OperationClaim")]
        public IResult Delete(int id)
        {
            _operationClaimDal.HardDelete(id);
            return new SuccessResult(Messages.OperationClaimDeleted);
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 180, "OperationClaim", "System")]
        public IDataResult<List<OperationClaim>> GetAll()
        {
            return new SuccessDataResult<List<OperationClaim>>(_operationClaimDal.GetAll(), Messages.OperationClaimsListed);
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 180, "OperationClaim", "Details")]
        public IDataResult<OperationClaim> GetById(int id)
        {
            return new SuccessDataResult<OperationClaim>(_operationClaimDal.Get(o => o.OperationClaimId == id));
        }
        [SecuredOperation("owner")]
        [SmartCacheRemoveAspect("OperationClaim")]
        public IResult Update(OperationClaim operationClaim)
        {
            _operationClaimDal.Update(operationClaim);
            return new SuccessResult(Messages.OperationClaimUpdated);
        }

        [LogAspect] // Loglama eklendi
        [MultiTenantCacheAspect(duration: 180, "OperationClaim", "Name")]
        public IDataResult<OperationClaim> GetByName(string name)
        {
            // Sadece "member" rolü için çalışacak şekilde sınırlandırma
            if (name != "member")
            {
                return new ErrorDataResult<OperationClaim>(null, "Bu metot sadece 'member' rolü için kullanılabilir");
            }

            // IP adresi ve zaman damgası gibi bilgileri loglama
            var ipAddress = System.Net.Dns.GetHostName(); // Gerçek uygulamada HttpContext'ten alınmalı
            var timestamp = DateTime.Now;

            // Log mesajı
            // Gerçek uygulamada _logger.Info() gibi bir metot kullanılabilir
            Console.WriteLine($"[{timestamp}] GetByName called for role '{name}', IP: {ipAddress}");

            return new SuccessDataResult<OperationClaim>(_operationClaimDal.Get(o => o.Name == name));
        }
    }
}