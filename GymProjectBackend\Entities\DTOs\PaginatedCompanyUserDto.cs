using Core.Entities;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Sayfalanmış CompanyUser listesi için DTO
    /// </summary>
    public class PaginatedCompanyUserDto : IDto
    {
        public List<CompanyUserFullDetailDto> Data { get; set; }
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage { get; set; }
        public bool HasNextPage { get; set; }
        
        public PaginatedCompanyUserDto()
        {
            Data = new List<CompanyUserFullDetailDto>();
        }
        
        public PaginatedCompanyUserDto(List<CompanyUserFullDetailDto> data, int totalCount, int pageNumber, int pageSize)
        {
            Data = data;
            TotalCount = totalCount;
            PageNumber = pageNumber;
            PageSize = pageSize;
            TotalPages = (int)System.Math.Ceiling((double)totalCount / pageSize);
            HasPreviousPage = pageNumber > 1;
            HasNextPage = pageNumber < TotalPages;
        }
    }
}
