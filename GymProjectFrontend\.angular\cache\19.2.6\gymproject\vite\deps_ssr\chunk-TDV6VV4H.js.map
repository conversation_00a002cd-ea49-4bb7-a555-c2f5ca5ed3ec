{"version": 3, "sources": ["../../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.5\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { B as BrowserModule, b as bootstrapApplication, c as createApplication, p as platformBrowser, a as provideProtractorTestingSupport, d as ɵBrowserDomAdapter, e as ɵBrowserGetTestability, D as ɵDomEventsPlugin, K as ɵKeyEventsPlugin } from './browser-6JTYoVGl.mjs';\nimport { ɵgetDOM as _getDOM, DOCUMENT } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Inject, Injectable, ɵglobal as _global, ApplicationRef, InjectionToken, Injector, Optional, ɵConsole as _Console, NgModule, ɵRuntimeError as _RuntimeError, ɵXSS_SECURITY_URL as _XSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow as _allowSanitizationBypassAndThrow, ɵunwrapSafeValue as _unwrapSafeValue, ɵ_sanitizeUrl as __sanitizeUrl, ɵ_sanitizeHtml as __sanitizeHtml, ɵbypassSanitizationTrustHtml as _bypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle as _bypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript as _bypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl as _bypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl as _bypassSanitizationTrustResourceUrl, forwardRef, makeEnvironmentProviders, ɵwithDomHydration as _withDomHydration, ɵwithEventReplay as _withEventReplay, ɵwithI18nSupport as _withI18nSupport, ɵwithIncrementalHydration as _withIncrementalHydration, ENVIRONMENT_INITIALIZER, inject, NgZone, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵformatRuntimeError as _formatRuntimeError, Version } from '@angular/core';\nimport { E as EVENT_MANAGER_PLUGINS, a as EventManagerPlugin } from './dom_renderer-B-2OOwSx.mjs';\nexport { b as EventManager, R as REMOVE_STYLES_ON_COMPONENT_DESTROY, D as ɵDomRendererFactory2, S as ɵSharedStylesHost } from './dom_renderer-B-2OOwSx.mjs';\nimport { ɵwithHttpTransferCache as _withHttpTransferCache } from '@angular/common/http';\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  _doc;\n  _dom;\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = _getDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static ɵfac = function Meta_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Meta)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Meta,\n    factory: Meta.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  _doc;\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static ɵfac = function Title_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Title)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Title,\n    factory: Title.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = _global['ng'] = _global['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  msPerTick;\n  numTicks;\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  appRef;\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```ts\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (_getDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  /**\n   * A set of supported event names for gestures to be used in Angular.\n   * Angular supports all built-in recognizers, as listed in\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  events = [];\n  /**\n   * Maps gesture event names to a set of configuration options\n   * that specify overrides to the default values for specific properties.\n   *\n   * The key is a supported event name to be configured,\n   * and the options object contains a set of properties, with override values\n   * to be applied to the named recognizer event.\n   * For example, to disable recognition of the rotate event, specify\n   *  `{\"rotate\": {\"enable\": false}}`.\n   *\n   * Properties that are not present take the HammerJS default values.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   *\n   */\n  overrides = {};\n  /**\n   * Properties whose default values can be overridden for a given event.\n   * Different sets of properties apply to different events.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  options;\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static ɵfac = function HammerGestureConfig_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGestureConfig)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGestureConfig,\n    factory: HammerGestureConfig.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  _config;\n  _injector;\n  loader;\n  _loaderPromise = null;\n  constructor(doc, _config, _injector, loader) {\n    super(doc);\n    this._config = _config;\n    this._injector = _injector;\n    this.loader = loader;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Get a `Console` through an injector to tree-shake the\n        // class when it is unused in production.\n        const _console = this._injector.get(_Console);\n        _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const _console = this._injector.get(_Console);\n            _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          const _console = this._injector.get(_Console);\n          _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static ɵfac = function HammerGesturesPlugin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(HAMMER_LOADER, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HammerGesturesPlugin,\n    factory: HammerGesturesPlugin.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {\n  static ɵfac = function HammerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HammerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HammerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n    }, {\n      provide: HAMMER_GESTURE_CONFIG,\n      useClass: HammerGestureConfig\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static ɵfac = function DomSanitizer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizer,\n    factory: function DomSanitizer_Factory(__ngFactoryType__) {\n      let __ngConditionalFactory__ = null;\n      if (__ngFactoryType__) {\n        __ngConditionalFactory__ = new (__ngFactoryType__ || DomSanitizer)();\n      } else {\n        __ngConditionalFactory__ = i0.ɵɵinject(DomSanitizerImpl);\n      }\n      return __ngConditionalFactory__;\n    },\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  _doc;\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (_allowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (_allowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return _unwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (_allowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (_allowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return _unwrapSafeValue(value);\n        }\n        return __sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (_allowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return _unwrapSafeValue(value);\n        }\n        throw new _RuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${_XSS_SECURITY_URL})`);\n      default:\n        throw new _RuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${_XSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return _bypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return _bypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return _bypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return _bypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return _bypassSanitizationTrustResourceUrl(value);\n  }\n  static ɵfac = function DomSanitizerImpl_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DomSanitizerImpl,\n    factory: DomSanitizerImpl.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n  HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n  HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n  HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, _withHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @developerPreview\n * @publicApi\n */\nfunction withI18nSupport() {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, _withI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, _withEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @experimental\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n  return hydrationFeature(HydrationFeatureKind.IncrementalHydration, _withIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      const isZoneless = inject(_ZONELESS_ENABLED);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (!isZoneless && ngZone.constructor !== NgZone) {\n        const console = inject(_Console);\n        const message = _formatRuntimeError(-5e3 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional hydration behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    // TODO: Make this a runtime error\n    throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], _withDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : _withHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.5');\nexport { By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, Title, VERSION, disableDebugTools, enableDebugTools, provideClientHydration, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,IAAM,OAAN,MAAM,MAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,OAAO,OAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,KAAK,gBAAgB,OAAO;AACjC,QAAI,CAAC,IAAK,QAAO;AACjB,WAAO,KAAK,oBAAoB,KAAK,aAAa;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,MAAM,gBAAgB,OAAO;AACnC,QAAI,CAAC,KAAM,QAAO,CAAC;AACnB,WAAO,KAAK,OAAO,CAAC,QAAQ,QAAQ;AAClC,UAAI,KAAK;AACP,eAAO,KAAK,KAAK,oBAAoB,KAAK,aAAa,CAAC;AAAA,MAC1D;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,cAAc;AACnB,QAAI,CAAC,aAAc,QAAO;AAC1B,WAAO,KAAK,KAAK,cAAc,QAAQ,YAAY,GAAG,KAAK;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,cAAc;AACpB,QAAI,CAAC,aAAc,QAAO,CAAC;AAC3B,UAAM,OAAoB,KAAK,KAAK,iBAAiB,QAAQ,YAAY,GAAG;AAC5E,WAAO,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,KAAK,UAAU;AACvB,QAAI,CAAC,IAAK,QAAO;AACjB,eAAW,YAAY,KAAK,eAAe,GAAG;AAC9C,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,QAAI,MAAM;AACR,aAAO,KAAK,0BAA0B,KAAK,IAAI;AAAA,IACjD;AACA,WAAO,KAAK,oBAAoB,KAAK,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,cAAc;AACtB,SAAK,iBAAiB,KAAK,OAAO,YAAY,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AACrB,QAAI,MAAM;AACR,WAAK,KAAK,OAAO,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,oBAAoB,MAAM,gBAAgB,OAAO;AAC/C,QAAI,CAAC,eAAe;AAClB,YAAM,WAAW,KAAK,eAAe,IAAI;AAIzC,YAAM,OAAO,KAAK,QAAQ,QAAQ,EAAE,OAAO,CAAAA,UAAQ,KAAK,oBAAoB,MAAMA,KAAI,CAAC,EAAE,CAAC;AAC1F,UAAI,SAAS,OAAW,QAAO;AAAA,IACjC;AACA,UAAM,UAAU,KAAK,KAAK,cAAc,MAAM;AAC9C,SAAK,0BAA0B,MAAM,OAAO;AAC5C,UAAM,OAAO,KAAK,KAAK,qBAAqB,MAAM,EAAE,CAAC;AACrD,SAAK,YAAY,OAAO;AACxB,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B,KAAK,IAAI;AACjC,WAAO,KAAK,GAAG,EAAE,QAAQ,UAAQ,GAAG,aAAa,KAAK,eAAe,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;AACtF,WAAO;AAAA,EACT;AAAA,EACA,eAAe,KAAK;AAClB,UAAM,OAAO,IAAI,OAAO,SAAS;AACjC,WAAO,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,EAC9B;AAAA,EACA,oBAAoB,KAAK,MAAM;AAC7B,WAAO,OAAO,KAAK,GAAG,EAAE,MAAM,SAAO,KAAK,aAAa,KAAK,eAAe,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC;AAAA,EAC/F;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,cAAc,IAAI,KAAK;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,aAAa,mBAAmB;AACrD,WAAO,KAAK,qBAAqB,OAAS,SAAS,QAAQ,CAAC;AAAA,EAC9D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,MAAK;AAAA,IACd,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,IAAM,gBAAgB;AAAA,EACpB,WAAW;AACb;AAYA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,UAAU;AACjB,SAAK,KAAK,QAAQ,YAAY;AAAA,EAChC;AAAA,EACA,OAAO,OAAO,SAAS,cAAc,mBAAmB;AACtD,WAAO,KAAK,qBAAqB,QAAU,SAAS,QAAQ,CAAC;AAAA,EAC/D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAM;AAAA,IACf,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AASH,SAAS,YAAY,MAAM,OAAO;AAChC,MAAI,OAAO,aAAa,eAAe,CAAC,UAAU;AAKhD,UAAM,KAAK,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,CAAC;AAC7C,OAAG,IAAI,IAAI;AAAA,EACb;AACF;AACA,IAAM,4BAAN,MAAgC;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,YAAY,WAAW,UAAU;AAC/B,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AACF;AAKA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA,YAAY,KAAK;AACf,SAAK,SAAS,IAAI,SAAS,IAAI,cAAc;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,oBAAoB,QAAQ;AAC1B,UAAM,SAAS,UAAU,OAAO,QAAQ;AACxC,UAAM,cAAc;AAEpB,QAAI,UAAU,aAAa,WAAW,OAAO,QAAQ,YAAY,YAAY;AAC3E,cAAQ,QAAQ,WAAW;AAAA,IAC7B;AACA,UAAM,QAAQ,YAAY,IAAI;AAC9B,QAAI,WAAW;AACf,WAAO,WAAW,KAAK,YAAY,IAAI,IAAI,QAAQ,KAAK;AACtD,WAAK,OAAO,KAAK;AACjB;AAAA,IACF;AACA,UAAM,MAAM,YAAY,IAAI;AAC5B,QAAI,UAAU,gBAAgB,WAAW,OAAO,QAAQ,eAAe,YAAY;AACjF,cAAQ,WAAW,WAAW;AAAA,IAChC;AACA,UAAM,aAAa,MAAM,SAAS;AAClC,YAAQ,IAAI,OAAO,QAAQ,0BAA0B;AACrD,YAAQ,IAAI,GAAG,UAAU,QAAQ,CAAC,CAAC,eAAe;AAClD,WAAO,IAAI,0BAA0B,WAAW,QAAQ;AAAA,EAC1D;AACF;AACA,IAAM,uBAAuB;AAc7B,SAAS,iBAAiB,KAAK;AAC7B,cAAY,sBAAsB,IAAI,gBAAgB,GAAG,CAAC;AAC1D,SAAO;AACT;AAMA,SAAS,oBAAoB;AAC3B,cAAY,sBAAsB,IAAI;AACxC;AAOA,IAAM,KAAN,MAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASP,OAAO,MAAM;AACX,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,IAAI,UAAU;AACnB,WAAO,kBAAgB;AACrB,aAAO,aAAa,iBAAiB,OAAO,eAAe,aAAa,eAAe,QAAQ,IAAI;AAAA,IACrG;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,UAAU,MAAM;AACrB,WAAO,eAAa,UAAU,eAAe,QAAQ,IAAI,MAAM;AAAA,EACjE;AACF;AACA,SAAS,eAAe,GAAG,UAAU;AACnC,MAAI,OAAQ,EAAE,cAAc,CAAC,GAAG;AAC9B,WAAO,EAAE,WAAW,EAAE,QAAQ,QAAQ,KAAK,EAAE,qBAAqB,EAAE,kBAAkB,QAAQ,KAAK,EAAE,yBAAyB,EAAE,sBAAsB,QAAQ;AAAA,EAChK;AACA,SAAO;AACT;AAKA,IAAM,cAAc;AAAA;AAAA,EAElB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,YAAY;AAAA;AAAA,EAEZ,SAAS;AAAA,EACT,WAAW;AAAA;AAAA,EAEX,UAAU;AAAA,EACV,eAAe;AAAA,EACf,cAAc;AAAA,EACd,aAAa;AAAA,EACb,gBAAgB;AAAA;AAAA,EAEhB,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,aAAa;AAAA;AAAA,EAEb,OAAO;AAAA,EACP,aAAa;AACf;AAQA,IAAM,wBAAwB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,wBAAwB,EAAE;AAQ3H,IAAM,gBAAgB,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,iBAAiB,EAAE;AAM5G,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBV,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,SAAS;AACnB,UAAM,KAAK,IAAI,OAAO,SAAS,KAAK,OAAO;AAC3C,OAAG,IAAI,OAAO,EAAE,IAAI;AAAA,MAClB,QAAQ;AAAA,IACV,CAAC;AACD,OAAG,IAAI,QAAQ,EAAE,IAAI;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC;AACD,eAAW,aAAa,KAAK,WAAW;AACtC,SAAG,IAAI,SAAS,EAAE,IAAI,KAAK,UAAU,SAAS,CAAC;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,uBAAN,MAAM,8BAA6B,mBAAmB;AAAA,EACpD;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,YAAY,KAAK,SAAS,WAAW,QAAQ;AAC3C,UAAM,GAAG;AACT,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,CAAC,YAAY,eAAe,UAAU,YAAY,CAAC,KAAK,CAAC,KAAK,cAAc,SAAS,GAAG;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,UAAU,CAAC,KAAK,QAAQ;AAClC,UAAI,OAAO,cAAc,eAAe,WAAW;AAGjD,cAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,iBAAS,KAAK,QAAQ,SAAS,kGAAuG;AAAA,MACxI;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,SAAS,WAAW,SAAS;AAC5C,UAAM,OAAO,KAAK,QAAQ,QAAQ;AAClC,gBAAY,UAAU,YAAY;AAGlC,QAAI,CAAC,OAAO,UAAU,KAAK,QAAQ;AACjC,WAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,MAAM,KAAK,OAAO,CAAC;AAIvF,UAAI,qBAAqB;AACzB,UAAI,aAAa,MAAM;AACrB,6BAAqB;AAAA,MACvB;AACA,WAAK,kBAAkB,MAAM,KAAK,eAAe,KAAK,MAAM;AAE1D,YAAI,CAAC,OAAO,QAAQ;AAClB,cAAI,OAAO,cAAc,eAAe,WAAW;AACjD,kBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,qBAAS,KAAK,mEAAmE;AAAA,UACnF;AACA,uBAAa,MAAM;AAAA,UAAC;AACpB;AAAA,QACF;AACA,YAAI,CAAC,oBAAoB;AAIvB,uBAAa,KAAK,iBAAiB,SAAS,WAAW,OAAO;AAAA,QAChE;AAAA,MACF,CAAC,EAAE,MAAM,MAAM;AACb,YAAI,OAAO,cAAc,eAAe,WAAW;AACjD,gBAAM,WAAW,KAAK,UAAU,IAAI,OAAQ;AAC5C,mBAAS,KAAK,QAAQ,SAAS,qEAA0E;AAAA,QAC3G;AACA,qBAAa,MAAM;AAAA,QAAC;AAAA,MACtB,CAAC,CAAC;AAIF,aAAO,MAAM;AACX,mBAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,KAAK,kBAAkB,MAAM;AAElC,YAAM,KAAK,KAAK,QAAQ,YAAY,OAAO;AAC3C,YAAM,WAAW,SAAU,UAAU;AACnC,aAAK,WAAW,WAAY;AAC1B,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AACA,SAAG,GAAG,WAAW,QAAQ;AACzB,aAAO,MAAM;AACX,WAAG,IAAI,WAAW,QAAQ;AAE1B,YAAI,OAAO,GAAG,YAAY,YAAY;AACpC,aAAG,QAAQ;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,WAAW;AACvB,WAAO,KAAK,QAAQ,OAAO,QAAQ,SAAS,IAAI;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,SAAS,QAAQ,GAAM,SAAS,qBAAqB,GAAM,SAAY,QAAQ,GAAM,SAAS,eAAe,CAAC,CAAC;AAAA,EAC3K;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAYH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,IACnF,GAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM,CAAC,UAAU,uBAAuB,UAAU,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,MACnF,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAiCH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,SAAS,qBAAqB,mBAAmB;AACxD,UAAI,2BAA2B;AAC/B,UAAI,mBAAmB;AACrB,mCAA2B,KAAK,qBAAqB,eAAc;AAAA,MACrE,OAAO;AACL,mCAA8B,SAAS,gBAAgB;AAAA,MACzD;AACA,aAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,aAAa,WAAW,MAAM,gBAAgB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,0BAAyB,aAAa;AAAA,EAC1C;AAAA,EACA,YAAY,MAAM;AAChB,UAAM;AACN,SAAK,OAAO;AAAA,EACd;AAAA,EACA,SAAS,KAAK,OAAO;AACnB,QAAI,SAAS,KAAM,QAAO;AAC1B,YAAQ,KAAK;AAAA,MACX,KAAK,gBAAgB;AACnB,eAAO;AAAA,MACT,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA4B,GAAG;AACzE,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO,cAAe,KAAK,MAAM,OAAO,KAAK,CAAC,EAAE,SAAS;AAAA,MAC3D,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA8B,GAAG;AAC3E,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO;AAAA,MACT,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAAgC,GAAG;AAC7E,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,cAAM,IAAI,aAAc,OAAyD,OAAO,cAAc,eAAe,cAAc,uCAAuC;AAAA,MAC5K,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA0B,GAAG;AACvE,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,eAAO,aAAc,OAAO,KAAK,CAAC;AAAA,MACpC,KAAK,gBAAgB;AACnB,YAAI;AAAA,UAAiC;AAAA,UAAO;AAAA;AAAA,QAA0C,GAAG;AACvF,iBAAO,gBAAiB,KAAK;AAAA,QAC/B;AACA,cAAM,IAAI,aAAc,OAA+D,OAAO,cAAc,eAAe,cAAc,oDAAoD,gBAAiB,GAAG;AAAA,MACnN;AACE,cAAM,IAAI,aAAc,OAA0D,OAAO,cAAc,eAAe,cAAc,8BAA8B,GAAG,SAAS,gBAAiB,GAAG;AAAA,IACtM;AAAA,EACF;AAAA,EACA,wBAAwB,OAAO;AAC7B,WAAO,4BAA6B,KAAK;AAAA,EAC3C;AAAA,EACA,yBAAyB,OAAO;AAC9B,WAAO,6BAA8B,KAAK;AAAA,EAC5C;AAAA,EACA,0BAA0B,OAAO;AAC/B,WAAO,8BAA+B,KAAK;AAAA,EAC7C;AAAA,EACA,uBAAuB,OAAO;AAC5B,WAAO,2BAA4B,KAAK;AAAA,EAC1C;AAAA,EACA,+BAA+B,OAAO;AACpC,WAAO,mCAAoC,KAAK;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,SAAS,QAAQ,CAAC;AAAA,EAC1E;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAQH,IAAI;AAAA,CACH,SAAUC,uBAAsB;AAC/B,EAAAA,sBAAqBA,sBAAqB,qBAAqB,IAAI,CAAC,IAAI;AACxE,EAAAA,sBAAqBA,sBAAqB,0BAA0B,IAAI,CAAC,IAAI;AAC7E,EAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,EAAAA,sBAAqBA,sBAAqB,aAAa,IAAI,CAAC,IAAI;AAChE,EAAAA,sBAAqBA,sBAAqB,sBAAsB,IAAI,CAAC,IAAI;AAC3E,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAItD,SAAS,iBAAiB,OAAO,aAAa,CAAC,GAAG,WAAW,CAAC,GAAG;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,0BAA0B;AAGjC,SAAO,iBAAiB,qBAAqB,mBAAmB;AAClE;AASA,SAAS,6BAA6B,SAAS;AAE7C,SAAO,iBAAiB,qBAAqB,0BAA0B,sBAAuB,OAAO,CAAC;AACxG;AAOA,SAASC,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAkBA,SAASC,mBAAkB;AACzB,SAAO,iBAAiB,qBAAqB,aAAa,gBAAiB,CAAC;AAC9E;AAiBA,SAASC,4BAA2B;AAClC,SAAO,iBAAiB,qBAAqB,sBAAsB,yBAA0B,CAAC;AAChG;AAMA,SAAS,qCAAqC;AAC5C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU,MAAM;AACd,YAAM,SAAS,OAAO,MAAM;AAC5B,YAAM,aAAa,OAAO,gBAAiB;AAG3C,UAAI,CAAC,cAAc,OAAO,gBAAgB,QAAQ;AAChD,cAAMC,WAAU,OAAO,OAAQ;AAC/B,cAAM,UAAU,mBAAoB,MAAyD,sKAAgL;AAC7Q,QAAAA,SAAQ,KAAK,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAkDA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,YAAY,CAAC;AACnB,QAAM,eAAe,oBAAI,IAAI;AAC7B,aAAW;AAAA,IACT;AAAA,IACA;AAAA,EACF,KAAK,UAAU;AACb,iBAAa,IAAI,KAAK;AACtB,QAAI,WAAW,QAAQ;AACrB,gBAAU,KAAK,UAAU;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,8BAA8B,aAAa,IAAI,qBAAqB,wBAAwB;AAClG,MAAI,OAAO,cAAc,eAAe,aAAa,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,6BAA6B;AAE9I,UAAM,IAAI,MAAM,sKAAsK;AAAA,EACxL;AACA,SAAO,yBAAyB,CAAC,OAAO,cAAc,eAAe,YAAY,mCAAmC,IAAI,CAAC,GAAG,iBAAkB,GAAG,aAAa,IAAI,qBAAqB,mBAAmB,KAAK,8BAA8B,CAAC,IAAI,sBAAuB,CAAC,CAAC,GAAG,SAAS,CAAC;AAC1R;AAUA,IAAM,UAAU,IAAI,QAAQ,QAAQ;", "names": ["elem", "HydrationFeatureKind", "withI18nSupport", "withEventReplay", "withIncrementalHydration", "console"]}