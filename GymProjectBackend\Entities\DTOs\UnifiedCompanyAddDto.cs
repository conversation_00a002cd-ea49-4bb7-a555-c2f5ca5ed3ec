using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class UnifiedCompanyAddDto : IDto
    {
        // Kullanıcı bilgileri (Adım 1)
        public string UserFirstName { get; set; }
        public string UserLastName { get; set; }
        public string UserEmail { get; set; }
        public string UserPassword { get; set; }

        // Şirket bilgileri (Adım 2)
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }

        // Adres bilgileri (Adım 3)
        public int CityID { get; set; }
        public int TownID { get; set; }
        public string Address { get; set; }

        // Salon sahibi bilgileri (Adım 4)
        public string OwnerFullName { get; set; }
        public string OwnerPhone { get; set; }
        // OwnerEmail Adım 1'den alınacak
    }
}
