import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { CompanyUserService } from '../../services/company-user.service';
import { CityService } from '../../services/city.service';
import { TownService } from '../../services/town.service';
import { CompanyUserFullDetail } from '../../models/companyUserFullDetail';
import { CompanyUserFullUpdate } from '../../models/companyUserFullUpdate';
import { City } from '../../models/city';
import { Town } from '../../models/town';

@Component({
  selector: 'app-company-user-detail-dialog',
  templateUrl: './company-user-detail-dialog.component.html',
  styleUrls: ['./company-user-detail-dialog.component.css'],
  standalone: false
})
export class CompanyUserDetailDialogComponent implements OnInit {
  companyUserForm: FormGroup;
  companyUser: CompanyUserFullDetail | null = null;
  cities: City[] = [];
  towns: Town[] = [];
  isLoading = false;
  isSubmitting = false;
  activeTab = 'personal'; // 'personal', 'company', 'statistics'
  isReadonly = false; // Yeni readonly modu

  constructor(
    private fb: FormBuilder,
    private companyUserService: CompanyUserService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    public dialogRef: MatDialogRef<CompanyUserDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { companyUserId: number; readonly?: boolean }
  ) {
    this.isReadonly = data.readonly || false;
    this.companyUserForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadCities();
    this.loadCompanyUserDetails();
  }

  createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10,11}$/)]],
      cityID: ['', Validators.required],
      townID: ['', Validators.required],
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^[0-9]{10,11}$/)]],
      companyAddress: ['', [Validators.required, Validators.minLength(5)]],
      companyCityID: [''],
      companyTownID: ['']
    });
  }

  loadCompanyUserDetails(): void {
    this.isLoading = true;
    this.companyUserService.getFullDetails(this.data.companyUserId).subscribe({
      next: (response) => {
        if (response.success) {
          this.companyUser = response.data;
          this.populateForm();
          this.loadTowns(this.companyUser.cityID);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading company user details:', error);
        this.toastrService.error('Kullanıcı detayları yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  populateForm(): void {
    if (this.companyUser) {
      this.companyUserForm.patchValue({
        name: this.companyUser.name,
        email: this.companyUser.email,
        phoneNumber: this.companyUser.phoneNumber,
        cityID: this.companyUser.cityID,
        townID: this.companyUser.townID,
        companyName: this.companyUser.companyName,
        companyPhone: this.companyUser.companyPhone,
        companyAddress: this.companyUser.companyAddress,
        companyCityID: this.companyUser.companyCityID,
        companyTownID: this.companyUser.companyTownID
      });

      // Readonly modda form alanlarını devre dışı bırak
      if (this.isReadonly) {
        this.companyUserForm.disable();
      }
    }
  }

  loadCities(): void {
    this.cityService.getCities().subscribe({
      next: (response) => {
        if (response.success) {
          this.cities = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading cities:', error);
      }
    });
  }

  loadTowns(cityId: number): void {
    if (cityId) {
      this.townService.getTownsByCityId(cityId).subscribe({
        next: (response) => {
          if (response.success) {
            this.towns = response.data;
          }
        },
        error: (error) => {
          console.error('Error loading towns:', error);
        }
      });
    }
  }

  onCityChange(): void {
    const cityId = this.companyUserForm.get('cityID')?.value;
    this.companyUserForm.get('townID')?.setValue('');
    this.towns = [];
    if (cityId) {
      this.loadTowns(cityId);
    }
  }

  onSubmit(): void {
    if (this.companyUserForm.valid && this.companyUser) {
      this.isSubmitting = true;
      
      const formValue = this.companyUserForm.value;
      const originalData = this.companyUser;
      
      const updateDto: CompanyUserFullUpdate = {
        companyUserID: this.companyUser.companyUserID,
        name: formValue.name,
        phoneNumber: formValue.phoneNumber,
        email: formValue.email,
        cityID: formValue.cityID,
        townID: formValue.townID,
        isActive: true, // Default olarak true gönder
        companyID: this.companyUser.companyID,
        companyName: formValue.companyName,
        companyPhone: formValue.companyPhone,
        companyAddress: formValue.companyAddress,
        companyCityID: formValue.companyCityID,
        companyTownID: formValue.companyTownID,
        companyIsActive: this.companyUser.companyIsActive,
        
        // Change tracking
        emailChanged: formValue.email !== originalData.email,
        nameChanged: formValue.name !== originalData.name,
        companyDataChanged: (
          formValue.companyName !== originalData.companyName ||
          formValue.companyPhone !== originalData.companyPhone ||
          formValue.companyAddress !== originalData.companyAddress ||
          formValue.companyCityID !== originalData.companyCityID ||
          formValue.companyTownID !== originalData.companyTownID
        ),
        
        // Old values for comparison
        oldEmail: originalData.email,
        oldName: originalData.name
      };

      this.companyUserService.updateFull(updateDto).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Kullanıcı bilgileri başarıyla güncellendi.', 'Başarılı');
            this.dialogRef.close('updated');
          } else {
            this.toastrService.error(response.message || 'Güncelleme sırasında bir hata oluştu.', 'Hata');
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error updating company user:', error);
          this.toastrService.error('Güncelleme sırasında bir hata oluştu.', 'Hata');
          this.isSubmitting = false;
        }
      });
    } else {
      this.markFormGroupTouched();
      this.toastrService.warning('Lütfen tüm gerekli alanları doldurun.', 'Uyarı');
    }
  }

  markFormGroupTouched(): void {
    Object.keys(this.companyUserForm.controls).forEach(key => {
      const control = this.companyUserForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  getFieldError(fieldName: string): string {
    const field = this.companyUserForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${this.getFieldDisplayName(fieldName)} gereklidir.`;
      if (field.errors['email']) return 'Geçerli bir email adresi girin.';
      if (field.errors['pattern']) return `${this.getFieldDisplayName(fieldName)} formatı geçersiz.`;
      if (field.errors['minlength']) return `${this.getFieldDisplayName(fieldName)} çok kısa.`;
    }
    return '';
  }

  getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      name: 'İsim',
      email: 'Email',
      phoneNumber: 'Telefon',
      cityID: 'Şehir',
      townID: 'İlçe',
      companyName: 'Şirket Adı',
      companyPhone: 'Şirket Telefonu',
      companyAddress: 'Şirket Adresi'
    };
    return displayNames[fieldName] || fieldName;
  }

  getInitials(name: string): string {
    if (!name) return 'CU';
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  }

  getAvatarColor(name: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  }

  getStatusClass(): string {
    if (!this.companyUser) return 'status-inactive';
    if (!this.companyUser.isActive) return 'status-inactive';
    if (!this.companyUser.userIsActive) return 'status-user-inactive';
    if (this.companyUser.requirePasswordChange) return 'status-password-required';
    return 'status-active';
  }

  getStatusText(): string {
    if (!this.companyUser) return 'Bilinmiyor';
    if (!this.companyUser.isActive) return 'Pasif';
    if (!this.companyUser.userIsActive) return 'Kullanıcı Hesabı Pasif';
    if (this.companyUser.requirePasswordChange) return 'Şifre Değiştirmeli';
    return 'Aktif';
  }
}
