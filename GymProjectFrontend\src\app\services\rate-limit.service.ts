import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';

@Injectable({
  providedIn: 'root'
})
export class RateLimitService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  checkLoginBan(deviceInfo: string): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}auth/check-login-ban?deviceInfo=${encodeURIComponent(deviceInfo)}`);
  }

  checkRegisterBan(): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}auth/check-register-ban`);
  }

  getRemainingProfileImageUploads(): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}user/remaining-profile-image-uploads`);
  }

  getRemainingFileDownloads(): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}user/remaining-file-downloads`);
  }

  private requestCounts: { [key: string]: { count: number, lastReset: number } } = {};
  private readonly RATE_LIMITS: { [key: string]: { maxRequests: number, windowMs: number } } = {
    'excel-export': { maxRequests: 5, windowMs: 10 * 60 * 1000 }, // 10 dakikada 5 istek
  };

  canMakeRequest(operation: string): boolean {
    const limit = this.RATE_LIMITS[operation];
    if (!limit) return true;

    const now = Date.now();
    const requestData = this.requestCounts[operation];

    if (!requestData || now - requestData.lastReset > limit.windowMs) {
      this.requestCounts[operation] = { count: 1, lastReset: now };
      return true;
    }

    if (requestData.count >= limit.maxRequests) {
      return false;
    }

    requestData.count++;
    return true;
  }

  formatRemainingTime(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} dakika`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours} saat ${remainingMinutes} dakika` : `${hours} saat`;
  }

  recordFileDownload(): Observable<any> {
    return this.httpClient.post<any>(`${this.apiUrl}user/record-file-download`, {});
  }


}
