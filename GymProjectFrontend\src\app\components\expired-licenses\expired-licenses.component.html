<div class="container-fluid mt-4">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="card-header">
          <h5>Lisansı Dolan Üyeler</h5>
          <div class="d-flex align-items-center gap-3">
            <div class="total-members-badge">
              <span class="modern-badge modern-badge-warning">
                <i class="fas fa-clock me-2"></i>
                Toplam: {{ totalExpiredLicenses }} Lisans
              </span>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="modern-card">
        <div class="card-header">
          <h5>Arama ve Filtreleme</h5>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-8">
              <div class="modern-input-group">
                <input
                  type="text"
                  class="modern-input search-input"
                  placeholder="Şirket adı, kullanıcı adı, e-posta, paket adı ile arayın..."
                  [(ngModel)]="searchTerm"
                  (keyup.enter)="onSearchChange()"
                />
               
              </div>
              <small class="search-help-text">
                Tüm lisans bilgileri ile kapsamlı arama yapabilirsiniz
              </small>
            </div>
            <div class="col-md-4">
              <button
                type="button"
                class="modern-btn modern-btn-primary"
                (click)="onSearchChange()">
                <i class="fas fa-search me-2"></i>Ara
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Results Section -->
  <div class="row">
    <div class="col-12">
      <div class="modern-card">
        <div class="card-header">
          <h5>Lisans Listesi</h5>
          <div class="d-flex align-items-center gap-3">
            <small class="text-muted" *ngIf="paginationData">
              Toplam {{ paginationData.totalCount }} kayıt
            </small>
            <div class="d-flex align-items-center gap-2">
              <label class="modern-label mb-0 me-2">Sayfa başına:</label>
              <select
                class="modern-select"
                style="width: auto;"
                [value]="pageSize"
                (change)="onPageSizeChange(+($any($event.target).value))">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </div>
          </div>
        </div>

        <div class="card-body p-0">
          <!-- Loading -->
          <div *ngIf="isLoading" class="text-center py-5">
            <div class="modern-spinner">
              <div class="spinner"></div>
            </div>
            <p class="mt-3 text-muted">Lisansı Dolan Üyeler yükleniyor...</p>
          </div>

          <!-- No Data -->
          <div *ngIf="!isLoading && userLicenses.length === 0" class="empty-state">
            <div class="empty-state-icon">
              <i class="fas fa-check-circle text-success"></i>
            </div>
            <h4 class="empty-state-title text-success">Harika! Süresi dolan lisans yok</h4>
            <p class="empty-state-description">Tüm lisanslar aktif durumda.</p>
          </div>

          <!-- Data Table -->
          <div *ngIf="!isLoading && userLicenses.length > 0" class="modern-table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-building me-1"></i>Şirket Bilgileri
                  </th>
                  <th>
                    <i class="fas fa-user me-1"></i>Kullanıcı
                  </th>
                  <th>
                    <i class="fas fa-box me-1"></i>Paket
                  </th>
                  <th>
                    <i class="fas fa-user-tag me-1"></i>Rol
                  </th>
                  <th>
                    <i class="fas fa-calendar-times me-1"></i>Bitiş Tarihi
                  </th>
                  <th>
                    <i class="fas fa-info-circle me-1"></i>Durum
                  </th>
                  <th class="text-center">
                    <i class="fas fa-cogs me-1"></i>İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of userLicenses; trackBy: trackByLicenseId">
                  <!-- Company Info -->
                  <td>
                    <div class="company-info">
                      <div class="company-name">{{ item.companyName }}</div>
                      <small class="company-id text-muted">ID: {{ item.userLicenseID }}</small>
                    </div>
                  </td>
                  
                  <!-- User Info -->
                  <td>
                    <div class="fw-bold">{{ item.userName }}</div>
                    <small class="text-muted">{{ item.userEmail }}</small>
                  </td>
                  
                  <!-- Package -->
                  <td>
                    <span class="modern-badge modern-badge-info">{{ item.packageName }}</span>
                  </td>

                  <!-- Role -->
                  <td>
                    <span class="modern-badge modern-badge-secondary">{{ item.role }}</span>
                  </td>

                  <!-- End Date -->
                  <td>
                    <small>{{ item.endDate | date:'dd/MM/yyyy' }}</small>
                  </td>

                  <!-- Status -->
                  <td>
                    <span [class]="getModernStatusBadgeClass(item)">
                      {{ getStatusText(item) }}
                    </span>
                  </td>

                  <!-- Actions -->
                  <td class="text-center">
                    <button
                      type="button"
                      class="modern-btn modern-btn-sm modern-btn-success"
                      (click)="openExtendDialog(item)"
                      title="Lisansı Yenile">
                      <i class="fas fa-redo me-1"></i>Yenile
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div class="modern-card-footer" *ngIf="paginationData && paginationData.totalPages > 1">
          <div class="modern-pagination">
            <!-- Previous Button -->
            <button
              class="modern-pagination-btn"
              [class.disabled]="!paginationData.hasPreviousPage"
              (click)="onPageChange(currentPage - 1)"
              [disabled]="!paginationData.hasPreviousPage">
              <i class="fas fa-chevron-left"></i>
            </button>

            <!-- Page Numbers -->
            <button
              *ngFor="let page of getPageNumbers()"
              class="modern-pagination-btn"
              [class.active]="page === currentPage"
              (click)="onPageChange(page)">
              {{ page }}
            </button>

            <!-- Next Button -->
            <button
              class="modern-pagination-btn"
              [class.disabled]="!paginationData.hasNextPage"
              (click)="onPageChange(currentPage + 1)"
              [disabled]="!paginationData.hasNextPage">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>

          <!-- Pagination Info -->
          <div class="modern-pagination-info">
            <small class="text-muted">
              Sayfa {{ paginationData.pageNumber }} / {{ paginationData.totalPages }}
              ({{ paginationData.totalCount }} toplam kayıt)
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
