﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MembershipTypeManager : IMembershipTypeService
    {
        IMembershiptypeDal _membershipTypeDal;

        public MembershipTypeManager(IMembershiptypeDal membershipTypeDal)
        {
            _membershipTypeDal = membershipTypeDal;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("MembershipType")]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        public IResult Add(MembershipType membershipType)
        {
            _membershipTypeDal.Add(membershipType);
            return new SuccessResult(Messages.MembershipTypeAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("MembershipType")]
        public IResult Delete(int id)
        {
            _membershipTypeDal.Delete(id);
            return new SuccessResult(Messages.MembershipTypeDeleted);
        }
        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 120, "MembershipType", "Configuration")]
        public IDataResult<List<MembershipType>> GetAll()
        {
            return new SuccessDataResult<List<MembershipType>>(_membershipTypeDal.GetAll(m => m.IsActive == true));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MembershipType>> GetAllPaginated(MembershipTypePagingParameters parameters)
        {
            var result = _membershipTypeDal.GetAllPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MembershipType>>(result);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("MembershipType")]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        public IResult Update(MembershipType membershipType)
        {
            _membershipTypeDal.Update(membershipType);
            return new SuccessResult(Messages.MembershipTypeUpdated);
        }
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 120, "MembershipType", "Branch")]
        public IDataResult<List<BranchGetAllDto>> GetBranchesAndTypes()
        {
            var result = _membershipTypeDal.GetAll(m => m.IsActive == true).Select(mt => new BranchGetAllDto  // Sadece aktif olanları getir
            {
                MembershipTypeID = mt.MembershipTypeID,
                Branch = mt.Branch,
                TypeName = mt.TypeName
            }).ToList();

            return new SuccessDataResult<List<BranchGetAllDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "MembershipType", "PackagesByBranch")]
        public IDataResult<List<PackageWithCountDto>> GetPackagesByBranch(string branch)
        {
            return new SuccessDataResult<List<PackageWithCountDto>>(_membershipTypeDal.GetPackagesByBranch(branch));
        }
    }
}
