 using Core.Entities;
using System;

namespace Entities.DTOs
{
    public class MemberWorkoutProgramDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string? MemberPhone { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public int CompanyID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public int DayCount { get; set; } // Program gün sayısı
        public int ExerciseCount { get; set; } // Program egzersiz sayısı
    }

    public class MemberWorkoutProgramAddDto : IDto
    {
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
    }

    public class MemberWorkoutProgramUpdateDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
    }

    public class MemberWorkoutProgramListDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string ProgramName { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
        public int DayCount { get; set; }
        public int ExerciseCount { get; set; }
    }

    /// <summary>
    /// Üyeye atanan program detayı (mobil API için)
    /// </summary>
    public class MemberWorkoutProgramDetailDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public int DayCount { get; set; }
        public int ExerciseCount { get; set; }
        public List<WorkoutProgramDayDto> Days { get; set; } = new List<WorkoutProgramDayDto>();
    }

    // Mobil API için özel DTO
    public class MemberActiveWorkoutProgramDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public int DayCount { get; set; }
        public int ExerciseCount { get; set; }
        // Program detayları (günler ve egzersizler) ayrı endpoint'ten gelecek
    }

    // Atama geçmişi için basit DTO
    public class MemberWorkoutProgramHistoryDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public string ProgramName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
        public string? Notes { get; set; }
        public DateTime? CreationDate { get; set; } // Atama tarihi yerine oluşturma tarihi
    }
}
