<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isInitialLoading}">
  <!-- Initial Loading Spinner -->
  <app-loading-spinner
    *ngIf="isInitialLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
   >
  </app-loading-spinner>

  <!-- Loading Spinner for other operations -->
  <app-loading-spinner
    *ngIf="isLoading && !isInitialLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="false">
  </app-loading-spinner>

  <!-- Main Content - Only show when initial loading is complete -->
  <div *ngIf="!isInitialLoading" class="main-content">

  <div class="row" [class.content-blur]="isInitialLoading">
    <!-- Total Cards -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <div class="col-md-3 mb-3">
          <div class="total-card total-cash">
            <div class="total-icon">
              <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="total-info">
              <h3>{{ totalCash | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p>Nakit Ödemeler</p>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="total-card total-credit-card">
            <div class="total-icon">
              <i class="fas fa-credit-card"></i>
            </div>
            <div class="total-info">
              <h3>{{ totalCreditCard | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p>Kredi Kartı Ödemeleri</p>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="total-card total-transfer">
            <div class="total-icon">
              <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="total-info">
              <h3>{{ totalTransfer | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p>Havale/EFT Ödemeleri</p>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="total-card total-all">
            <div class="total-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="total-info">
              <h3>{{ totalAmount | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p>Toplam Gelir</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Payment Distribution Chart -->
        <div class="col-md-6 mb-4">
          <div class="card h-100">
            <div class="card-header">
              <h5>Ödeme Dağılımı</h5>
            </div>
            <div class="card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="licensePaymentChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="col-md-6 mb-4">
          <div class="card h-100">
            <div class="card-header">
              <h5>Aylık Satış Trendi</h5>
            </div>
            <div class="card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="licenseMonthlySalesChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5>{{ getTotalValuesTitle() }}</h5>
          <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-success"
                    (click)="exportToExcel()"
                    [disabled]="isExporting">
              <i class="fas fa-spinner fa-spin me-1" *ngIf="isExporting"></i>
              <i class="fas fa-file-excel me-1" *ngIf="!isExporting"></i>
              {{ isExporting ? 'Hazırlanıyor...' : 'Excel' }}
            </button>
          </div>
        </div>

        <div class="card-body">
          <!-- Search and Filter -->
          <div class="row mb-4">
            <div class="col-md-4 mb-3">
              <label class="form-label">Admin Ara</label>
              <div class="d-flex">
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-search"></i></span>
                  <input
                    type="text"
                    class="form-control"
                    placeholder="Admin adı veya email"
                    [formControl]="adminSearchControl"
                    [matAutocomplete]="auto"
                  />
                </div>
                <button
                  *ngIf="adminSearchControl.value"
                  class="btn btn-primary ms-2"
                  type="button"
                  (click)="searchAdmin()"
                >
                  Ara
                </button>
                <button
                  *ngIf="selectedAdmin"
                  class="btn btn-outline-secondary ms-2"
                  type="button"
                  (click)="clearAdminFilter()"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayAdmin">
                <mat-option *ngFor="let admin of filteredAdmins | async" [value]="admin">
                  {{ admin.firstName }} {{ admin.lastName }} - {{ admin.email }}
                </mat-option>
              </mat-autocomplete>
            </div>
            <div class="col-md-3 mb-3">
              <label class="form-label">Başlangıç Tarihi</label>
              <div class="input-group">
                <input
                  type="date"
                  class="form-control"
                  [(ngModel)]="startDate"
                  (change)="onFilterChange()"
                />
                <button
                  *ngIf="startDate"
                  class="btn btn-outline-secondary"
                  type="button"
                  (click)="clearStartDateFilter()"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <label class="form-label">Bitiş Tarihi</label>
              <div class="input-group">
                <input
                  type="date"
                  class="form-control"
                  [(ngModel)]="endDate"
                  (change)="onFilterChange()"
                />
                <button
                  *ngIf="endDate"
                  class="btn btn-outline-secondary"
                  type="button"
                  (click)="clearEndDateFilter()"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
            <div class="col-md-2 mb-3 d-flex align-items-end">
              <button
                class="btn btn-secondary w-100"
                [disabled]="!hasActiveFilters()"
                (click)="resetFilters()"
              >
                <i class="fas fa-filter-circle-xmark me-1"></i> Filtreleri Temizle
              </button>
            </div>
          </div>

          <!-- Active Filters -->
          <div class="active-filters" *ngIf="startDate || endDate">
            <div class="d-flex flex-wrap gap-2 mb-3">
              <div class="filter-badge" *ngIf="startDate">
                <span>Başlangıç: {{ startDate | date }}</span>
                <button type="button" class="btn-close ms-2" aria-label="Close" (click)="clearStartDateFilter()"></button>
              </div>
              <div class="filter-badge" *ngIf="endDate">
                <span>Bitiş: {{ endDate | date }}</span>
                <button type="button" class="btn-close ms-2" aria-label="Close" (click)="clearEndDateFilter()"></button>
              </div>
            </div>
          </div>

          <!-- License Transactions Table -->
          <div class="table-responsive">
            <table class="table">
              <thead>
                <tr>
                  <th>Tarih</th>
                  <th>Admin/Salon</th>
                  <th>Paket</th>
                  <th>Ödeme Tipi</th>
                  <th style="text-align: right;">Tutar</th>
                  <th style="text-align: center;">İşlem</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let transaction of transactions; let i = index" [style.animation-delay]="i * 0.05 + 's'">
                  <td>{{ transaction.transactionDate | date:'dd.MM.yyyy HH:mm' }}</td>
                  <td>
                    <div style="display: flex; align-items: center;">
                      <div class="avatar" [style.background-color]="getAvatarColor(getUserName(transaction.userID))" style="margin-right: 0.75rem;">
                        {{ getInitials(getUserName(transaction.userID)) }}
                      </div>
                      <div>
                        <div style="font-weight: 500;">{{ getUserName(transaction.userID) }}</div>
                        <small style="color: var(--text-secondary);">{{ getUserEmail(transaction.userID) }}</small>
                      </div>
                    </div>
                  </td>
                  <td>{{ getPackageName(transaction.licensePackageID) }}</td>
                  <td>
                    <span class="payment-method-badge" [ngClass]="{
                      'payment-cash': transaction.paymentMethod.includes('Nakit'),
                      'payment-credit-card': transaction.paymentMethod.includes('Kredi Kartı'),
                      'payment-transfer': transaction.paymentMethod.includes('Havale'),
                      'payment-debt': transaction.paymentMethod.includes('Uzatma')
                    }">
                      {{ transaction.paymentMethod }}
                    </span>
                  </td>
                  <td style="text-align: right; font-weight: 700;">
                    {{ transaction.amount | currency:'₺':'symbol':'1.2-2':'tr' }}
                  </td>
                  <td style="text-align: center;">
                    <button
                      class="btn btn-sm btn-outline-danger"
                      (click)="deleteTransaction(transaction)"
                      title="İşlemi Sil"
                    >
                      <i class="fas fa-trash-alt"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Empty State -->
          <div class="empty-state" *ngIf="transactions.length === 0">
            <i class="fas fa-receipt fa-3x text-muted"></i>
            <h5 class="mt-3">İşlem Bulunamadı</h5>
            <p class="text-muted">Seçilen kriterlere uygun lisans işlemi bulunamadı.</p>
            <button class="btn btn-primary" (click)="resetFilters()">
              Filtreleri Temizle
            </button>
          </div>

          <!-- Pagination -->
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 1.5rem;" *ngIf="totalPages > 0">
            <div style="color: var(--text-secondary);">
              Toplam {{ totalItems }} kayıttan {{ (currentPage - 1) * itemsPerPage + 1 }} -
              {{ currentPage * itemsPerPage > totalItems ? totalItems : currentPage * itemsPerPage }} arası gösteriliyor
            </div>
            <nav aria-label="Page navigation">
              <ul class="modern-pagination">
                <li class="modern-page-item" [class.disabled]="currentPage === 1">
                  <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(currentPage - 1)" style="border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);">
                    <i class="fas fa-chevron-left"></i>
                  </a>
                </li>
                <li
                  class="modern-page-item"
                  *ngFor="let page of getPaginationRange()"
                  [class.active]="page === currentPage"
                >
                  <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(page)">
                    {{ page }}
                  </a>
                </li>
                <li class="modern-page-item" [class.disabled]="currentPage === totalPages">
                  <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(currentPage + 1)" style="border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;">
                    <i class="fas fa-chevron-right"></i>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>

  </div> <!-- Main Content div kapatma -->
</div>