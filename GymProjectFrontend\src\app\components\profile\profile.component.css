/* <PERSON>il <PERSON> */
.profile-container {
  padding: 20px 0;
}

/* Profil Fotoğrafı Stilleri */
.profile-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.profile-image-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.profile-image-display {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--primary-color);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.profile-image-display:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.default-profile-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-size: 4rem;
}

.profile-image-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.image-preview-section {
  width: 100%;
  max-width: 400px;
  background-color: var(--bg-secondary);
  border-radius: 10px;
  padding: 20px;
  border: 2px dashed var(--border-color);
  transition: all 0.3s ease;
}

.image-preview-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.image-preview {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 10px;
  border: 2px solid var(--primary-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.upload-info {
  text-align: center;
  margin-top: 10px;
}

.upload-rules {
  text-align: center;
  margin-top: 15px;
  padding: 10px;
  background-color: var(--info-light);
  border-radius: 5px;
  border-left: 4px solid var(--info);
}



.upload-status {
  text-align: center;
  margin: 15px 0;
  padding: 15px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.profile-section {
  background-color: var(--bg-primary);
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
  padding: 20px;
  margin-bottom: 30px;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.profile-section:hover {
  box-shadow: var(--shadow-md);
}

.section-title {
  color: var(--primary);
  font-size: 1.5rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--border-color);
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 10px;
  font-size: 1.2em;
  color: var(--primary);
}

.profile-info {
  padding: 10px 0;
}

.info-row {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.info-label {
  font-weight: 600;
  width: 150px;
  color: var(--text-secondary);
}

.info-value {
  flex: 1;
  color: var(--text-primary);
}

.alert-info {
  background-color: var(--info-light);
  border-color: var(--info);
  color: var(--text-primary);
}

.alert-info i {
  color: var(--info);
}

/* Şifre Değiştirme Formu */
.password-form-container {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid var(--border-color);
}

.password-field-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  position: relative;
}

.password-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  color: white;
  border-radius: 5px;
  margin-right: 10px;
  margin-top: 30px;
  position: absolute;
  left: 0;
  z-index: 1;
}

.password-input-wrapper {
  flex: 1;
  padding-left: 50px; /* İkon genişliği + margin */
}

.modern-form-label {
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 5px;
  display: block;
}

.modern-form-control {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
  padding-right: 40px; /* Göz ikonu için yer açma */
}

.modern-form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
  outline: none;
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.input-group {
  display: flex;
  width: 100%;
  position: relative;
}

.password-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}

.password-toggle:hover {
  color: var(--primary);
}

.password-toggle:focus {
  outline: none;
}

.btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-secondary);
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 5px;
}

/* Şifre Gereksinimleri */
.password-requirements {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  margin-top: 20px;
  border-radius: 5px;
}

.password-requirements .card-title {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.requirements-list {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}

.requirements-list li {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  color: var(--text-primary);
}

.requirements-list li i {
  margin-right: 10px;
  font-size: 1.1em;
}

.text-success {
  color: #4caf50 !important;
}

.text-danger {
  color: #f44336 !important;
}

.text-warning {
  color: #ff9800 !important;
}

.fulfilled {
  color: #4caf50;
}

/* Lisans Bilgileri Stilleri */
.license-info-container {
  padding: 10px 0;
}

.license-status-card {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.license-status-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.license-status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.1rem;
  font-weight: 600;
}

.license-status-header i {
  font-size: 1.3rem;
}

.license-status-text {
  flex: 1;
}

/* Lisans durumu renkleri */
.license-status-card.text-success {
  border-left: 4px solid #4caf50;
}

.license-status-card.text-warning {
  border-left: 4px solid #ff9800;
}

.license-status-card.text-danger {
  border-left: 4px solid #f44336;
}

/* Modern badge stilleri */
.modern-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
  transition: all 0.3s ease;
}

.modern-badge-success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.modern-badge-danger {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Butonlar */
.btn-success {
  background-color: #4caf50;
  border-color: #4caf50;
  color: white;
}

.btn-success:hover {
  background-color: #3d8b40;
  border-color: #3d8b40;
}

.btn-success:disabled {
  background-color: #81c784;
  border-color: #81c784;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
  }

  .info-label {
    width: 100%;
    margin-bottom: 5px;
  }

  .info-value {
    width: 100%;
  }

  .card-header h2 {
    font-size: 1.3rem;
  }

  .section-title {
    font-size: 1.2rem;
  }

  .password-field-container {
    margin-bottom: 25px;
  }

  .password-icon {
    width: 35px;
    height: 35px;
    margin-top: 32px;
  }

  .password-input-wrapper {
    padding-left: 45px;
  }

  .modern-form-control {
    font-size: 14px;
  }

  /* Profil fotoğrafı responsive */
  .profile-image-display {
    width: 120px;
    height: 120px;
  }

  .default-profile-icon {
    font-size: 3rem;
  }

  .profile-image-actions {
    flex-direction: column;
    align-items: center;
  }

  .image-preview-section {
    max-width: 100%;
    padding: 15px;
  }

  .image-preview {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 576px) {
  .profile-section {
    padding: 15px;
  }

  .password-form-container {
    padding: 15px;
  }

  .card-header {
    padding: 15px;
  }

  .card-body {
    padding: 15px;
  }

  .password-icon {
    width: 30px;
    height: 30px;
    margin-top: 33px;
  }

  .password-input-wrapper {
    padding-left: 40px;
  }

  .modern-form-control {
    padding: 8px 10px;
    padding-right: 35px;
  }

  .password-toggle {
    width: 35px;
    height: 35px;
  }

  /* Profil fotoğrafı mobile */
  .profile-image-display {
    width: 100px;
    height: 100px;
  }

  .default-profile-icon {
    font-size: 2.5rem;
  }

  .profile-image-actions button {
    font-size: 12px;
    padding: 8px 12px;
  }

  .image-preview {
    width: 80px;
    height: 80px;
  }

  .upload-rules {
    font-size: 12px;
    padding: 8px;
  }

  /* Lisans bilgileri mobile */
  .license-status-header {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .license-status-card {
    padding: 15px;
  }
}