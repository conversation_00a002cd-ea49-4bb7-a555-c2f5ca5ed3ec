export interface CompanyUserFullDetail {
  // CompanyUser bilgileri
  companyUserID: number;
  name: string;
  phoneNumber: string;
  email: string;
  cityID: number;
  townID: number;
  cityName: string;
  townName: string;
  isActive?: boolean;
  creationDate?: Date;
  updatedDate?: Date;
  
  // User bilgileri (ilişkili User tablosundan)
  userID?: number;
  firstName: string;
  lastName: string;
  userIsActive: boolean;
  lastLoginDate?: Date;
  requirePasswordChange: boolean;
  
  // Company bilgileri (UserCompany üzerinden)
  companyID?: number;
  companyName: string;
  companyPhone: string;
  companyAddress: string;
  companyCityID?: number;
  companyTownID?: number;
  companyIsActive?: boolean;

  // İlişki ID'leri
  companyAddressId?: number;
  userCompanyId?: number;
  
  // İstatistik bilgileri
  totalMembers: number;
  activemembers: number;
  monthlyRevenue: number;
  
  // Hesap durumu bilgileri
  accountStatus: string;
  fullDisplayName: string;
}
