/// API Service - GymKod Pro Mobile
///
/// Bu service Dio HTTP client'ı kullanarak backend API'leri ile iletişim kurar.
/// Referans: Angular frontend'deki HTTP service
library;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../models/api_response.dart';
import 'storage_service.dart';
import 'logging_service.dart';

/// API Service
/// Angular frontend'deki HTTP service'e benzer
class ApiService {
  late final Dio _dio;
  final StorageService _storageService = StorageService();

  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;

  ApiService._internal() {
    _initializeDio();
  }

  /// Dio client'ı initialize et
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      validateStatus: (status) {
        // 200-299 arası ve 401, 403 status code'larını valid say
        return (status != null && status >= 200 && status < 300) ||
               status == 401 || status == 403;
      },
    ));

    // Interceptor'ları ekle
    _dio.interceptors.add(_createAuthInterceptor());
    _dio.interceptors.add(_createLoggingInterceptor());
    _dio.interceptors.add(_createErrorInterceptor());
  }

  /// Auth Interceptor - JWT token'ı otomatik ekler
  Interceptor _createAuthInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        try {
          // Auth gerektirmeyen endpoint'ler
          final authFreeEndpoints = [
            '/auth/login',
            '/auth/register-member',
            '/auth/refresh-token',
          ];

          // isAuthFree parametresini kontrol et (TokenRefreshService için)
          final isAuthFreeFromParam = options.extra['isAuthFree'] == true;

          final isAuthFree = isAuthFreeFromParam || authFreeEndpoints.any(
            (endpoint) => options.path.contains(endpoint)
          );

          if (!isAuthFree) {
            final accessToken = await _storageService.getAccessToken();
            if (accessToken != null && accessToken.isNotEmpty) {
              // Sadece token'ı header'a ekle
              // Token refresh'i UnifiedTokenManager hallediyor
              options.headers['Authorization'] = 'Bearer $accessToken';
            }
          }

          LoggingService.apiRequest(options.method, options.path, data: options.data);
          LoggingService.debug('Request headers: ${options.headers}', tag: 'API_DEBUG');
          LoggingService.debug('Request base URL: ${options.baseUrl}', tag: 'API_DEBUG');
          handler.next(options);
        } catch (e, stackTrace) {
          LoggingService.apiError(options.method, options.path, e, stackTrace: stackTrace);
          handler.next(options);
        }
      },
    );
  }

  /// Logging Interceptor - API çağrılarını loglar
  Interceptor _createLoggingInterceptor() {
    return InterceptorsWrapper(
      onResponse: (response, handler) {
        LoggingService.apiResponse(
          response.requestOptions.method,
          response.requestOptions.path,
          response.statusCode ?? 0,
          data: kDebugMode ? response.data : null,
        );
        handler.next(response);
      },
      onError: (error, handler) {
        LoggingService.apiError(
          error.requestOptions.method,
          error.requestOptions.path,
          error,
        );
        handler.next(error);
      },
    );
  }

  /// Error Interceptor - HTTP hatalarını handle eder
  Interceptor _createErrorInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) async {
        final networkError = _handleDioError(error);

        // 401 Unauthorized - Token expired
        if (error.response?.statusCode == 401) {
          LoggingService.authLog('Unauthorized access, attempting token refresh');

          // Token refresh dene
          final refreshed = await _refreshToken();
          if (refreshed) {
            // Request'i tekrar dene
            final newToken = await _storageService.getAccessToken();
            if (newToken != null) {
              error.requestOptions.headers['Authorization'] = 'Bearer $newToken';

              try {
                final response = await _dio.fetch(error.requestOptions);
                handler.resolve(response);
                return;
              } catch (retryError) {
                LoggingService.apiError(
                  error.requestOptions.method,
                  error.requestOptions.path,
                  retryError,
                );
              }
            }
          }

          // Refresh başarısız, kullanıcıyı logout et
          await _storageService.clearAuthTokens();
          LoggingService.authLog('Token refresh failed, user logged out');
        }

        // Custom DioException oluştur
        final customError = DioException(
          requestOptions: error.requestOptions,
          response: error.response,
          type: error.type,
          error: networkError,
          message: networkError.message,
        );

        handler.next(customError);
      },
    );
  }

  /// Dio error'ını NetworkError'a çevir
  NetworkError _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkError(
          message: AppConstants.networkErrorMessage,
          statusCode: null,
          errorCode: 'TIMEOUT',
          originalError: error,
        );

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        String message;

        if (statusCode != null) {
          switch (statusCode) {
            case 400:
              message = 'Geçersiz istek';
              break;
            case 401:
              message = AppConstants.tokenExpiredMessage;
              break;
            case 403:
              message = 'Bu işlem için yetkiniz bulunmuyor';
              break;
            case 404:
              message = 'İstenen kaynak bulunamadı';
              break;
            case 429:
              message = 'Çok fazla istek gönderildi, lütfen bekleyin';
              break;
            case >= 500:
              message = AppConstants.serverErrorMessage;
              break;
            default:
              message = error.response?.data?['message'] ?? AppConstants.unknownErrorMessage;
          }
        } else {
          message = error.response?.data?['message'] ?? AppConstants.unknownErrorMessage;
        }

        return NetworkError(
          message: message,
          statusCode: statusCode,
          errorCode: 'HTTP_ERROR',
          originalError: error,
        );

      case DioExceptionType.cancel:
        return NetworkError(
          message: 'İstek iptal edildi',
          statusCode: null,
          errorCode: 'CANCELLED',
          originalError: error,
        );

      case DioExceptionType.connectionError:
        return NetworkError(
          message: AppConstants.networkErrorMessage,
          statusCode: null,
          errorCode: 'CONNECTION_ERROR',
          originalError: error,
        );

      default:
        return NetworkError(
          message: AppConstants.unknownErrorMessage,
          statusCode: null,
          errorCode: 'UNKNOWN',
          originalError: error,
        );
    }
  }

  /// Token refresh işlemi
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        return false;
      }

      // Device info al
      final deviceInfo = await _storageService.getDeviceInfo();
      final deviceInfoString = deviceInfo?.toDeviceInfoString() ?? 'Unknown Device';

      final response = await _dio.post(
        '/auth/refresh-token',
        data: {
          'refreshToken': refreshToken,
          'deviceInfo': deviceInfoString,
        },
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final data = response.data['data'];
        await _storageService.saveAccessToken(data['token']);
        await _storageService.saveRefreshToken(data['refreshToken']);

        LoggingService.authLog('Token refreshed successfully');
        return true;
      }

      return false;
    } catch (e) {
      LoggingService.authLog('Token refresh failed', details: e.toString());
      return false;
    }
  }

  /// GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool isAuthFree = false,
  }) async {
    try {
      // isAuthFree parametresini options'a ekle
      final requestOptions = options ?? Options();
      if (isAuthFree) {
        requestOptions.extra ??= {};
        requestOptions.extra!['isAuthFree'] = true;
      }

      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: requestOptions,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Dio instance'ını al (özel durumlar için)
  Dio get dio => _dio;

  /// API service'i test et
  Future<bool> testConnection() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      LoggingService.error('API connection test failed', error: e);
      return false;
    }
  }
}

/// API Service Provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});
