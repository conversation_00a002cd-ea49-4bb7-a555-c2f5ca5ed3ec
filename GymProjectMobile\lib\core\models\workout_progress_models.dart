/// Workout Progress Models - GymKod Pro Mobile
///
/// Bu dosya lokal antrenman ilerleme takibi için modeller içerir.
/// SharedPreferences ile lokal depolama yapılacak.
library;

import 'package:json_annotation/json_annotation.dart';

part 'workout_progress_models.g.dart';

/// Antrenman programı ilerleme modeli
/// Lokal SharedPreferences'da saklanacak veriler
@JsonSerializable()
class ProgramProgressModel {
  /// Program ID'si
  final int programId;
  
  /// Günlerin tamamlanma durumları (DayID -> bool)
  final Map<int, bool> dayCompletions;
  
  /// Günlerin tamamlanma tarihleri (DayID -> DateTime)
  final Map<int, DateTime?> completionDates;
  
  /// Mevcut döngü sayısı (1, 2, 3...)
  final int currentCycle;
  
  /// Son aktivite tarihi
  final DateTime? lastActivityDate;
  
  /// Toplam tamamlanan gün sayısı
  final int totalCompletions;
  
  /// Son sıfırlama tarihi
  final DateTime? lastResetDate;

  const ProgramProgressModel({
    required this.programId,
    this.dayCompletions = const {},
    this.completionDates = const {},
    this.currentCycle = 1,
    this.lastActivityDate,
    this.totalCompletions = 0,
    this.lastResetDate,
  });

  /// JSON'dan model oluştur
  factory ProgramProgressModel.fromJson(Map<String, dynamic> json) =>
      _$ProgramProgressModelFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$ProgramProgressModelToJson(this);

  /// Model kopyalama (immutable update için)
  ProgramProgressModel copyWith({
    int? programId,
    Map<int, bool>? dayCompletions,
    Map<int, DateTime?>? completionDates,
    int? currentCycle,
    DateTime? lastActivityDate,
    int? totalCompletions,
    DateTime? lastResetDate,
  }) {
    return ProgramProgressModel(
      programId: programId ?? this.programId,
      dayCompletions: dayCompletions ?? this.dayCompletions,
      completionDates: completionDates ?? this.completionDates,
      currentCycle: currentCycle ?? this.currentCycle,
      lastActivityDate: lastActivityDate ?? this.lastActivityDate,
      totalCompletions: totalCompletions ?? this.totalCompletions,
      lastResetDate: lastResetDate ?? this.lastResetDate,
    );
  }

  /// Belirli bir günün tamamlanıp tamamlanmadığını kontrol et
  bool isDayCompleted(int dayId) {
    return dayCompletions[dayId] ?? false;
  }

  /// Belirli bir günün tamamlanma tarihini al
  DateTime? getDayCompletionDate(int dayId) {
    return completionDates[dayId];
  }

  /// Tüm antrenman günlerinin tamamlanıp tamamlanmadığını kontrol et
  bool isAllWorkoutDaysCompleted(List<int> workoutDayIds) {
    for (final dayId in workoutDayIds) {
      if (!isDayCompleted(dayId)) {
        return false;
      }
    }
    return workoutDayIds.isNotEmpty;
  }

  /// Bu döngüde tamamlanan gün sayısı
  int getCompletedDaysInCurrentCycle() {
    return dayCompletions.values.where((completed) => completed).length;
  }

  /// İlerleme yüzdesi (0.0 - 1.0)
  double getProgressPercentage(int totalWorkoutDays) {
    if (totalWorkoutDays == 0) return 0.0;
    final completedDays = getCompletedDaysInCurrentCycle();
    return completedDays / totalWorkoutDays;
  }
}

/// Gün ilerleme detay modeli
@JsonSerializable()
class DayProgressModel {
  /// Gün ID'si
  final int dayId;
  
  /// Gün numarası (1, 2, 3...)
  final int dayNumber;
  
  /// Gün adı ("Göğüs-Triceps", "Sırt-Biceps" vb.)
  final String dayName;
  
  /// Dinlenme günü mü?
  final bool isRestDay;
  
  /// Tamamlandı mı?
  final bool isCompleted;
  
  /// Tamamlanma tarihi
  final DateTime? completedDate;

  const DayProgressModel({
    required this.dayId,
    required this.dayNumber,
    required this.dayName,
    required this.isRestDay,
    this.isCompleted = false,
    this.completedDate,
  });

  /// JSON'dan model oluştur
  factory DayProgressModel.fromJson(Map<String, dynamic> json) =>
      _$DayProgressModelFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$DayProgressModelToJson(this);

  /// Model kopyalama
  DayProgressModel copyWith({
    int? dayId,
    int? dayNumber,
    String? dayName,
    bool? isRestDay,
    bool? isCompleted,
    DateTime? completedDate,
  }) {
    return DayProgressModel(
      dayId: dayId ?? this.dayId,
      dayNumber: dayNumber ?? this.dayNumber,
      dayName: dayName ?? this.dayName,
      isRestDay: isRestDay ?? this.isRestDay,
      isCompleted: isCompleted ?? this.isCompleted,
      completedDate: completedDate ?? this.completedDate,
    );
  }
}

/// İlerleme istatistikleri modeli
@JsonSerializable()
class ProgressStatsModel {
  /// Program ID'si
  final int programId;
  
  /// Toplam döngü sayısı
  final int totalCycles;
  
  /// Toplam tamamlanan gün sayısı (tüm döngüler)
  final int totalCompletedDays;
  
  /// En uzun streak (ardışık gün sayısı)
  final int longestStreak;
  
  /// Mevcut streak
  final int currentStreak;
  
  /// İlk başlangıç tarihi
  final DateTime? firstStartDate;
  
  /// Son aktivite tarihi
  final DateTime? lastActivityDate;

  const ProgressStatsModel({
    required this.programId,
    this.totalCycles = 1,
    this.totalCompletedDays = 0,
    this.longestStreak = 0,
    this.currentStreak = 0,
    this.firstStartDate,
    this.lastActivityDate,
  });

  /// JSON'dan model oluştur
  factory ProgressStatsModel.fromJson(Map<String, dynamic> json) =>
      _$ProgressStatsModelFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$ProgressStatsModelToJson(this);
}
