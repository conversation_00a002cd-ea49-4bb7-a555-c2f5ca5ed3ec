/* Modern Components CSS - Salon Management System */

:root {
  /* Color Variables */
  --primary: #4361ee;
  --primary-light: rgba(67, 97, 238, 0.15);
  --primary-dark: #3a0ca3;
  --secondary: #6c757d;
  --secondary-light: rgba(108, 117, 125, 0.15);
  --success: #2ecc71;
  --success-light: rgba(46, 204, 113, 0.15);
  --danger: #e74c3c;
  --danger-light: rgba(231, 76, 60, 0.15);
  --warning: #f39c12;
  --warning-light: rgba(243, 156, 18, 0.15);
  --info: #4cc9f0;
  --info-light: rgba(76, 201, 240, 0.15);
  
  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --text-light: #f8f9fa;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-dark: #343a40;
  
  /* Border Colors */
  --border-color: #dee2e6;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* Transitions */
  --transition-speed: 0.3s;
  --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --primary: #4361ee;
  --primary-light: rgba(67, 97, 238, 0.15);
  --primary-dark: #3a0ca3;
  --secondary: #6c757d;
  --secondary-light: rgba(108, 117, 125, 0.15);
  --success: #2ecc71;
  --success-light: rgba(46, 204, 113, 0.15);
  --danger: #e74c3c;
  --danger-light: rgba(231, 76, 60, 0.15);
  --warning: #f39c12;
  --warning-light: rgba(243, 156, 18, 0.15);
  --info: #4cc9f0;
  --info-light: rgba(76, 201, 240, 0.15);
  
  --text-primary: #f8f9fa;
  --text-secondary: #e9ecef;
  --text-muted: #adb5bd;
  --text-light: #f8f9fa;
  
  --bg-primary: #212529;
  --bg-secondary: #343a40;
  --bg-dark: #121212;
  
  --border-color: #495057;
}

/* Modern Card */
.modern-card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--transition-speed) var(--transition-timing);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.modern-card:hover {
  box-shadow: var(--shadow-md);
}

.modern-card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-card-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--text-primary);
}

.modern-card-body {
  padding: var(--spacing-lg);
}

.modern-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* Modern Stats Card */
.modern-stats-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  transition: transform var(--transition-speed) var(--transition-timing);
}

.modern-stats-card:hover {
  transform: translateY(-5px);
}

.modern-stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  color: white;
  font-size: 1.25rem;
}

.modern-stats-info {
  flex: 1;
}

.modern-stats-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.modern-stats-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: var(--spacing-md);
}

.modern-table th,
.modern-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.modern-table th {
  font-weight: 600;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  cursor: pointer;
  user-select: none;
}

.modern-table th:hover {
  background-color: var(--primary-light);
}

.modern-table tbody tr {
  transition: background-color var(--transition-speed) var(--transition-timing);
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

/* Modern Form Controls */
.modern-form-group {
  margin-bottom: var(--spacing-md);
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.modern-form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.modern-form-control:focus {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

/* Modern Buttons */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--border-radius-md);
  transition: color var(--transition-speed) ease-in-out, 
              background-color var(--transition-speed) ease-in-out, 
              border-color var(--transition-speed) ease-in-out, 
              box-shadow var(--transition-speed) ease-in-out;
  cursor: pointer;
}

.modern-btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.modern-btn-icon {
  margin-right: var(--spacing-xs);
}

.modern-btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: var(--border-radius-sm);
}

.modern-btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  border-radius: var(--border-radius-lg);
}

/* Button Variants */
.modern-btn-primary {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.modern-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.modern-btn-success {
  color: white;
  background-color: var(--success);
  border-color: var(--success);
}

.modern-btn-success:hover:not(:disabled) {
  background-color: #27ae60;
  border-color: #27ae60;
}

.modern-btn-danger {
  color: white;
  background-color: var(--danger);
  border-color: var(--danger);
}

.modern-btn-danger:hover:not(:disabled) {
  background-color: #c0392b;
  border-color: #c0392b;
}

.modern-btn-info {
  color: white;
  background-color: var(--info);
  border-color: var(--info);
}

.modern-btn-info:hover:not(:disabled) {
  background-color: #3498db;
  border-color: #3498db;
}

.modern-btn-warning {
  color: white;
  background-color: var(--warning);
  border-color: var(--warning);
}

.modern-btn-warning:hover:not(:disabled) {
  background-color: #e67e22;
  border-color: #e67e22;
}

.modern-btn-secondary {
  color: white;
  background-color: var(--secondary);
  border-color: var(--secondary);
}

.modern-btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
  border-color: #5a6268;
}

/* Outline Button Variants */
.modern-btn-outline-primary {
  color: var(--primary);
  background-color: transparent;
  border-color: var(--primary);
}

.modern-btn-outline-primary:hover:not(:disabled) {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.modern-btn-outline-success {
  color: var(--success);
  background-color: transparent;
  border-color: var(--success);
}

.modern-btn-outline-success:hover:not(:disabled) {
  color: white;
  background-color: var(--success);
  border-color: var(--success);
}

.modern-btn-outline-danger {
  color: var(--danger);
  background-color: transparent;
  border-color: var(--danger);
}

.modern-btn-outline-danger:hover:not(:disabled) {
  color: white;
  background-color: var(--danger);
  border-color: var(--danger);
}

.modern-btn-outline-info {
  color: var(--info);
  background-color: transparent;
  border-color: var(--info);
}

.modern-btn-outline-info:hover:not(:disabled) {
  color: white;
  background-color: var(--info);
  border-color: var(--info);
}

.modern-btn-outline-warning {
  color: var(--warning);
  background-color: transparent;
  border-color: var(--warning);
}

.modern-btn-outline-warning:hover:not(:disabled) {
  color: white;
  background-color: var(--warning);
  border-color: var(--warning);
}

.modern-btn-outline-secondary {
  color: var(--secondary);
  background-color: transparent;
  border-color: var(--secondary);
}

.modern-btn-outline-secondary:hover:not(:disabled) {
  color: white;
  background-color: var(--secondary);
  border-color: var(--secondary);
}

/* Modern Badges */
.modern-badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--border-radius-sm);
}

.modern-badge-primary {
  color: white;
  background-color: var(--primary);
}

.modern-badge-success {
  color: white;
  background-color: var(--success);
}

.modern-badge-danger {
  color: white;
  background-color: var(--danger);
}

.modern-badge-warning {
  color: white;
  background-color: var(--warning);
}

.modern-badge-info {
  color: white;
  background-color: var(--info);
}

.modern-badge-secondary {
  color: white;
  background-color: var(--secondary);
}

/* Modern Pagination */
.modern-pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: var(--border-radius-md);
}

.modern-page-item {
  margin: 0 var(--spacing-xs);
}

.modern-page-item.active .modern-page-link {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.modern-page-item.disabled .modern-page-link {
  color: var(--text-muted);
  pointer-events: none;
  cursor: not-allowed;
}

.modern-page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.modern-page-link:hover {
  color: var(--primary-dark);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

/* Modern Avatar */
.modern-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  background-color: var(--primary);
}

/* Animations */
.fade-in {
  animation: fadeIn var(--transition-speed) var(--transition-timing);
}

.slide-in-left {
  animation: slideInLeft var(--transition-speed) var(--transition-timing);
}

.slide-in-right {
  animation: slideInRight var(--transition-speed) var(--transition-timing);
}

.slide-in-up {
  animation: slideInUp var(--transition-speed) var(--transition-timing);
}

.slide-in-down {
  animation: slideInDown var(--transition-speed) var(--transition-timing);
}

.zoom-in {
  animation: zoomIn var(--transition-speed) var(--transition-timing);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes zoomIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Responsive Utilities */
@media (max-width: 576px) {
  .modern-card-header, .modern-card-body, .modern-card-footer {
    padding: var(--spacing-sm);
  }
  
  .modern-table th, .modern-table td {
    padding: var(--spacing-sm);
  }
  
  .modern-stats-card {
    flex-direction: column;
    text-align: center;
  }
  
  .modern-stats-icon {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }
}
