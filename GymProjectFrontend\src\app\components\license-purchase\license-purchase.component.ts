import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseService } from '../../services/user-license.service';
import { LicensePackage } from '../../models/licensePackage';
import { LicensePurchaseDto } from '../../models/LicensePurchaseDto';
import { User } from '../../models/user';
import { UserService } from '../../services/user-service.service';

@Component({
  selector: 'app-license-purchase',
  templateUrl: './license-purchase.component.html',
  styleUrls: ['./license-purchase.component.css'],
  standalone:false
})
export class LicensePurchaseComponent implements OnInit {
  purchaseForm: FormGroup;
  licensePackages: LicensePackage[] = [];
  isLoading = false;
  isSubmitting = false;
  paymentMethods: string[] = ['Nakit', '<PERSON><PERSON><PERSON>', 'Havale/EFT'];

  // Email validation and autocomplete
  userEmail = '';
  emailValidationState: 'idle' | 'loading' | 'valid' | 'invalid' = 'idle';
  selectedUser: User | null = null;
  filteredUsers: User[] = [];
  private emailValidationTimeout: any;
  
  constructor(
    private fb: FormBuilder,
    private licensePackageService: LicensePackageService,
    private userLicenseService: UserLicenseService,
    private userService: UserService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<LicensePurchaseComponent>
  ) {
    this.purchaseForm = this.fb.group({
      userID: [null, Validators.required],
      licensePackageID: [null, Validators.required],
      paymentMethod: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadLicensePackages();
  }

  loadLicensePackages(): void {
    this.isLoading = true;
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        this.licensePackages = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  /**
   * Email input değişikliği
   */
  onEmailInput(event: any): void {
    this.userEmail = event.target.value;

    // Clear previous timeout
    if (this.emailValidationTimeout) {
      clearTimeout(this.emailValidationTimeout);
    }

    // Reset validation state
    this.emailValidationState = 'idle';
    this.selectedUser = null;
    this.filteredUsers = [];
    this.purchaseForm.patchValue({ userID: null });

    // Debounce search
    if (this.userEmail && this.userEmail.length > 2) {
      this.emailValidationTimeout = setTimeout(() => {
        this.searchUsers();
      }, 300);
    }
  }

  /**
   * Kullanıcı arama
   */
  searchUsers(): void {
    if (!this.userEmail || this.userEmail.length < 2) {
      this.filteredUsers = [];
      return;
    }

    this.emailValidationState = 'loading';

    this.userService.getNonMembersPaginated(1, 10, this.userEmail).subscribe({
      next: (response) => {
        this.filteredUsers = response.data.filter((user: any) =>
          user.email.toLowerCase().includes(this.userEmail.toLowerCase()) ||
          user.firstName.toLowerCase().includes(this.userEmail.toLowerCase()) ||
          user.lastName.toLowerCase().includes(this.userEmail.toLowerCase())
        );

        // Exact email match check
        const exactMatch = this.filteredUsers.find((u: any) =>
          u.email.toLowerCase() === this.userEmail.toLowerCase()
        );

        if (exactMatch) {
          this.selectedUser = exactMatch;
          this.emailValidationState = 'valid';
          this.purchaseForm.patchValue({ userID: exactMatch.userID });
        } else {
          this.emailValidationState = this.filteredUsers.length > 0 ? 'idle' : 'invalid';
        }
      },
      error: (error) => {
        this.emailValidationState = 'invalid';
        this.filteredUsers = [];
      }
    });
  }

  /**
   * Autocomplete'ten kullanıcı seçimi
   */
  selectUserFromAutocomplete(user: User, event: any): void {
    if (event.isUserInput) {
      this.selectedUser = user;
      this.userEmail = user.email;
      this.emailValidationState = 'valid';
      this.purchaseForm.patchValue({ userID: user.userID });
      this.filteredUsers = [];
    }
  }

  /**
   * Autocomplete display function
   */
  displayUserEmail(user: User): string {
    return user ? user.email : '';
  }

  /**
   * Kullanıcı başlangıç harflerini al
   */
  getUserInitials(user: User): string {
    if (!user.firstName && !user.lastName) return 'U';
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  }

  /**
   * Seçili paketi getir
   */
  getSelectedPackage(): LicensePackage | null {
    const selectedId = this.purchaseForm.get('licensePackageID')?.value;
    if (!selectedId) return null;
    return this.licensePackages.find(pkg => pkg.licensePackageID === selectedId) || null;
  }

  /**
   * Form hatalarını kontrol et
   */
  hasFormErrors(): boolean {
    return !this.selectedUser ||
           (this.purchaseForm.get('licensePackageID')?.hasError('required') ?? false) ||
           (this.purchaseForm.get('paymentMethod')?.hasError('required') ?? false);
  }

  /**
   * Submit edilebilir mi kontrol et
   */
  canSubmit(): boolean {
    return this.selectedUser !== null &&
           this.purchaseForm.get('licensePackageID')?.value &&
           this.purchaseForm.get('paymentMethod')?.value &&
           this.emailValidationState === 'valid';
  }

  onSubmit(): void {
    if (this.purchaseForm.invalid) {
      this.toastr.error('Lütfen formu doğru şekilde doldurun', 'Hata');
      return;
    }

    if (!this.selectedUser) {
      this.toastr.error('Lütfen bir kullanıcı seçin', 'Hata');
      return;
    }

    this.isSubmitting = true;

    const formValue = this.purchaseForm.value;
    const licensePurchaseDto: LicensePurchaseDto = {
      userID: formValue.userID,
      licensePackageID: formValue.licensePackageID,
      paymentMethod: formValue.paymentMethod
    };

    this.userLicenseService.purchase(licensePurchaseDto).subscribe({
      next: (response) => {
        this.toastr.success(response.message, 'Başarılı');
        this.dialogRef.close(true);
        this.isSubmitting = false;
      },
      error: (error) => {
        this.toastr.error('Lisans satın alınırken bir hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
