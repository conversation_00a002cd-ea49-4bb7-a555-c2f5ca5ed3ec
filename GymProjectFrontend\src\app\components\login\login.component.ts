import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { RateLimitService } from '../../services/rate-limit.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { TokenResponse } from '../../models/refreshToken';
import { finalize } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
  standalone: false,
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  isLoading: boolean = false;
  passwordVisible: boolean = false; // <PERSON><PERSON>re gö<PERSON>ünürlüğünü takip eder
  isBanned: boolean = false;
  remainingBanTime: string = '';
  private isBrowser: boolean;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private rateLimitService: RateLimitService,
    private toastrService: ToastrService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  ngOnInit(): void {
    this.createLoginForm();
    if (this.isBrowser) {
      this.checkLoginBanStatus();
    }
  }

  createLoginForm() {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required]
    });
  }

  checkLoginBanStatus() {
    if (!this.isBrowser) return;

    const deviceInfo = this.authService.getDeviceInfo();
    this.rateLimitService.checkLoginBan(deviceInfo).subscribe({
      next: (response) => {
        if (response.success && response.isBanned) {
          this.isBanned = true;
          this.remainingBanTime = this.rateLimitService.formatRemainingTime(response.remainingMinutes);
          this.toastrService.error(`Çok fazla başarısız giriş denemesi. ${this.remainingBanTime} sonra tekrar deneyin.`, 'Giriş Engellendi');
        } else {
          this.isBanned = false;
          this.remainingBanTime = '';
        }
      },
      error: (error) => {
        console.error('Ban status check failed:', error);
      }
    });
  }

  login(event: Event) {
    event.preventDefault();

    if (!this.loginForm.valid) {
      this.toastrService.error('Lütfen tüm alanları doldurun');
      return;
    }

    // Her login denemesinden önce ban durumunu kontrol et
    if (!this.isBrowser) {
      this.performLogin();
      return;
    }

    this.isLoading = true;
    const deviceInfo = this.authService.getDeviceInfo();

    this.rateLimitService.checkLoginBan(deviceInfo).subscribe({
      next: (response) => {
        if (response.success && response.isBanned) {
          this.isBanned = true;
          this.remainingBanTime = this.rateLimitService.formatRemainingTime(response.remainingMinutes);
          this.toastrService.error(`Çok fazla başarısız giriş denemesi. ${this.remainingBanTime} sonra tekrar deneyin.`, 'Giriş Engellendi');
          this.isLoading = false;
          return;
        } else {
          this.isBanned = false;
          this.remainingBanTime = '';
          this.performLogin();
        }
      },
      error: (error) => {
        console.error('Ban status check failed:', error);
        // Ban kontrolü başarısız olsa bile login'e devam et
        this.performLogin();
      }
    });
  }

  private performLogin() {
    if (!this.isLoading) {
      this.isLoading = true;
    }

    const loginModel = Object.assign({}, this.loginForm.value);

    this.authService.login(loginModel)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (response: TokenResponse) => {
          if (response.success && response.data) {
            localStorage.setItem('token', response.data.token);
            localStorage.setItem('refreshToken', response.data.refreshToken);

            // Şifre değiştirme zorunluluğu kontrolü
            if (response.requirePasswordChange) {
              this.toastrService.info('İlk girişiniz için şifrenizi değiştirmeniz gerekmektedir.');
              this.router.navigate(['/change-password']);
              return;
            }

            this.toastrService.success(response.message || 'Giriş başarılı');

            // Kullanıcının rolüne göre yönlendirme yap
            // redirectUrl auth.service.ts'de role göre ayarlanır, fallback olarak license-dashboard
            const redirectUrl = response.redirectUrl || '/license-dashboard';

            this.router.navigate([redirectUrl])
              .then(() => {
                console.log('Navigation successful to', redirectUrl);
              })
              .catch(err => {
                console.error('Navigation error:', err);
                this.authService.clearSession();
              });
          } else {
            this.toastrService.error(response.message || 'Giriş başarısız');
          }
        },
        error: (error) => {
          // API'den gelen spesifik hata mesajını göster
          this.toastrService.error(error.message);
          this.authService.clearSession();

          // Ban kontrolü zaten login() metodunda yapıldığı için
          // burada tekrar kontrol etmeye gerek yok
        }
      });
  }

  // Şifre görünürlüğünü değiştirir
  togglePasswordVisibility(): void {
    this.passwordVisible = !this.passwordVisible;
  }

}
