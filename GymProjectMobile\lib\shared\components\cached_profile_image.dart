import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

/// Cached Profile Image Widget
/// 
/// Profil fotoğraflarını cache'leyerek performans optimizasyonu sağlar.
/// Angular frontend'deki profile image component'inin mobil versiyonu.
/// 
/// 100.000+ kullanıcı için optimize edilmiş cache ayarları
class CachedProfileImage extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final double borderRadius;
  final Color? borderColor;
  final double borderWidth;
  final Widget? placeholder;
  final Widget? errorWidget;

  const CachedProfileImage({
    super.key,
    this.imageUrl,
    this.size = 50,
    this.borderRadius = 25,
    this.borderColor,
    this.borderWidth = 0,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        border: borderWidth > 0 && borderColor != null
            ? Border.all(color: borderColor!, width: borderWidth)
            : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: imageUrl != null && imageUrl!.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: imageUrl!,
                width: size,
                height: size,
                fit: BoxFit.cover,
                
                // Memory optimization (100.000+ kullanıcı için)
                memCacheWidth: _getOptimalCacheSize(),
                memCacheHeight: _getOptimalCacheSize(),
                maxWidthDiskCache: _getOptimalDiskCacheSize(),
                maxHeightDiskCache: _getOptimalDiskCacheSize(),
                
                // Cache ayarları
                cacheKey: _generateCacheKey(),
                
                placeholder: (context, url) => placeholder ?? _defaultPlaceholder(),
                errorWidget: (context, url, error) => errorWidget ?? _defaultErrorWidget(),
              )
            : errorWidget ?? _defaultErrorWidget(),
      ),
    );
  }
  
  /// Memory cache için optimal boyut hesapla
  int _getOptimalCacheSize() {
    // Küçük profil fotoğrafları için memory'de daha az yer kapla
    if (size <= 50) return 100;   // 50px -> 100px cache
    if (size <= 100) return 200;  // 100px -> 200px cache
    return 400;                   // Büyük fotoğraflar -> 400px cache
  }
  
  /// Disk cache için optimal boyut hesapla
  int _getOptimalDiskCacheSize() {
    // Disk'te biraz daha büyük tutabiliriz
    if (size <= 50) return 200;   // 50px -> 200px disk cache
    if (size <= 100) return 400;  // 100px -> 400px disk cache
    return 800;                   // Büyük fotoğraflar -> 800px disk cache
  }
  
  /// Cache key oluştur (URL + boyut bazlı)
  String? _generateCacheKey() {
    if (imageUrl == null) return null;
    // URL + boyut kombinasyonu ile unique key
    return '${imageUrl!}_${size.toInt()}';
  }

  /// Default placeholder widget
  Widget _defaultPlaceholder() {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: Icon(
            Icons.person,
            size: size * 0.6,
            color: Colors.white,
          ),
        );
      },
    );
  }

  /// Default error widget
  Widget _defaultErrorWidget() {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: Icon(
            Icons.person,
            size: size * 0.6,
            color: Colors.white,
          ),
        );
      },
    );
  }
}

/// Cache Manager Singleton (Global cache yönetimi)
class ProfileImageCacheManager {
  static final ProfileImageCacheManager _instance = ProfileImageCacheManager._internal();
  factory ProfileImageCacheManager() => _instance;
  ProfileImageCacheManager._internal();

  /// Cache boyutunu kontrol et ve gerekirse temizle
  static Future<void> manageCacheSize() async {
    try {
      // Cache boyutunu kontrol et
      final cacheSize = await _getCacheSize();
      
      // 500MB'dan büyükse eski cache'leri temizle
      if (cacheSize > 500 * 1024 * 1024) { // 500MB
        await _clearOldCache();
      }
    } catch (e) {
      // Cache yönetimi hatası - sessizce devam et
      debugPrint('Cache management error: $e');
    }
  }

  /// Cache boyutunu al (yaklaşık)
  static Future<int> _getCacheSize() async {
    // CachedNetworkImage'in internal cache size'ını almak zor
    // Yaklaşık hesaplama yapabiliriz
    return 0; // TODO: Implement actual cache size calculation
  }

  /// Eski cache'leri temizle
  static Future<void> _clearOldCache() async {
    try {
      // 7 günden eski cache'leri temizle
      await DefaultCacheManager().emptyCache();
    } catch (e) {
      debugPrint('Cache clear error: $e');
    }
  }

  /// Belirli bir kullanıcının cache'ini temizle
  static Future<void> clearUserCache(String userId) async {
    try {
      // Bu kullanıcının tüm profil fotoğraflarını cache'den çıkar
      final patterns = [
        'profile/$userId',
        'user_$userId',
      ];
      
      for (final pattern in patterns) {
        await DefaultCacheManager().removeFile(pattern);
      }
    } catch (e) {
      debugPrint('User cache clear error: $e');
    }
  }
}
