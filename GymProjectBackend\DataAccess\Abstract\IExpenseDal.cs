using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IExpenseDal : IEntityRepository<Expense>
    {
        // Pagination ve gelişmiş filtreleme için yeni metotlar
        PaginatedResult<ExpenseDto> GetExpensesPaginated(ExpensePagingParameters parameters);
        List<ExpenseDto> GetAllExpensesFiltered(ExpensePagingParameters parameters); // Export için
    }
}