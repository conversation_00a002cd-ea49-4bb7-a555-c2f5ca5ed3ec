/// Auth Repository Interface - GymKod Pro Mobile
/// 
/// Bu interface auth işlemlerini tanımlar.
/// Referans: Angular frontend'deki auth service interface
library;

import '../../../../core/models/models.dart';

/// Auth Repository Interface
/// Clean Architecture Domain Layer
abstract class AuthRepository {
  /// Kullanıcı girişi
  /// Angular frontend'deki login metodu
  Future<ApiResponse<AuthData>> login({
    required String email,
    required String password,
    required String deviceInfo,
  });

  /// Üye kaydı
  /// Angular frontend'deki registerMember metodu
  Future<ApiResponse<AuthData>> registerMember({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String deviceInfo,
  });

  /// Admin/Owner kaydı
  /// Angular frontend'deki register metodu
  Future<ApiResponse<AuthData>> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String deviceInfo,
  });

  /// Token yenileme
  /// Angular frontend'deki refreshToken metodu
  Future<ApiResponse<AuthData>> refreshToken({
    required String refreshToken,
    required String deviceInfo,
  });

  /// Şifre değiştirme
  /// Angular frontend'deki changePassword metodu
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  /// Kullanıcı profili alma
  /// Angular frontend'deki getProfile metodu
  Future<ApiResponse<UserModel>> getProfile();

  /// Çıkış yapma (local storage temizleme)
  /// Angular frontend'deki logout metodu
  Future<void> logout();

  /// Giriş durumunu kontrol etme
  /// Angular frontend'deki isAuthenticated metodu
  Future<bool> isAuthenticated();

  /// Mevcut kullanıcıyı alma
  /// Angular frontend'deki getCurrentUser metodu
  Future<UserModel?> getCurrentUser();

  /// Token'ın geçerli olup olmadığını kontrol etme
  Future<bool> isTokenValid();

  /// Token'ın yenilenmesi gerekip gerekmediğini kontrol etme
  Future<bool> shouldRefreshToken();
}
