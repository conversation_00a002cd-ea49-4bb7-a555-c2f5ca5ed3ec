﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfTransactionDal : EfCompanyEntityRepositoryBase<Transaction, GymContext>, ITransactionDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfTransactionDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }
        public List<TransactionDetailDto> GetTransactionsWithDetails()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from t in context.Transactions
                             join m in context.Members on t.MemberID equals m.MemberID
                             join p in context.Products on t.ProductID equals p.ProductID into productJoin
                             from p in productJoin.DefaultIfEmpty()
                             where t.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && m.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                             && (p == null || p.CompanyID == companyId) // Ürünlerin de aynı şirkete ait olduğundan emin ol
                             && t.IsActive == true // Sadece aktif (silinmemiş) işlemler
                             orderby t.TransactionDate descending // Tarih bazlı sıralama eklendi
                             select new TransactionDetailDto
                             {
                                 TransactionID = t.TransactionID,
                                 MemberID = t.MemberID,
                                 MemberName = m.Name,
                                 ProductID = t.ProductID,
                                 ProductName = p != null ? p.Name : null,
                                 Amount = t.Amount,
                                 UnitPrice = t.UnitPrice,
                                 TransactionType = t.TransactionType,
                                 TransactionDate = t.TransactionDate,
                                 Quantity = t.Quantity,
                                 IsPaid = t.IsPaid,
                                 Balance = m.Balance,
                                 TotalPrice = t.TransactionType == "Satış" ? t.UnitPrice * t.Quantity : t.Amount
                             };

                return result.ToList();
            }
        }
    }
}
