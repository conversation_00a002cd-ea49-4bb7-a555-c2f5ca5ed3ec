﻿using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class UserCompanyValidator : AbstractValidator<UserCompany>
    {
        public UserCompanyValidator()
        {
            RuleFor(p => p.UserID).NotEmpty().WithMessage("Kullanıcı seçimi boş bırakılamaz.");
            RuleFor(p => p.CompanyId).NotEmpty().WithMessage("Şirket seçimi boş bırakılamaz.");
            RuleFor(p => p).Must(BeUniqueCompanyName).WithMessage("Bu şirket zaten bir kullanıcıya bağlı!");
        }

        private bool BeUniqueCompanyName(UserCompany userCompany)
        {
            using (var context = new GymContext())
            {
                if (userCompany.UserCompanyID != 0)
                {
                    // Güncelleme durumu
                    return !context.UserCompanies.Any(uc =>
                        uc.CompanyId == userCompany.CompanyId &&
                        uc.UserCompanyID != userCompany.UserCompanyID &&
                        uc.IsActive == true);
                }
                else
                {
                    // Yeni ekleme durumu
                    return !context.UserCompanies.Any(uc =>
                        uc.CompanyId == userCompany.CompanyId &&
                        uc.IsActive == true);
                }
            }
        }
    }
}