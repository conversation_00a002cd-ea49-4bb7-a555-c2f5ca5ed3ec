/// Navigation Helper Service - GymKod Pro Mobile
///
/// Bu service kullanıcı durumuna göre doğru sayfaya yönlendirme yapar.
/// İlk giriş, şifre değiştirme, role-based navigation gibi durumları handle eder.
library;

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../models/user_model.dart';
import 'services.dart';

/// Navigation Helper Service
/// Kullanıcı durumuna göre akıllı yönlendirme yapar
class NavigationHelperService {
  static final NavigationHelperService _instance = NavigationHelperService._internal();
  factory NavigationHelperService() => _instance;
  NavigationHelperService._internal();

  final StorageService _storageService = StorageService();

  /// Login sonrası yönlendirme
  /// Kullanıcının durumuna göre doğru sayfaya yönlendirir
  Future<void> handlePostLoginNavigation(
    BuildContext context, 
    UserModel user, 
    bool requirePasswordChange
  ) async {
    try {
      LoggingService.authLog('Handling post-login navigation', 
        details: 'User: ${user.name}, Role: ${user.role}, RequirePasswordChange: $requirePasswordChange');

      if (requirePasswordChange) {
        // Şifre değiştirme zorunluluğu var
        LoggingService.navigationLog('Post-login redirect - password change required', '/auth/change-password?required=true');
        if (context.mounted) context.go('/auth/change-password?required=true');
        return;
      }

      // Role-based navigation
      if (context.mounted) {
        await _handleRoleBasedNavigation(context, user);
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'NavigationHelperService handlePostLoginNavigation');
      
      // Fallback navigation
      if (context.mounted) context.go('/home');
    }
  }

  /// Şifre değiştirme sonrası yönlendirme
  /// İlk giriş kontrolü yaparak doğru sayfaya yönlendirir
  Future<void> handlePostPasswordChangeNavigation(
    BuildContext context, 
    UserModel user,
    bool wasRequired
  ) async {
    try {
      LoggingService.authLog('Handling post-password-change navigation', 
        details: 'User: ${user.name}, Role: ${user.role}, WasRequired: $wasRequired');

      if (!wasRequired) {
        // Normal şifre değiştirme ise geri dön
        if (context.mounted) context.pop();
        return;
      }

      // Zorunlu şifre değiştirme sonrası - ilk giriş kontrolü yap
      final isFirstLogin = await _checkIfFirstLogin(user);
      
      if (context.mounted) {
        if (isFirstLogin) {
          LoggingService.authLog('First login detected - showing welcome flow');
          await _handleFirstLoginNavigation(context, user);
        } else {
          LoggingService.authLog('Regular login - normal navigation');
          await _handleRoleBasedNavigation(context, user);
        }
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'NavigationHelperService handlePostPasswordChangeNavigation');

      // Fallback navigation
      if (context.mounted) {
        await _handleRoleBasedNavigation(context, user);
      }
    }
  }

  /// İlk giriş kontrolü
  /// Kullanıcının daha önce uygulamayı kullanıp kullanmadığını kontrol eder
  Future<bool> _checkIfFirstLogin(UserModel user) async {
    try {
      // Storage'da first login flag'i kontrol et
      final hasCompletedFirstLogin = await _storageService.getBool('has_completed_first_login_${user.userId}') ?? false;
      
      LoggingService.authLog('First login check', 
        details: 'UserId: ${user.userId}, HasCompletedFirstLogin: $hasCompletedFirstLogin');

      return !hasCompletedFirstLogin;
    } catch (e) {
      LoggingService.authLog('First login check failed', details: e.toString());
      // Hata durumunda güvenli tarafta kal - first login değil say
      return false;
    }
  }

  /// İlk giriş sonrası yönlendirme
  /// Kullanıcıya hoş geldin akışı gösterir
  Future<void> _handleFirstLoginNavigation(BuildContext context, UserModel user) async {
    try {
      final userRole = user.role.toLowerCase();

      if (userRole == 'member') {
        // Member için ana sayfa ile başla ve welcome göster
        LoggingService.navigationLog('First login redirect - member with welcome', '/member-main');
        
        // First login flag'ini set et
        await _storageService.setBool('has_completed_first_login_${user.userId}', true);
        
        if (context.mounted) {
          context.go('/member-main');

          // Daha uzun bir delay sonra welcome dialog göster (sayfa yüklendikten sonra)
          Future.delayed(const Duration(milliseconds: 1500), () {
            if (context.mounted) {
              _showWelcomeDialog(context, user);
            }
          });
        }
      } else {
        // Admin/Owner için normal navigation
        await _handleRoleBasedNavigation(context, user);
        await _storageService.setBool('has_completed_first_login_${user.userId}', true);
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'NavigationHelperService _handleFirstLoginNavigation');
      if (context.mounted) {
        await _handleRoleBasedNavigation(context, user);
      }
    }
  }

  /// Role-based navigation
  /// Kullanıcının rolüne göre doğru sayfaya yönlendirir
  Future<void> _handleRoleBasedNavigation(BuildContext context, UserModel user) async {
    try {
      final userRole = user.role.toLowerCase();

      LoggingService.authLog('Role-based navigation', details: 'User role: $userRole');

      if (userRole == 'member') {
        LoggingService.navigationLog('Role-based redirect - member', '/member-main');
        if (context.mounted) context.go('/member-main');
      } else if (userRole == 'admin' || userRole == 'owner') {
        // İlerde admin dashboard'a yönlendirilecek
        LoggingService.navigationLog('Role-based redirect - admin/owner', '/home');
        if (context.mounted) context.go('/home');
      } else {
        // Varsayılan olarak home'a git
        LoggingService.navigationLog('Role-based redirect - default', '/home');
        if (context.mounted) context.go('/home');
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'NavigationHelperService _handleRoleBasedNavigation');
      
      // Fallback
      if (context.mounted) context.go('/home');
    }
  }

  /// Welcome dialog göster
  /// İlk giriş yapan kullanıcılara hoş geldin mesajı gösterir
  void _showWelcomeDialog(BuildContext context, UserModel user) {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Row(
              children: [
                Icon(Icons.celebration, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text('Hoş Geldiniz!'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Merhaba ${user.firstName},',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  'GymKod Pro mobil uygulamasına hoş geldiniz! '
                  'Artık QR kodunuzu kullanarak kolayca giriş yapabilir, '
                  'antrenman programınızı takip edebilirsiniz.',
                ),
                const SizedBox(height: 16),
                const Text(
                  'Ana özellikler:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('• QR kod ile hızlı giriş'),
                const Text('• Antrenman programı takibi'),
                const Text('• Profil yönetimi'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  LoggingService.authLog('Welcome dialog dismissed');
                },
                child: const Text('Anladım'),
              ),
            ],
          );
        },
      );
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'NavigationHelperService _showWelcomeDialog');
    }
  }

  /// Splash screen'den yönlendirme
  /// Kullanıcının durumuna göre doğru sayfaya yönlendirir
  Future<String> getDefaultRouteForUser(UserModel? user) async {
    try {
      if (user == null) {
        return '/auth/login';
      }

      final userRole = user.role.toLowerCase();
      
      switch (userRole) {
        case 'member':
          return '/member-main';
        case 'admin':
        case 'owner':
          return '/home';
        default:
          return '/home';
      }
    } catch (e) {
      LoggingService.authLog('Get default route failed', details: e.toString());
      return '/auth/login';
    }
  }

  /// First login flag'ini reset et (debug için)
  Future<void> resetFirstLoginFlag(int userId) async {
    try {
      await _storageService.remove('has_completed_first_login_$userId');
      LoggingService.authLog('First login flag reset', details: 'UserId: $userId');
    } catch (e) {
      LoggingService.authLog('Reset first login flag failed', details: e.toString());
    }
  }

  /// Service'i dispose et
  void dispose() {
    // Cleanup if needed
  }
}
