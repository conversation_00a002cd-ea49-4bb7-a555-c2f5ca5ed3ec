import { Component, OnInit, Renderer2, Inject, PLATFORM_ID } from '@angular/core';
import { Router, Event, NavigationEnd, NavigationStart } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import { AuthService } from './services/auth.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
  standalone: false
})
export class AppComponent implements OnInit {
  title = 'gymproject';
  showNavbar: boolean = false;
  sidebarCollapsed: boolean = false;
  isDarkMode: boolean = true;
  isBrowser: boolean;
  isInitializing: boolean = true;

  constructor(
    private router: Router, 
    private renderer: Renderer2,
    private authService: AuthService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  ngOnInit() {
    if (this.isBrowser) {
      // Add initializing class to body to prevent content flash
      document.body.classList.add('initializing');
      
      // Check for saved theme preference
      this.loadThemePreference();
      
      // Check for saved sidebar state
      this.loadSidebarState();
      
      // Add responsive behavior
      this.handleResponsiveLayout();
      window.addEventListener('resize', () => {
        this.handleResponsiveLayout();
      });
      
      // Check authentication status before showing content
      this.checkAuthAndInitialize();
    }
    
    // NavigationStart ve NavigationEnd eventlerini dinle
    this.router.events.subscribe((event: Event) => {
      if (event instanceof NavigationStart || event instanceof NavigationEnd) {
        const currentUrl = this.router.url;
        this.showNavbar = !['/login', '/', '/qr', '/register'].includes(currentUrl);
        
        // On mobile, collapse sidebar when navigating
        if (this.isBrowser && window.innerWidth < 992) {
          this.sidebarCollapsed = true;
        }
      }
    });
  }
  
  // Check authentication and initialize app
  private checkAuthAndInitialize() {
    // If we have tokens, check if they're valid
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');
    
    // Get the current path from window.location instead of router.url
    // This ensures we have the correct path even during page refresh
    const currentPath = window.location.pathname;
    const isLoginPage = currentPath === '/login' || currentPath === '/';
    
    if (token && refreshToken) {
      // Check if token is still valid
      if (this.authService.isAuthenticated()) {
        // Token is still valid, no need to refresh
        // If user should be on login page, redirect to main page
        if (isLoginPage) {
          this.router.navigate(['/todayentries']);
        }
        
        // Complete initialization
        this.completeInitialization();
      } else {
        // Token is expired, try to refresh
        this.authService.refreshToken().subscribe({
          next: (response) => {
            // Token refreshed successfully
            if (response.success) {
              // If user should be on login page, redirect to main page
              if (isLoginPage) {
                this.router.navigate(['/todayentries']);
              }
            }
            // Complete initialization
            this.completeInitialization();
          },
          error: () => {
            // Token refresh failed, clear tokens
            this.authService.clearSession();
            // Complete initialization
            this.completeInitialization();
          }
        });
      }
    } else {
      // No tokens, complete initialization immediately
      this.completeInitialization();
    }
  }
  
  // Complete initialization and remove loading state
  private completeInitialization() {
    this.isInitializing = false;
    // Remove initializing class with a small delay to ensure smooth transition
    setTimeout(() => {
      document.body.classList.remove('initializing');
    }, 100);
  }
  
  // Toggle sidebar collapsed state
  toggleSidebar() {
    this.sidebarCollapsed = !this.sidebarCollapsed;
    if (this.isBrowser) {
      localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed.toString());
    }
  }
  
  // Toggle dark mode
  toggleDarkMode() {
    this.isDarkMode = !this.isDarkMode;
    
    if (this.isDarkMode) {
      this.renderer.setAttribute(document.documentElement, 'data-theme', 'dark');
    } else {
      this.renderer.removeAttribute(document.documentElement, 'data-theme');
    }
    
    if (this.isBrowser) {
      localStorage.setItem('darkMode', this.isDarkMode.toString());
    }
  }
  
  // Load saved theme preference
  private loadThemePreference() {
    if (!this.isBrowser) return;

    const savedTheme = localStorage.getItem('darkMode');

    if (savedTheme === 'true') {
      this.isDarkMode = true;
      this.renderer.setAttribute(document.documentElement, 'data-theme', 'dark');
    } else if (savedTheme === 'false') {
      this.isDarkMode = false;
      this.renderer.removeAttribute(document.documentElement, 'data-theme');
    } else {
      // İlk defa giren kullanıcılar için varsayılan karanlık tema
      this.isDarkMode = true;
      this.renderer.setAttribute(document.documentElement, 'data-theme', 'dark');
    }
  }
  
  // Load saved sidebar state
  private loadSidebarState() {
    if (!this.isBrowser) return;
    
    const savedState = localStorage.getItem('sidebarCollapsed');
    
    if (savedState === 'true') {
      this.sidebarCollapsed = true;
    } else if (savedState === 'false') {
      this.sidebarCollapsed = false;
    } else {
      // Default state based on screen size
      this.sidebarCollapsed = window.innerWidth < 992;
    }
  }
  
  // Handle responsive layout
  private handleResponsiveLayout() {
    if (!this.isBrowser) return;
    
    if (window.innerWidth < 992 && !this.sidebarCollapsed) {
      this.sidebarCollapsed = true;
      localStorage.setItem('sidebarCollapsed', 'true');
    }
  }
}
