/* Enhanced Exercise Selection Modal Styles */

/* Modal Container Override */
::ng-deep .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  max-height: 95vh !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
}

/* Exercise Selection Dialog Panel */
::ng-deep .exercise-selection-dialog .mat-mdc-dialog-container {
  z-index: 1050 !important;
}

::ng-deep .exercise-selection-dialog .cdk-overlay-backdrop {
  z-index: 1049 !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
}

::ng-deep .mat-mdc-dialog-content {
  padding: 0 !important;
  margin: 0 !important;
  max-height: none !important;
  overflow: hidden !important;
}

/* Main Modal Container */
.exercise-selection-modal {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 600px;
  max-height: 95vh;
  background: var(--bg-primary);
}

/* Enhanced Modal Header */
.enhanced-modal-header {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.enhanced-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.header-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  backdrop-filter: blur(10px);
}

.header-text .modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.header-text .modal-subtitle {
  margin: 0.125rem 0 0 0;
  font-size: 0.8rem;
  opacity: 0.85;
  font-weight: 400;
}

.enhanced-close-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.enhanced-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Enhanced Modal Body */
.enhanced-modal-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
}

/* Enhanced Filters Section */
.enhanced-filters-section {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 0.75rem;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.filters-title {
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.clear-all-btn {
  background: var(--danger-light);
  color: var(--danger);
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.clear-all-btn:hover {
  background: var(--danger);
  color: white;
  transform: translateY(-1px);
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1rem;
  align-items: end;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.375rem;
  display: flex;
  align-items: center;
}

.label-icon {
  color: var(--primary);
  margin-right: 0.5rem;
}

/* Enhanced Search Input */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.enhanced-search-input {
  width: 100%;
  padding: 0.625rem 0.75rem 0.625rem 2.25rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  outline: none;
}

.enhanced-search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.enhanced-search-input::placeholder {
  color: var(--text-secondary);
  font-style: italic;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  z-index: 1;
}

.clear-search-btn {
  position: absolute;
  right: 0.375rem;
  width: 20px;
  height: 20px;
  background: var(--text-secondary);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  transition: all 0.3s ease;
}

.clear-search-btn:hover {
  background: var(--danger);
  transform: scale(1.1);
}

/* Enhanced Select Wrapper */
.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.enhanced-select {
  width: 100%;
  padding: 0.625rem 2rem 0.625rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  appearance: none;
}

.enhanced-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.select-arrow {
  position: absolute;
  right: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.8rem;
  pointer-events: none;
  transition: all 0.3s ease;
}

.enhanced-select:focus + .select-arrow {
  color: var(--primary);
  transform: rotate(180deg);
}

/* Results Info Section */
.results-info-section {
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.results-stats {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.stats-icon {
  color: var(--primary);
  font-size: 0.85rem;
}

.stats-text {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-primary);
}

.selected-info .stats-text {
  color: var(--success);
}

/* Enhanced Loading Section */
.enhanced-loading-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: var(--bg-primary);
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 2rem;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  border-top-color: var(--secondary);
  animation-duration: 1.2s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  border-top-color: var(--success);
  animation-duration: 0.9s;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.1rem;
  color: var(--text-secondary);
  font-weight: 500;
  margin: 0;
}

/* Enhanced Exercises List */
.enhanced-exercises-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem;
  background: var(--bg-primary);
}

.enhanced-exercise-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(10px);
}

.enhanced-exercise-card.animate-in {
  animation: slideInUp 0.3s ease-out forwards;
}

.enhanced-exercise-card:hover {
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-exercise-card.selected {
  border-color: var(--primary);
  background: var(--primary-light);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.2);
}

.enhanced-exercise-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--primary);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Exercise Card Components */
.exercise-card-header {
  padding: 0.5rem 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.exercise-main-info {
  flex: 1;
  min-width: 0;
}

.exercise-name {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.exercise-category-tag {
  display: inline-flex;
  align-items: center;
  background: var(--primary-light);
  color: var(--primary);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.exercise-badges-group {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  flex-shrink: 0;
}

.enhanced-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
}

.type-badge.modern-badge-primary {
  background: var(--primary-light);
  color: var(--primary);
}

.type-badge.modern-badge-info {
  background: var(--info-light);
  color: var(--info);
}

.difficulty-badge.modern-badge-success {
  background: var(--success-light);
  color: var(--success);
}

.difficulty-badge.modern-badge-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.difficulty-badge.modern-badge-danger {
  background: var(--danger-light);
  color: var(--danger);
}

.exercise-card-body {
  padding: 0.375rem 0.75rem 0.5rem 0.75rem;
  border-top: 1px solid var(--border-color);
}

.exercise-description {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  line-height: 1.4;
  font-size: 0.85rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.exercise-details-grid {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.detail-icon {
  color: var(--primary);
  font-size: 0.85rem;
  flex-shrink: 0;
}

.detail-content {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.detail-label {
  font-weight: 500;
}

.detail-value {
  color: var(--text-primary);
  font-weight: 500;
}

/* Enhanced Selection Indicator */
.enhanced-selection-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--primary-rgb), 0.05));
  backdrop-filter: blur(1px);
}

.selection-checkmark {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.4);
  animation: checkmarkPop 0.3s ease-out;
}

@keyframes checkmarkPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Enhanced Empty State */
.enhanced-empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-state-icon {
  width: 80px;
  height: 80px;
  background: var(--bg-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  border: 3px solid var(--border-color);
}

.empty-state-title {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.empty-state-description {
  margin: 0 0 2rem 0;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 400px;
}

.empty-state-action {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.empty-state-action:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.3);
}

/* Enhanced Pagination */
.enhanced-pagination-section {
  padding: 0.5rem 0.75rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.pagination-text {
  font-size: 0.8rem;
  color: var(--text-primary);
  font-weight: 500;
}

.pagination-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  border-color: var(--primary);
  background: var(--primary-light);
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-number-btn {
  width: 32px;
  height: 32px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number-btn:hover {
  border-color: var(--primary);
  background: var(--primary-light);
  transform: translateY(-1px);
}

.page-number-btn.active {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

/* Enhanced Modal Footer */
.enhanced-modal-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
}

.footer-info {
  flex: 1;
}

.selected-exercise-info,
.no-selection-info {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
}

.selected-text {
  color: var(--success);
  font-weight: 500;
}

.info-text {
  color: var(--text-secondary);
  font-style: italic;
}

.footer-actions {
  display: flex;
  gap: 0.75rem;
}

.enhanced-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  justify-content: center;
}

.cancel-btn {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.cancel-btn:hover {
  border-color: var(--danger);
  background: var(--danger-light);
  color: var(--danger);
  transform: translateY(-1px);
}

.confirm-btn {
  background: var(--primary);
  color: white;
  border: 1px solid var(--primary);
}

.confirm-btn:hover:not(.disabled) {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.3);
}

.confirm-btn.disabled {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-color);
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-icon {
  font-size: 0.8rem;
}

.btn-text {
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .exercise-details-grid {
    grid-template-columns: 1fr;
  }

  .enhanced-pagination-section {
    flex-direction: column;
    gap: 1rem;
  }

  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .enhanced-modal-header {
    padding: 0.5rem 0.75rem;
  }

  .header-text .modal-title {
    font-size: 1.1rem;
  }

  .header-text .modal-subtitle {
    font-size: 0.75rem;
  }

  .enhanced-filters-section {
    padding: 0.75rem;
  }

  .filters-header {
    margin-bottom: 0.75rem;
  }

  .exercise-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
  }

  .exercise-badges-group {
    align-items: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .exercise-card-body {
    padding: 0.5rem 0.75rem;
  }

  .exercise-details-grid {
    flex-direction: column;
    gap: 0.5rem;
  }

  .enhanced-modal-footer {
    flex-direction: column;
    align-items: stretch;
  }

  .footer-actions {
    width: 100%;
  }

  .enhanced-btn {
    flex: 1;
  }

  .page-numbers {
    flex-wrap: wrap;
    justify-content: center;
  }

  .pagination-btn .btn-text {
    display: none;
  }
}

/* Dark Mode Enhancements */
[data-theme="dark"] .enhanced-modal-header {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

[data-theme="dark"] .enhanced-filters-section {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .enhanced-search-input,
[data-theme="dark"] .enhanced-select {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .enhanced-search-input:focus,
[data-theme="dark"] .enhanced-select:focus {
  border-color: var(--primary);
  background-color: var(--bg-primary);
}

[data-theme="dark"] .results-info-section {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .enhanced-exercise-card {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .enhanced-exercise-card:hover {
  background-color: var(--bg-secondary);
}

[data-theme="dark"] .enhanced-exercise-card.selected {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

[data-theme="dark"] .exercise-card-header {
  border-color: var(--border-color);
}

[data-theme="dark"] .enhanced-pagination-section {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .pagination-btn,
[data-theme="dark"] .page-number-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .pagination-btn:hover:not(:disabled),
[data-theme="dark"] .page-number-btn:hover {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

[data-theme="dark"] .enhanced-modal-footer {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .cancel-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .cancel-btn:hover {
  background-color: var(--danger-light);
  border-color: var(--danger);
  color: var(--danger);
}

[data-theme="dark"] .empty-state-icon {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .clear-search-btn {
  background-color: var(--text-secondary);
}

[data-theme="dark"] .clear-search-btn:hover {
  background-color: var(--danger);
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .enhanced-exercise-card,
  .enhanced-btn,
  .pagination-btn,
  .page-number-btn,
  .enhanced-search-input,
  .enhanced-select {
    transition: none;
  }

  .enhanced-exercise-card.animate-in {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .selection-checkmark {
    animation: none;
  }

  .spinner-ring {
    animation: none;
  }
}

/* Focus Styles for Accessibility */
.enhanced-search-input:focus,
.enhanced-select:focus,
.enhanced-btn:focus,
.pagination-btn:focus,
.page-number-btn:focus,
.enhanced-close-btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .enhanced-exercise-card {
    border-width: 3px;
  }

  .enhanced-exercise-card.selected {
    border-width: 4px;
  }

  .enhanced-badge {
    border: 2px solid currentColor;
  }
}

/* Print Styles */
@media print {
  .enhanced-modal-header,
  .enhanced-filters-section,
  .enhanced-pagination-section,
  .enhanced-modal-footer {
    display: none;
  }

  .enhanced-exercise-card {
    break-inside: avoid;
    border: 2px solid #000;
    margin-bottom: 1rem;
  }

  .enhanced-exercises-list {
    overflow: visible;
  }
}
