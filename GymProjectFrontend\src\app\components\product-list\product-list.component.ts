import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ProductService } from '../../services/product.service';
import { Product } from '../../models/product';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { ProductUpdateComponent } from '../crud/product-update/product-update.component';
import { faEdit, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import { DialogService } from '../../services/dialog.service';
import { PaginatedResult, ProductPagingParameters } from '../../models/pagination';

@Component({
    selector: 'app-product-list',
    templateUrl: './product-list.component.html',
    styleUrls: ['./product-list.component.css'],
    standalone: false
})
export class ProductListComponent implements OnInit {
  products: Product[] = [];
  productForm: FormGroup;
  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  isLoading: boolean = false;
  searchTerm: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 1;
  totalItems: number = 0;
  pageSizeOptions: number[] = [10, 20, 30];

  // Sorting properties
  sortField: string = 'ProductID';
  sortDirection: string = 'desc';

  // Pagination result
  paginatedResult: PaginatedResult<Product> = {
    data: [],
    pageNumber: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 1,
    hasPrevious: false,
    hasNext: false
  };

  constructor(
    private productService: ProductService,
    private toastrService: ToastrService,
    private dialog: MatDialog,
    private fb: FormBuilder,
    private dialogService: DialogService
  ) {}

  ngOnInit(): void {
    this.loadProductsPaginated();
    this.createProductForm();
  }

  createProductForm() {
    this.productForm = this.fb.group({
      name: ['', Validators.required],
      price: [0, [Validators.required, Validators.min(0)]],
    });
  }
  
  // Pagination methods
  loadProductsPaginated(): void {
    this.isLoading = true;

    const parameters: ProductPagingParameters = {
      pageNumber: this.currentPage,
      pageSize: this.itemsPerPage,
      searchText: this.searchTerm,
      sortBy: this.sortField,
      sortDirection: this.sortDirection
    };

    this.productService.getProductsPaginated(parameters).subscribe({
      next: (response) => {
        if (response.success) {
          this.paginatedResult = response.data;
          this.products = response.data.data;
          this.totalItems = response.data.totalCount;
          this.totalPages = response.data.totalPages;
          this.currentPage = response.data.pageNumber;
        } else {
          this.toastrService.error(response.message, 'Hata');
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Ürünler yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearch(): void {
    this.currentPage = 1;
    this.loadProductsPaginated();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.currentPage = 1;
    this.loadProductsPaginated();
  }

  sortProducts(field: string, direction: string): void {
    this.sortField = field;
    this.sortDirection = direction;
    this.currentPage = 1;
    this.loadProductsPaginated();
  }

  getSortText(): string {
    if (this.sortField === 'Name') {
      return this.sortDirection === 'asc' ? 'İsim (A-Z)' : 'İsim (Z-A)';
    } else if (this.sortField === 'Price') {
      return this.sortDirection === 'asc' ? 'Fiyat (Artan)' : 'Fiyat (Azalan)';
    } else if (this.sortField === 'CreationDate') {
      return this.sortDirection === 'asc' ? 'Tarih (Eski)' : 'Tarih (Yeni)';
    }
    return 'Sıralama';
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadProductsPaginated();
    }
  }

  changePageSize(size: number): void {
    this.itemsPerPage = size;
    this.currentPage = 1;
    this.loadProductsPaginated();
  }

  getPaginationRange(): number[] {
    const range = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }
  
  deleteProduct(product: Product) {
    this.dialogService.confirmProductDelete(product.name).subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.productService.deleteProduct(product.productID).subscribe(
          response => {
            this.toastrService.success('Ürün başarıyla silindi');
            // Silme işleminden sonra mevcut sayfada kal, sadece listeyi yenile
            this.loadProductsPaginated();
          },
          error => {
            this.toastrService.error('Ürün silinirken bir hata oluştu');
            this.isLoading = false;
          }
        );
      }
    });
  }

  addProduct() {
    if (this.productForm.valid) {
      this.isLoading = true;

      let productModel = Object.assign({}, this.productForm.value);
      this.productService.addProduct(productModel).subscribe(
        response => {
          this.toastrService.success(response.message, 'Başarılı');
          this.productForm.reset();
          // Yeni ürün eklendikten sonra ilk sayfaya git ve listeyi yenile
          this.currentPage = 1;
          this.loadProductsPaginated();
        },
        responseError => {
          this.toastrService.error(responseError.error.message, 'Hata');
          this.isLoading = false;
        }
      );
    }
  }

  editProduct(product: Product) {
    const dialogRef = this.dialog.open(ProductUpdateComponent, {
      width: '400px',
      data: product
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.productService.updateProduct(result).subscribe(
          response => {
            this.toastrService.success(response.message, 'Başarılı');
            // Güncelleme işleminden sonra mevcut sayfada kal, sadece listeyi yenile
            this.loadProductsPaginated();
          },
          responseError => {
            this.toastrService.error(responseError.error.message, 'Hata');
            this.isLoading = false;
          }
        );
      }
    });
  }
}
