import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { UserLicenseService } from '../../services/user-license.service';
import { DialogService } from '../../services/dialog.service';
import { UserLicenseDto } from '../../models/UserLicenseDto';
import { PaginatedUserLicenseDto } from '../../models/PaginatedUserLicenseDto';
import { ExtendLicenseComponent } from '../extend-license/extend-license.component';

@Component({
  selector: 'app-expired-licenses',
  templateUrl: './expired-licenses.component.html',
  styleUrls: ['./expired-licenses.component.css'],
  standalone: false
})
export class ExpiredLicensesComponent implements OnInit {
  userLicenses: UserLicenseDto[] = [];
  paginationData: PaginatedUserLicenseDto | null = null;
  isLoading = false;

  // Pagination
  currentPage = 1;
  pageSize = 20;

  // Search
  searchTerm = '';
  totalExpiredLicenses = 0;
  
  constructor(
    private userLicenseService: UserLicenseService,
    private dialogService: DialogService,
    private dialog: MatDialog,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadExpiredLicenses();
  }

  loadExpiredLicenses(): void {
    this.isLoading = true;

    this.userLicenseService.getExpiredAndPassive(
      this.currentPage,
      this.pageSize,
      this.searchTerm
    ).subscribe({
      next: (response) => {
        this.paginationData = response.data;
        this.userLicenses = response.data.data;
        this.totalExpiredLicenses = response.data.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisansı Dolan Üyeler yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.loadExpiredLicenses();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadExpiredLicenses();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.currentPage = 1;
    this.loadExpiredLicenses();
  }



  openExtendDialog(userLicense: UserLicenseDto): void {
    const dialogRef = this.dialog.open(ExtendLicenseComponent, {
      width: '400px',
      data: { userLicense }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadExpiredLicenses();
      }
    });
  }

  reactivateLicense(userLicenseId: number, userName: string): void {
    // Bu fonksiyon lisansı yeniden aktif hale getirir
    this.openExtendDialog(this.userLicenses.find(ul => ul.userLicenseID === userLicenseId)!);
  }

  getStatusBadgeClass(license: UserLicenseDto): string {
    if (!license.isActive) {
      return 'badge bg-secondary';
    } else if (license.remainingDays < 0) {
      return 'badge bg-danger';
    } else {
      return 'badge bg-warning text-dark';
    }
  }

  getModernStatusBadgeClass(license: UserLicenseDto): string {
    if (!license.isActive) {
      return 'modern-badge modern-badge-secondary';
    } else if (license.remainingDays < 0) {
      return 'modern-badge modern-badge-danger';
    } else {
      return 'modern-badge modern-badge-warning';
    }
  }

  getStatusText(license: UserLicenseDto): string {
    if (!license.isActive) {
      return 'Pasif';
    } else if (license.remainingDays < 0) {
      return `${Math.abs(license.remainingDays)} gün önce sona erdi`;
    } else {
      return `${license.remainingDays} gün kaldı`;
    }
  }

  getPageNumbers(): number[] {
    if (!this.paginationData) return [];
    
    const totalPages = this.paginationData.totalPages;
    const currentPage = this.paginationData.pageNumber;
    const pages: number[] = [];
    
    // Show max 5 page numbers
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    
    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  trackByLicenseId(index: number, item: UserLicenseDto): number {
    return item.userLicenseID;
  }
}
