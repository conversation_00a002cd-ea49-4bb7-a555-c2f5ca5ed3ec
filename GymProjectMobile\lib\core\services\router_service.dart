/// Router Service - GymKod Pro Mobile
///
/// Bu service Go Router ile navigation yönetimini yapar.
/// Referans: Angular frontend'deki routing system
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/presentation.dart';
import '../../features/auth/presentation/pages/change_password_page.dart';
import '../../features/member/presentation/pages/qr_code_page.dart';
import '../../features/member/presentation/pages/member_main_layout.dart';
import '../../features/member/presentation/pages/workout_program_page.dart';

import 'logging_service.dart';
import 'theme_service.dart';

/// App Routes - Angular frontend'deki route paths'e benzer
class AppRoutes {
  // Auth Routes (Angular: /auth/login, /auth/register)
  static const String splash = '/';
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String registerMember = '/auth/register-member';
  static const String changePassword = '/auth/change-password';

  // Member Routes (Angular: /my-qr)
  static const String myQr = '/my-qr';
  static const String memberMain = '/member-main';
  static const String workoutProgram = '/workout-program';

  // Main App Routes (gelecekte eklenecek)
  static const String home = '/home';
  static const String profile = '/profile';
  static const String settings = '/settings';



  // Error Routes
  static const String notFound = '/404';
}

/// Router Service
/// Angular frontend'deki router service'e benzer
class RouterService {
  static final RouterService _instance = RouterService._internal();
  factory RouterService() => _instance;
  RouterService._internal();

  /// Go Router instance'ını oluştur
  static GoRouter createRouter(Ref ref) {
    return GoRouter(
      initialLocation: AppRoutes.splash,
      debugLogDiagnostics: true,

      // Auth state değişikliklerini dinle
      refreshListenable: _AuthStateNotifier(ref),

      // Redirect Logic (Angular: CanActivate guard'a benzer) - Optimize edildi
      redirect: (context, state) {
        final authState = ref.read(authProvider);
        final currentPath = state.matchedLocation;

        // Debug mode'da detaylı log, production'da sadece önemli log'lar
        if (kDebugMode) {
          LoggingService.navigationLog('Redirect check', currentPath,
            details: 'Auth: ${authState.isAuthenticated}, Initialized: ${authState.isInitialized}, RequirePasswordChange: ${authState.requirePasswordChange}');
        }

        // Auth durumu henüz initialize olmadıysa splash'te kal
        if (!authState.isInitialized) {
          if (currentPath != AppRoutes.splash) {
            if (kDebugMode) {
              LoggingService.navigationLog('Redirect to splash', AppRoutes.splash,
                details: 'Auth not initialized');
            }
            return AppRoutes.splash;
          }
          return null;
        }

        // Şifre değiştirme zorunluluğu kontrolü (Angular frontend pattern)
        if (authState.isAuthenticated && authState.requirePasswordChange) {
          if (!currentPath.startsWith('/auth/change-password')) {
            LoggingService.navigationLog('Redirect to change password', '${AppRoutes.changePassword}?required=true',
              details: 'Password change required');
            return '${AppRoutes.changePassword}?required=true';
          }
          return null; // Zaten şifre değiştirme sayfasındaysa redirect yapma
        }

        // Authenticated user'lar auth sayfalarına gidemez (şifre değiştirme hariç)
        if (authState.isAuthenticated) {
          if (_isAuthRoute(currentPath) && !currentPath.startsWith('/auth/change-password')) {
            // Role-based redirect (Angular frontend pattern)
            final userRole = authState.user?.role.toLowerCase();
            final redirectRoute = _getDefaultRouteForRole(userRole);

            LoggingService.navigationLog('Redirect authenticated user', redirectRoute,
              details: 'User already authenticated, Role: $userRole');
            return redirectRoute;
          }
        } else {
          // Non-authenticated user'lar protected sayfalarına gidemez
          if (_isProtectedRoute(currentPath)) {
            LoggingService.navigationLog('Redirect to login', AppRoutes.login,
              details: 'User not authenticated');
            return AppRoutes.login;
          }
        }

        return null;
      },

      // Error Handler
      errorBuilder: (context, state) {
        LoggingService.navigationLog('Route error', state.matchedLocation,
          details: 'Error: ${state.error}');
        return _buildErrorPage(context, state.error.toString());
      },

      // Routes
      routes: [
        // Splash Route
        GoRoute(
          path: AppRoutes.splash,
          name: 'splash',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to splash', AppRoutes.splash);
            return const SplashPage();
          },
        ),

        // Auth Routes
        GoRoute(
          path: AppRoutes.login,
          name: 'login',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to login', AppRoutes.login);
            return const LoginPage();
          },
        ),

        GoRoute(
          path: AppRoutes.register,
          name: 'register',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to register', AppRoutes.register);
            return const RegisterPage();
          },
        ),

        GoRoute(
          path: AppRoutes.registerMember,
          name: 'register-member',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to register member', AppRoutes.registerMember);
            return const RegisterPage(isMemberRegistration: true);
          },
        ),

        GoRoute(
          path: AppRoutes.changePassword,
          name: 'change-password',
          builder: (context, state) {
            // Query parameter'dan zorunlu şifre değiştirme kontrolü
            final isRequired = state.uri.queryParameters['required'] == 'true';
            LoggingService.navigationLog('Navigate to change password', AppRoutes.changePassword,
              details: 'Required: $isRequired');
            return ChangePasswordPage(isRequired: isRequired);
          },
        ),

        // Home Route (geçici - gelecekte home page eklenecek)
        GoRoute(
          path: AppRoutes.home,
          name: 'home',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to home', AppRoutes.home);
            return _buildTempHomePage(context);
          },
        ),

        // Profile Route (geçici)
        GoRoute(
          path: AppRoutes.profile,
          name: 'profile',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to profile', AppRoutes.profile);
            return _buildTempProfilePage(context);
          },
        ),

        // Settings Route (geçici)
        GoRoute(
          path: AppRoutes.settings,
          name: 'settings',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to settings', AppRoutes.settings);
            return _buildTempSettingsPage(context);
          },
        ),

        // Member Main Layout Route (Bottom Navigation)
        GoRoute(
          path: AppRoutes.memberMain,
          name: 'member-main',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to member main', AppRoutes.memberMain);
            return const MemberMainLayout();
          },
        ),

        // My QR Route (Angular: /my-qr) - Standalone için korundu
        GoRoute(
          path: AppRoutes.myQr,
          name: 'my-qr',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to my QR', AppRoutes.myQr);
            return const QRCodePage();
          },
        ),

        // Workout Program Route - Standalone için
        GoRoute(
          path: AppRoutes.workoutProgram,
          name: 'workout-program',
          builder: (context, state) {
            LoggingService.navigationLog('Navigate to workout program', AppRoutes.workoutProgram);
            return const WorkoutProgramPage();
          },
        ),


      ],
    );
  }

  /// Auth route kontrolü
  static bool _isAuthRoute(String path) {
    return path.startsWith('/auth/') || path == AppRoutes.splash;
  }

  /// Protected route kontrolü
  static bool _isProtectedRoute(String path) {
    const protectedRoutes = [
      AppRoutes.home,
      AppRoutes.profile,
      AppRoutes.settings,
      AppRoutes.myQr,
      AppRoutes.memberMain,
      AppRoutes.workoutProgram,
    ];
    return protectedRoutes.contains(path);
  }

  /// Role'e göre varsayılan route'u döndür
  static String _getDefaultRouteForRole(String? role) {
    switch (role?.toLowerCase()) {
      case 'member':
        return AppRoutes.memberMain;
      case 'admin':
      case 'owner':
        return AppRoutes.home;
      default:
        return AppRoutes.home;
    }
  }

  /// Error page builder
  static Widget _buildErrorPage(BuildContext context, String error) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Hata'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Sayfa Bulunamadı',
              style: theme.textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.splash),
              child: const Text('Ana Sayfaya Dön'),
            ),
          ],
        ),
      ),
    );
  }

  /// Geçici Home Page
  static Widget _buildTempHomePage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ana Sayfa'),
        actions: [
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () => context.push(AppRoutes.profile),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.push(AppRoutes.settings),
          ),
        ],
      ),
      body: Consumer(
        builder: (context, ref, child) {
          final user = ref.watch(currentUserProvider);
          final theme = Theme.of(context);

          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.home,
                  size: 64,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'Hoş Geldiniz!',
                  style: theme.textTheme.headlineMedium,
                ),
                const SizedBox(height: 8),
                if (user != null) ...[
                  Text(
                    user.name,
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Chip(
                    label: Text(user.role),
                    backgroundColor: theme.colorScheme.primaryContainer,
                  ),
                ],
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () {
                    ref.read(authProvider.notifier).logout();
                    context.go(AppRoutes.login);
                  },
                  child: const Text('Çıkış Yap'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Geçici Profile Page
  static Widget _buildTempProfilePage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profil'),
      ),
      body: Consumer(
        builder: (context, ref, child) {
          final user = ref.watch(currentUserProvider);
          final theme = Theme.of(context);

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (user != null) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Kullanıcı Bilgileri',
                            style: theme.textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          _buildInfoRow('Ad', user.name),
                          _buildInfoRow('E-posta', user.email),
                          _buildInfoRow('Rol', user.role),
                          _buildInfoRow('Kullanıcı ID', user.userId.toString()),
                          if (user.hasValidCompany)
                            _buildInfoRow('Şirket ID', user.companyId.toString()),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  /// Geçici Settings Page
  static Widget _buildTempSettingsPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ayarlar'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            child: Column(
              children: [
                Consumer(
                  builder: (context, ref, child) {
                    final themeMode = ref.watch(themeProvider);
                    final themeService = ref.watch(themeServiceProvider);
                    final systemBrightness = MediaQuery.of(context).platformBrightness;

                    return ListTile(
                      leading: Icon(themeService.getThemeModeIcon(themeMode, systemBrightness)),
                      title: const Text('Tema'),
                      subtitle: Text(themeService.getThemeModeDescription(themeMode, systemBrightness)),
                      trailing: Switch(
                        value: themeService.isDarkMode(themeMode, systemBrightness),
                        onChanged: (value) async {
                          await ref.read(themeProvider.notifier).toggleTheme(systemBrightness);
                        },
                      ),
                    );
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.language),
                  title: const Text('Dil'),
                  subtitle: const Text('Türkçe'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // Language selection logic buraya eklenecek
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.lock_reset),
                  title: const Text('Şifre Değiştir'),
                  subtitle: const Text('Güvenliğiniz için şifrenizi değiştirin'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    context.push(AppRoutes.changePassword);
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.notifications),
                  title: const Text('Bildirimler'),
                  subtitle: const Text('Bildirim ayarları'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    // Notification settings logic buraya eklenecek
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  /// Info row builder helper
  static Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

/// Auth State Notifier for GoRouter
/// Auth state değişikliklerini GoRouter'a bildirir
class _AuthStateNotifier extends ChangeNotifier {
  final Ref _ref;
  late final ProviderSubscription _subscription;

  _AuthStateNotifier(this._ref) {
    // Auth state değişikliklerini dinle
    _subscription = _ref.listen<AuthState>(
      authProvider,
      (previous, next) {
        // Auth state değiştiğinde router'ı bilgilendir
        if (previous?.isAuthenticated != next.isAuthenticated ||
            previous?.isInitialized != next.isInitialized ||
            previous?.requirePasswordChange != next.requirePasswordChange) {
          LoggingService.navigationLog('Auth state changed - notifying router',
            'Auth: ${next.isAuthenticated}, Initialized: ${next.isInitialized}');
          notifyListeners();
        }
      },
    );
  }

  @override
  void dispose() {
    _subscription.close();
    super.dispose();
  }
}

/// Router Provider
final routerProvider = Provider<GoRouter>((ref) {
  return RouterService.createRouter(ref);
});
