import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import 'storage_service.dart';
import 'logging_service.dart';

/// Theme Service - GymKod Pro Mobile
///
/// Bu service Angular frontend'deki theme yönetiminden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/app/app.component.ts - toggleDarkMode()
/// Angular'daki localStorage theme yönetimi Flutter'a çevrilmiştir.

class ThemeService {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  final StorageService _storageService = StorageService();

  /// Tema modunu yükle
  /// Angular: loadThemePreference()
  Future<ThemeMode> loadThemeMode() async {
    try {
      final savedTheme = await _storageService.getThemeMode();

      LoggingService.info('Saved theme loaded: $savedTheme', tag: 'THEME_SERVICE');

      switch (savedTheme) {
        case AppConstants.lightTheme:
          return ThemeMode.light;
        case AppConstants.darkTheme:
          return ThemeMode.dark;
        case AppConstants.systemTheme:
          return ThemeMode.system;
        default:
          // Varsayılan olarak Dark tema (ilk giriş için)
          return ThemeMode.dark;
      }
    } catch (e, stackTrace) {
      LoggingService.error(
        'Theme loading error, defaulting to dark theme',
        tag: 'THEME_SERVICE',
        error: e,
        stackTrace: stackTrace,
      );
      return ThemeMode.dark;
    }
  }

  /// Tema modunu kaydet
  /// Angular: localStorage.setItem('darkMode', this.isDarkMode.toString())
  Future<void> saveThemeMode(ThemeMode themeMode) async {
    try {
      String themeValue;
      switch (themeMode) {
        case ThemeMode.light:
          themeValue = AppConstants.lightTheme;
          break;
        case ThemeMode.dark:
          themeValue = AppConstants.darkTheme;
          break;
        case ThemeMode.system:
          themeValue = AppConstants.systemTheme;
          break;
      }

      await _storageService.saveThemeMode(themeValue);
      LoggingService.info('Theme saved: $themeValue', tag: 'THEME_SERVICE');
    } catch (e, stackTrace) {
      LoggingService.error(
        'Theme saving error',
        tag: 'THEME_SERVICE',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Tema modunu değiştir (toggle)
  /// Angular: toggleDarkMode() - Sadece Light/Dark toggle
  ThemeMode toggleTheme(ThemeMode currentMode, Brightness systemBrightness) {
    // Mevcut tema dark mı kontrol et
    final isCurrentlyDark = isDarkMode(currentMode, systemBrightness);

    // Angular'daki gibi sadece Light/Dark toggle
    return isCurrentlyDark ? ThemeMode.light : ThemeMode.dark;
  }

  /// Mevcut tema modunun string açıklamasını al
  String getThemeModeDescription(ThemeMode themeMode, Brightness systemBrightness) {
    // Mevcut tema dark mı kontrol et
    final isCurrentlyDark = isDarkMode(themeMode, systemBrightness);
    return isCurrentlyDark ? 'Koyu Tema' : 'Açık Tema';
  }

  /// Mevcut tema modunun ikonunu al
  IconData getThemeModeIcon(ThemeMode themeMode, Brightness systemBrightness) {
    // Mevcut tema dark mı kontrol et
    final isCurrentlyDark = isDarkMode(themeMode, systemBrightness);
    return isCurrentlyDark ? Icons.dark_mode : Icons.light_mode;
  }

  /// Tema modunun aktif olarak dark olup olmadığını kontrol et
  bool isDarkMode(ThemeMode themeMode, Brightness systemBrightness) {
    switch (themeMode) {
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
      case ThemeMode.system:
        return systemBrightness == Brightness.dark;
    }
  }
}

/// Theme Provider - Riverpod State Management
/// Angular'daki component state yönetiminden uyarlanmıştır.

class ThemeNotifier extends StateNotifier<ThemeMode> {
  ThemeNotifier() : super(ThemeMode.dark) {
    _loadTheme();
  }

  final ThemeService _themeService = ThemeService();

  /// Tema modunu yükle
  Future<void> _loadTheme() async {
    final themeMode = await _themeService.loadThemeMode();
    state = themeMode;
  }

  /// Tema modunu değiştir
  Future<void> toggleTheme(Brightness systemBrightness) async {
    final newThemeMode = _themeService.toggleTheme(state, systemBrightness);
    state = newThemeMode;
    await _themeService.saveThemeMode(newThemeMode);

    LoggingService.info(
      'Theme toggled to: ${_themeService.getThemeModeDescription(newThemeMode, systemBrightness)}',
      tag: 'THEME_PROVIDER',
    );
  }

  /// Belirli bir tema modunu ayarla
  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = themeMode;
    await _themeService.saveThemeMode(themeMode);

    LoggingService.info(
      'Theme set to: ${_themeService.getThemeModeDescription(themeMode, Brightness.light)}',
      tag: 'THEME_PROVIDER',
    );
  }
}

/// Theme Provider Instance
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeMode>((ref) {
  return ThemeNotifier();
});

/// Theme Service Provider Instance
final themeServiceProvider = Provider<ThemeService>((ref) {
  return ThemeService();
});
