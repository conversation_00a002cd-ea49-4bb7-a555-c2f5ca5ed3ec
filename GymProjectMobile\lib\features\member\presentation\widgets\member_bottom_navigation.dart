/// Member Bottom Navigation - GymKod Pro Mobile
///
/// Bu widget member rol<PERSON><PERSON><PERSON> kullanıcılar için bottom navigation bar'ı sağlar.
/// Angular frontend'deki tasarım sistemini takip eder.
///
/// RESPONSIVE DESIGN:
/// - Responsive icon sizes ve spacing
/// - Responsive font sizes
/// - Responsive navigation bar height
/// - Responsive shadow ve elevation
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../providers/bottom_navigation_provider.dart';

/// Member Bottom Navigation Widget
/// Member rolündeki kullanıcılar için özelleştirilmiş bottom navigation
class MemberBottomNavigation extends ConsumerWidget {
  final Function(int) onTap;

  const MemberBottomNavigation({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final navigationState = ref.watch(bottomNavigationProvider);
    final width = MediaQuery.of(context).size.width;
    final deviceType = ScreenSize.getDeviceType(width);

    // Responsive values
    final responsiveElevation = AppSpacing.responsiveElevation(context);
    final responsiveIconSize = AppSpacing.responsiveIconSize(context,
      mobile: 22.0,
      tablet: 24.0,
      desktop: 26.0,
    );
    final responsiveSelectedFontSize = AppTypography.responsiveFontSize(context,
      mobile: 11.0,
      tablet: 12.0,
      desktop: 13.0,
    );
    final responsiveUnselectedFontSize = AppTypography.responsiveFontSize(context,
      mobile: 9.0,
      tablet: 10.0,
      desktop: 11.0,
    );

    return Container(
      decoration: BoxDecoration(
        color: theme.bottomNavigationBarTheme.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: deviceType == DeviceType.mobile ? 0.08 : 0.1),
            blurRadius: responsiveElevation * 2,
            offset: Offset(0, -responsiveElevation / 2),
          ),
        ],
      ),
      child: SafeArea(
        child: BottomNavigationBar(
          currentIndex: navigationState.currentIndex,
          onTap: (index) {
            // Auth durumunu kontrol et
            final authState = ref.read(authProvider);
            if (!authState.isAuthenticated) {
              LoggingService.authLog('Bottom navigation blocked - user not authenticated');
              return;
            }

            LoggingService.navigationLog(
              'Bottom navigation tap',
              navigationState.items[index].route,
              details: 'Tab index: $index, Device: ${deviceType.name}',
            );

            // Provider'ı güncelle
            ref.read(bottomNavigationProvider.notifier).changeTab(index);

            // Callback'i çağır
            onTap(index);
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: theme.bottomNavigationBarTheme.backgroundColor,
          selectedItemColor: theme.bottomNavigationBarTheme.selectedItemColor,
          unselectedItemColor: theme.bottomNavigationBarTheme.unselectedItemColor,
          elevation: 0, // Container'da shadow kullanıyoruz
          selectedFontSize: responsiveSelectedFontSize,
          unselectedFontSize: responsiveUnselectedFontSize,
          iconSize: responsiveIconSize,
          items: navigationState.items.map((item) => _buildNavigationItem(
            context,
            theme,
            item,
            deviceType,
          )).toList(),
        ),
      ),
    );
  }

  /// Navigation Item Builder (responsive)
  BottomNavigationBarItem _buildNavigationItem(
    BuildContext context,
    ThemeData theme,
    BottomNavigationItem item,
    DeviceType deviceType,
  ) {
    return BottomNavigationBarItem(
      icon: _buildIcon(context, theme, item, false, deviceType),
      activeIcon: _buildIcon(context, theme, item, true, deviceType),
      label: item.label,
      tooltip: item.label,
    );
  }

  /// Icon Builder (responsive)
  Widget _buildIcon(
    BuildContext context,
    ThemeData theme,
    BottomNavigationItem item,
    bool isActive,
    DeviceType deviceType,
  ) {
    IconData iconData;

    // Icon mapping
    switch (item.icon) {
      case 'qr_code':
        iconData = Icons.qr_code_2;
        break;
      case 'fitness_center':
        iconData = Icons.fitness_center;
        break;
      case 'person':
        iconData = Icons.person;
        break;
      default:
        iconData = Icons.help_outline;
    }

    // Responsive values
    final responsiveIconSize = AppSpacing.responsiveIconSize(context,
      mobile: 22.0,
      tablet: 24.0,
      desktop: 26.0,
    );

    final responsivePadding = AppSpacing.responsive(context,
      mobile: 3.0,
      tablet: 4.0,
      desktop: 5.0,
    );

    final responsiveHorizontalPadding = AppSpacing.responsive(context,
      mobile: 10.0,
      tablet: 12.0,
      desktop: 14.0,
    );

    final responsiveBorderRadius = AppSpacing.responsiveBorderRadius(context,
      mobile: 14.0,
      tablet: 16.0,
      desktop: 18.0,
    );

    return Container(
      padding: EdgeInsets.symmetric(
        vertical: responsivePadding,
        horizontal: responsiveHorizontalPadding,
      ),
      decoration: isActive ? BoxDecoration(
        color: theme.colorScheme.primary.withValues(
          alpha: deviceType == DeviceType.mobile ? 0.08 : 0.1,
        ),
        borderRadius: BorderRadius.circular(responsiveBorderRadius),
      ) : null,
      child: Icon(
        iconData,
        size: responsiveIconSize,
        color: isActive
          ? theme.bottomNavigationBarTheme.selectedItemColor
          : theme.bottomNavigationBarTheme.unselectedItemColor,
      ),
    );
  }
}
