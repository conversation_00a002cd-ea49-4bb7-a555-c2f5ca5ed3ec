﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;

namespace Business.Concrete
{
    public class UserOperationClaimManager : IUserOperationClaimService
    {
        private readonly IUserOperationClaimDal _userOperationClaimDal;
        private readonly IUserDeviceService _userDeviceService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserOperationClaimManager(
            IUserOperationClaimDal userOperationClaimDal,
            IUserDeviceService userDeviceService,
            IHttpContextAccessor httpContextAccessor)
        {
            _userOperationClaimDal = userOperationClaimDal;
            _userDeviceService = userDeviceService;
            _httpContextAccessor = httpContextAccessor;
        }

        [SecuredOperation("owner")]
        public IDataResult<List<UserOperationClaimDto>> GetUserOperationClaimDetails()
        {
            return new SuccessDataResult<List<UserOperationClaimDto>>(_userOperationClaimDal.GetUserOperationClaimDetails());
        }

        [SecuredOperation("owner")]
        [LogAspect]
        public IResult Add(UserOperationClaim userOperationClaim)
        {
            _userOperationClaimDal.Add(userOperationClaim);
            InvalidateUserClaims(userOperationClaim.UserId);
            return new SuccessResult(Messages.UserOperationClaimAdded);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        public IResult Delete(int id)
        {
            var claim = _userOperationClaimDal.Get(x => x.UserOperationClaimId == id);
            if (claim != null)
            {
                _userOperationClaimDal.HardDelete(id);
                InvalidateUserClaims(claim.UserId);
            }
            return new SuccessResult(Messages.UserOperationClaimDeleted);
        }

        [SecuredOperation("owner")]
        public IDataResult<List<UserOperationClaim>> GetAll()
        {
            return new SuccessDataResult<List<UserOperationClaim>>(_userOperationClaimDal.GetAll(u=>u.IsActive==true), Messages.UserOperationClaimsListed);
        }

        [SecuredOperation("owner")]
        public IDataResult<UserOperationClaim> GetById(int id)
        {
            return new SuccessDataResult<UserOperationClaim>(_userOperationClaimDal.Get(u => u.UserOperationClaimId == id));
        }

        [SecuredOperation("owner")]
        [LogAspect]
        public IResult Update(UserOperationClaim userOperationClaim)
        {
            _userOperationClaimDal.Update(userOperationClaim);
            InvalidateUserClaims(userOperationClaim.UserId);
            return new SuccessResult(Messages.UserOperationClaimUpdated);
        }

        public IResult InvalidateUserClaims(int userId)
        {
            var userDevices = _userDeviceService.GetActiveDevicesByUserId(userId);
            if (userDevices.Success)
            {
                foreach (var device in userDevices.Data)
                {
                    // Force token refresh by expiring the current refresh token
                    device.RefreshTokenExpiration = DateTime.Now;
                    _userDeviceService.Update(device);
                }
            }
            return new SuccessResult();
        }

        // Güvenlik kontrolü olmadan üye kaydı için özel metot
        [LogAspect]
        public IResult AddForRegistration(UserOperationClaim userOperationClaim)
        {
            // IP adresi ve zaman damgası gibi bilgileri loglama
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
            var timestamp = DateTime.Now;

            // Log mesajı
            Console.WriteLine($"[{timestamp}] AddForRegistration called: UserId={userOperationClaim.UserId}, RoleId={userOperationClaim.OperationClaimId}, IP: {ipAddress}");

            _userOperationClaimDal.Add(userOperationClaim);
            return new SuccessResult(Messages.UserOperationClaimAdded);
        }
    }
}
