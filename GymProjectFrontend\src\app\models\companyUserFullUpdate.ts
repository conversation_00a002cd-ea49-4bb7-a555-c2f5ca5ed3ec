export interface CompanyUserFullUpdate {
  // CompanyUser güncellemeleri
  companyUserID: number;
  name: string;
  phoneNumber: string;
  email: string;
  cityID: number;
  townID: number;
  isActive?: boolean;
  
  // Company güncellemeleri (opsiyonel)
  companyID?: number;
  companyName: string;
  companyPhone: string;
  companyAddress: string;
  companyCityID?: number;
  companyTownID?: number;
  companyIsActive?: boolean;
  
  // Kontrol alanları
  emailChanged: boolean;
  nameChanged: boolean;
  companyDataChanged: boolean;
  
  // <PERSON><PERSON> (değişiklik kontrolü için)
  oldEmail: string;
  oldName: string;
}
