<div class="register-container">
  <div class="register-wrapper">
    <!-- Left Panel with Gym Image -->
    <div class="register-image-panel">
      <div class="overlay">
        <div class="gym-branding">
          <div class="logo-container">
            <i class="fas fa-dumbbell"></i>
          </div>
          <h1>GymKod Pro</h1>
          <p>Admin <PERSON> Sistemi</p>
          <div class="features">
            <div class="feature">
              <i class="fas fa-shield-alt"></i>
              <span>Admin <PERSON></span>
            </div>
            <div class="feature">
              <i class="fas fa-cogs"></i>
              <span>Sistem Yönetimi</span>
            </div>
            <div class="feature">
              <i class="fas fa-users-cog"></i>
              <span>Kullanıcı Yönetimi</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel with Register Form -->
    <div class="register-form-panel">
      <div class="register-form-container">
        <div class="register-header">
          <h2>Admin Hesabı Oluştur</h2>
          <p>Sistem yöneticisi olarak kayıt olun</p>
        </div>

        <!-- Ban Warning -->
        <div class="ban-warning" *ngIf="isBanned">
          <i class="fas fa-exclamation-triangle"></i>
          <span>Çok fazla başarısız deneme! {{ remainingBanTime }} sonra tekrar deneyin.</span>
        </div>

        <form [formGroup]="registerForm" (ngSubmit)="register()" class="register-form">
          <div class="form-row">
            <div class="form-group">
              <label for="firstName">Ad</label>
              <div class="input-group">
                <i class="fas fa-user"></i>
                <input
                  id="firstName"
                  type="text"
                  formControlName="firstName"
                  placeholder="Adınız"
                  [ngClass]="{'is-invalid': registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched}"
                >
              </div>
              <div class="error-message" *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched">
                <small *ngIf="registerForm.get('firstName')?.errors?.['required']">Ad alanı gerekli</small>
                <small *ngIf="registerForm.get('firstName')?.errors?.['minlength']">Ad en az 2 karakter olmalıdır</small>
              </div>
            </div>

            <div class="form-group">
              <label for="lastName">Soyad</label>
              <div class="input-group">
                <i class="fas fa-user"></i>
                <input
                  id="lastName"
                  type="text"
                  formControlName="lastName"
                  placeholder="Soyadınız"
                  [ngClass]="{'is-invalid': registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched}"
                >
              </div>
              <div class="error-message" *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched">
                <small *ngIf="registerForm.get('lastName')?.errors?.['required']">Soyad alanı gerekli</small>
                <small *ngIf="registerForm.get('lastName')?.errors?.['minlength']">Soyad en az 2 karakter olmalıdır</small>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="email">E-posta</label>
            <div class="input-group">
              <i class="fas fa-envelope"></i>
              <input
                id="email"
                type="email"
                formControlName="email"
                placeholder="<EMAIL>"
                [ngClass]="{'is-invalid': registerForm.get('email')?.invalid && registerForm.get('email')?.touched}"
              >
            </div>
            <div class="error-message" *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
              <small *ngIf="registerForm.get('email')?.errors?.['required']">E-posta alanı gerekli</small>
              <small *ngIf="registerForm.get('email')?.errors?.['email']">Geçerli bir e-posta adresi girin</small>
            </div>
          </div>

          <div class="form-group">
            <label for="password">Şifre</label>
            <div class="input-group">
              <i class="fas fa-lock"></i>
              <input
                id="password"
                [type]="passwordVisible ? 'text' : 'password'"
                formControlName="password"
                placeholder="En az 6 karakter"
                [ngClass]="{'is-invalid': registerForm.get('password')?.invalid && registerForm.get('password')?.touched}"
              >
              <button type="button" class="password-toggle" (click)="togglePasswordVisibility()">
                <i [class]="passwordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div class="error-message" *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
              <small *ngIf="registerForm.get('password')?.errors?.['required']">Şifre alanı gerekli</small>
              <small *ngIf="registerForm.get('password')?.errors?.['minlength']">Şifre en az 6 karakter olmalıdır</small>
            </div>
          </div>

          <div class="form-group">
            <label for="confirmPassword">Şifre Tekrarı</label>
            <div class="input-group">
              <i class="fas fa-lock"></i>
              <input
                id="confirmPassword"
                [type]="confirmPasswordVisible ? 'text' : 'password'"
                formControlName="confirmPassword"
                placeholder="Şifrenizi tekrar girin"
                [ngClass]="{'is-invalid': (registerForm.get('confirmPassword')?.invalid || registerForm.hasError('mismatch')) && registerForm.get('confirmPassword')?.touched}"
              >
              <button type="button" class="password-toggle" (click)="toggleConfirmPasswordVisibility()">
                <i [class]="confirmPasswordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div class="error-message" *ngIf="(registerForm.get('confirmPassword')?.invalid || registerForm.hasError('mismatch')) && registerForm.get('confirmPassword')?.touched">
              <small *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">Şifre tekrarı gerekli</small>
              <small *ngIf="registerForm.hasError('mismatch')">Şifreler eşleşmiyor</small>
            </div>
          </div>

          <div class="form-actions">
            <a routerLink="/login" class="login-link">Zaten hesabınız var mı? Giriş yapın</a>
          </div>

          <button
            type="submit"
            [disabled]="registerForm.invalid || isLoading"
            class="register-button"
          >
            <span *ngIf="!isLoading">Admin Kaydı Oluştur</span>
            <app-loading-spinner *ngIf="isLoading" [size]="'small'" [showText]="false"></app-loading-spinner>
          </button>
        </form>

        <div class="register-footer">
          <p class="info-text">
            <i class="fas fa-info-circle"></i>
            Bu kayıt formu sadece sistem yöneticileri içindir. Kayıt olduktan sonra rol ataması için sistem yöneticisi ile iletişime geçin.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
