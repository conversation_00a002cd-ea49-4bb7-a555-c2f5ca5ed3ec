<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isSubmitting">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="modern-card slide-in-right">
        <div class="modern-card-header">
          <h5 class="mb-0">Yeni Lisans Paketi</h5>
        </div>
        
        <div class="modern-card-body">
          <form [formGroup]="licensePackageAddForm">
            <div class="form-section">
              <h6 class="section-title">Paket Bilgileri</h6>
              
              <div class="modern-form-group">
                <label for="name" class="modern-form-label">Paket Adı</label>
                <input
                  type="text"
                  id="name"
                  formControlName="name"
                  class="modern-form-control"
                  placeholder="Örn: <PERSON><PERSON>, Premium Lisans"
                />
                <small class="text-danger" *ngIf="licensePackageAddForm.get('name')?.invalid && licensePackageAddForm.get('name')?.touched">
                  Paket adı zorunludur
                </small>
              </div>

              <div class="modern-form-group">
                <label for="description" class="modern-form-label">Açıklama</label>
                <textarea
                  id="description"
                  formControlName="description"
                  class="modern-form-control"
                  rows="3"
                  placeholder="Paket açıklaması..."
                ></textarea>
                <small class="text-danger" *ngIf="licensePackageAddForm.get('description')?.invalid && licensePackageAddForm.get('description')?.touched">
                  Açıklama zorunludur
                </small>
              </div>

              <div class="modern-form-group">
                <label for="role" class="modern-form-label">Rol</label>
                <select id="role" formControlName="role" class="modern-form-control">
                  <option value="" disabled>Rol Seçiniz</option>
                  <option *ngFor="let role of roles" [value]="role.name">{{ role.name | titlecase }}</option>
                </select>
                <small class="text-danger" *ngIf="licensePackageAddForm.get('role')?.invalid && licensePackageAddForm.get('role')?.touched">
                  Rol seçimi zorunludur
                </small>
              </div>
            </div>
            
            <div class="form-section">
              <h6 class="section-title">Süre ve Fiyat</h6>
              
              <div class="modern-form-group">
                <label class="modern-form-label">Geçerlilik Süresi</label>
                <div class="d-flex validity-selects">
                  <select id="year" formControlName="year" class="modern-form-control me-2">
                    <option *ngFor="let i of [].constructor(11); let year = index" [value]="year">
                      {{ year }} Yıl
                    </option>
                  </select>
                  
                  <select id="month" formControlName="month" class="modern-form-control me-2">
                    <option *ngFor="let i of [].constructor(13); let month = index" [value]="month">
                      {{ month }} Ay
                    </option>
                  </select>
                  
                  <select id="day" formControlName="day" class="modern-form-control">
                    <option *ngFor="let i of [].constructor(32); let day = index" [value]="day">
                      {{ day }} Gün
                    </option>
                  </select>
                </div>
                <small class="text-danger" *ngIf="(licensePackageAddForm.get('year')?.value === '0' && licensePackageAddForm.get('month')?.value === '0' && licensePackageAddForm.get('day')?.value === '0') && (licensePackageAddForm.get('year')?.touched || licensePackageAddForm.get('month')?.touched || licensePackageAddForm.get('day')?.touched)">
                  Geçerlilik süresi en az 1 gün olmalıdır
                </small>
              </div>

              <div class="modern-form-group">
                <label for="price" class="modern-form-label">Ücret</label>
                <div class="input-group">
                  <span class="input-group-text">₺</span>
                  <input
                    type="number"
                    id="price"
                    formControlName="price"
                    class="modern-form-control"
                    min="0"
                    step="0.01"
                    placeholder="Ücret"
                  />
                </div>
                <small class="text-danger" *ngIf="licensePackageAddForm.get('price')?.invalid && licensePackageAddForm.get('price')?.touched">
                  Ücret bilgisi zorunludur ve 0'dan büyük olmalıdır
                </small>
              </div>


            </div>
          </form>
        </div>
        
        <div class="modern-card-footer">
          <button 
            class="modern-btn modern-btn-primary w-100" 
            (click)="add()" 
            [disabled]="!licensePackageAddForm.valid || isSubmitting"
          >
            {{ isSubmitting ? 'Paket Kaydediliyor...' : 'Paketi Kaydet' }}
          </button>
        </div>
      </div>
    </div>

    <div class="col-md-8">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">Lisans Paketleri</h5>
        </div>
        
        <div class="modern-card-body">
          <app-license-packages-list #licensePackageList></app-license-packages-list>
        </div>
      </div>
    </div>
  </div>
</div>
