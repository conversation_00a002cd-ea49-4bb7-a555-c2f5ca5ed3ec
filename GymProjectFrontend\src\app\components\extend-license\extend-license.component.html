<div class="modern-card extend-license-dialog">
  <div class="modern-card-header">
    <h3 class="text-primary mb-0">
      <i class="fas fa-calendar-plus me-2"></i>Lisa<PERSON>
    </h3>
    <button class="modern-btn modern-btn-sm modern-btn-secondary" type="button" (click)="onCancel()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <form [formGroup]="extendForm" (ngSubmit)="onSubmit()">
    <div class="modern-card-body">
      <!-- Simplified User Info -->
      <div class="user-info-simple mb-4">
        <div class="d-flex align-items-center gap-3">
          <div class="modern-avatar" style="background-color: var(--primary);">
            {{ getUserInitials() }}
          </div>
          <div>
            <h5 class="mb-1">{{ data.userLicense.userName }}</h5>
            <p class="text-muted mb-1">{{ data.userLicense.companyName }}</p>
            <div class="d-flex align-items-center gap-3">
              <small class="text-muted">
                <i class="fas fa-calendar-times me-1"></i>
                Bitiş: {{ data.userLicense.endDate | date:'dd/MM/yyyy' }}
              </small>
              <span class="modern-badge" [ngClass]="getRemainingDaysClass()">
                {{ data.userLicense.remainingDays }} gün kaldı
              </span>
            </div>
          </div>
        </div>
      </div>



      <!-- Package Selection -->
      <div class="form-section mb-4">
        <div class="modern-form-group">
          <label class="modern-label">Lisans Paketi</label>
          <div class="select-wrapper">
            <i class="fas fa-box input-icon"></i>
            <select class="modern-select" formControlName="licensePackageID">
              <option value="">Lisans paketi seçin...</option>
              <option *ngFor="let pkg of licensePackages" [value]="pkg.licensePackageID">
                {{ pkg.name }} - {{ pkg.role }} ({{ pkg.durationDays }} gün) - {{ pkg.price | currency:'TRY':'symbol':'1.0-0' }}
              </option>
            </select>
          </div>
          <div class="package-info" *ngIf="getSelectedPackage()">
            <div class="info-card">
              <div class="info-header">
                <h6 class="mb-1">{{ getSelectedPackage()?.name }}</h6>
                <span class="price-badge">{{ getSelectedPackage()?.price | currency:'TRY':'symbol':'1.0-0' }}</span>
              </div>
              <div class="info-details">
                <div class="detail-item">
                  <i class="fas fa-clock text-primary"></i>
                  <span>{{ getSelectedPackage()?.durationDays }} gün</span>
                </div>
                <div class="detail-item" *ngIf="getSelectedPackage()?.role">
                  <i class="fas fa-user-tag text-secondary"></i>
                  <span>{{ getSelectedPackage()?.role }}</span>
                </div>
              </div>
              <p class="description" *ngIf="getSelectedPackage()?.description">
                {{ getSelectedPackage()?.description }}
              </p>
            </div>
          </div>
        </div>
      </div>



      <!-- Payment Method Selection -->
      <div class="form-section mb-4">
        <div class="modern-form-group">
          <label class="modern-label">
            <i class="fas fa-credit-card me-1"></i>Ödeme Yöntemi
          </label>
          <div class="payment-methods">
            <div
              *ngFor="let method of paymentMethods"
              class="payment-method-option"
              [class.selected]="extendForm.get('paymentMethod')?.value === method.value"
              (click)="selectPaymentMethod(method.value)"
            >
              <div class="payment-method-icon">
                <i class="fas" [ngClass]="method.icon"></i>
              </div>
              <div class="payment-method-label">{{method.label}}</div>
            </div>
          </div>
          <div *ngIf="extendForm.get('paymentMethod')?.invalid && extendForm.get('paymentMethod')?.touched"
               class="form-error">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Ödeme yöntemi seçilmelidir
          </div>
        </div>
      </div>
    </div>

    <div class="modern-card-footer">
      <button type="button"
              class="modern-btn modern-btn-secondary"
              (click)="onCancel()"
              [disabled]="isSubmitting">
        <i class="fas fa-times me-2"></i>İptal
      </button>
      <button type="submit"
              class="modern-btn modern-btn-primary"
              [disabled]="extendForm.invalid || isSubmitting">
        <span *ngIf="!isSubmitting">
          <i class="fas fa-check me-2"></i>
          Lisansı Uzat
        </span>
        <span *ngIf="isSubmitting">
          <div class="spinner-border spinner-border-sm me-2" role="status"></div>
          Uzatılıyor...
        </span>
      </button>
    </div>
  </form>
</div>