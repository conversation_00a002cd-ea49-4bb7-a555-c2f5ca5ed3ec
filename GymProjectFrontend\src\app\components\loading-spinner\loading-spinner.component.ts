import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-loading-spinner',
  template: `
    <div class="spinner-container" [ngClass]="{'overlay': overlay, 'sidebar-aware': sidebarAware}">
      <div class="spinner-content" [ngClass]="{'with-text': showText}">
        <div class="gym-spinner" [ngClass]="size">
          <div class="spinner-circle"></div>
          <div class="dumbbell">
            <div class="weight left">
              <div class="inner-weight"></div>
            </div>
            <div class="handle"></div>
            <div class="weight right">
              <div class="inner-weight"></div>
            </div>
          </div>
        </div>
        <div class="spinner-text" *ngIf="showText">
          <span class="text">{{ text }}</span>
          <span class="dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }

    .spinner-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }

    .spinner-container.overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(var(--bg-primary-rgb), 0.85);
      z-index: 999; /* Sidebar'dan düşük z-index */
      backdrop-filter: blur(4px);
      transition: all 0.3s ease;
    }

    /* Sidebar-aware overlay - sidebar'ın sağından başlar */
    .spinner-container.overlay.sidebar-aware {
      left: 280px; /* Normal sidebar genişliği */
      width: calc(100% - 280px);
    }

    /* Sidebar collapsed durumunda */
    :host-context(.app-container.sidebar-collapsed) .spinner-container.overlay.sidebar-aware {
      left: 80px; /* Collapsed sidebar genişliği */
      width: calc(100% - 80px);
    }

    /* Mobile responsive - sidebar overlay olduğu için full width */
    @media (max-width: 991.98px) {
      .spinner-container.overlay.sidebar-aware {
        left: 0;
        width: 100%;
      }

      :host-context(.app-container.sidebar-collapsed) .spinner-container.overlay.sidebar-aware {
        left: 0;
        width: 100%;
      }
    }

    .spinner-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20px;
    }

    .spinner-content.with-text {
      padding: 30px;
      border-radius: 16px;
      background-color: rgba(var(--bg-secondary-rgb), 0.95);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(10px);
      border: 1px solid var(--border-color);
      color: var(--text-primary);
    }

    .gym-spinner {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .gym-spinner.small {
      width: 60px;
      height: 40px;
    }

    .gym-spinner.medium {
      width: 90px;
      height: 60px;
    }

    .gym-spinner.large {
      width: 120px;
      height: 80px;
    }

    .spinner-circle {
      position: absolute;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: var(--primary-color, #4361ee);
      border-bottom-color: var(--primary-color, #4361ee);
      animation: spin 1.5s linear infinite;
    }

    .small .spinner-circle {
      width: 40px;
      height: 40px;
      border-width: 2px;
    }

    .medium .spinner-circle {
      width: 60px;
      height: 60px;
      border-width: 3px;
    }

    .large .spinner-circle {
      width: 80px;
      height: 80px;
      border-width: 4px;
    }

    .dumbbell {
      display: flex;
      align-items: center;
      justify-content: center;
      animation: lift 2s ease-in-out infinite;
    }

    .weight {
      background: linear-gradient(135deg, var(--primary-color, #4361ee) 0%, var(--secondary-color, #3f37c9) 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);
    }

    .inner-weight {
      width: 50%;
      height: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
    }

    .weight.left {
      animation: pulse-left 2s ease-in-out infinite;
    }

    .weight.right {
      animation: pulse-right 2s ease-in-out infinite;
    }

    .small .weight {
      width: 16px;
      height: 16px;
    }

    .medium .weight {
      width: 22px;
      height: 22px;
    }

    .large .weight {
      width: 28px;
      height: 28px;
    }

    .handle {
      height: 8px;
      background: linear-gradient(90deg, var(--primary-color, #4361ee) 0%, var(--secondary-color, #3f37c9) 100%);
      border-radius: 4px;
      box-shadow: 0 0 10px rgba(67, 97, 238, 0.5);
    }

    .small .handle {
      width: 30px;
      height: 5px;
    }

    .medium .handle {
      width: 40px;
      height: 6px;
    }

    .large .handle {
      width: 50px;
      height: 8px;
    }

    .spinner-text {
      color: var(--text-primary);
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .dots {
      display: flex;
      gap: 4px;
    }

    .dot {
      width: 4px;
      height: 4px;
      background-color: var(--text-primary);
      border-radius: 50%;
      display: inline-block;
    }

    .dot:nth-child(1) {
      animation: dot-fade 1.5s ease-in-out infinite;
    }

    .dot:nth-child(2) {
      animation: dot-fade 1.5s ease-in-out 0.5s infinite;
    }

    .dot:nth-child(3) {
      animation: dot-fade 1.5s ease-in-out 1s infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes lift {
      0%, 100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-5px);
      }
    }

    @keyframes pulse-left {
      0%, 100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
    }

    @keyframes pulse-right {
      0%, 100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
    }

    @keyframes dot-fade {
      0%, 100% {
        opacity: 0.3;
      }
      50% {
        opacity: 1;
      }
    }
  `],
  standalone: false
})
export class LoadingSpinnerComponent {
  @Input() showText: boolean = true;
  @Input() text: string = '';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() overlay: boolean = false;
  @Input() sidebarAware: boolean = true; // Default true for modern UX
}
