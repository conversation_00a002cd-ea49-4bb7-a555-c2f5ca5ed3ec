﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class TransactionManager : ITransactionService
    {
        private readonly ITransactionDal _transactionDal;
        private readonly IMemberDal _memberDal;

        public TransactionManager(ITransactionDal transactionDal, IMemberDal memberDal)
        {
            _transactionDal = transactionDal;
            _memberDal = memberDal;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult AddBulk(BulkTransactionDto bulkTransaction)
        {
            var member = _memberDal.Get(m => m.MemberID == bulkTransaction.MemberID);
            if (member == null)
                return new ErrorResult(Messages.MemberNotFound);

            decimal totalAmount = bulkTransaction.Items.Sum(item => item.Quantity * item.UnitPrice);
            decimal availableBalance = Math.Max(0, member.Balance);
            decimal amountToDeduct = Math.Min(totalAmount, availableBalance);
            decimal remainingDebt = totalAmount - amountToDeduct;

            member.Balance -= totalAmount;
            member.UpdatedDate = DateTime.Now;
            _memberDal.Update(member);

            foreach (var item in bulkTransaction.Items)
            {
                decimal itemTotalCost = item.Quantity * item.UnitPrice;
                decimal itemAvailableBalance = (availableBalance * itemTotalCost) / totalAmount;
                decimal itemDebt = itemTotalCost - itemAvailableBalance;

                var transaction = new Transaction
                {
                    MemberID = bulkTransaction.MemberID,
                    ProductID = item.ProductID,
                    Amount = itemDebt,
                    UnitPrice = item.UnitPrice,
                    Quantity = item.Quantity,
                    TransactionType = bulkTransaction.TransactionType,
                    TransactionDate = DateTime.Now,
                    IsPaid = itemDebt <= 0
                };
                _transactionDal.Add(transaction);
            }

            return new SuccessResult(Messages.TransactionAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult Add(Transaction transaction)
        {
            var member = _memberDal.Get(m => m.MemberID == transaction.MemberID);
            if (member == null)
                return new ErrorResult(Messages.MemberNotFound);

            transaction.TransactionDate = DateTime.Now;
            if (transaction.TransactionType == "Bakiye Yükleme")
            {
                member.Balance += transaction.Amount;
                transaction.IsPaid = true;
                transaction.ProductID = null;
            }
            else
            {
                decimal totalCost = transaction.UnitPrice * transaction.Quantity;
                decimal availableBalance = Math.Max(0, member.Balance);
                decimal amountToDeduct = Math.Min(totalCost, availableBalance);
                decimal remainingDebt = totalCost - amountToDeduct;

                member.Balance -= totalCost; // Toplam tutarı bakiyeden düş
                transaction.Amount = remainingDebt; // Sadece kalan borç miktarını Amount'a kaydet
                transaction.IsPaid = remainingDebt <= 0;
            }

            member.UpdatedDate = DateTime.Now;
            _memberDal.Update(member);
            _transactionDal.Add(transaction);
            return new SuccessResult(Messages.TransactionAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult UpdatePaymentStatus(int transactionId)
        {
            var transaction = _transactionDal.Get(t => t.TransactionID == transactionId);
            if (transaction == null)
                return new ErrorResult(Messages.TransactionNotFound);

            var member = _memberDal.Get(m => m.MemberID == transaction.MemberID);
            if (member == null)
                return new ErrorResult(Messages.MemberNotFound);

            if (transaction.IsPaid)
                return new SuccessResult();

            if (!transaction.IsPaid && transaction.TransactionType != "Bakiye Yükleme")
            {
                member.Balance += transaction.Amount;
                member.UpdatedDate = DateTime.Now;
                _memberDal.Update(member);
            }

            transaction.IsPaid = true;
            _transactionDal.Update(transaction);
            return new SuccessResult(Messages.TransactionUpdated);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult UpdateAllPaymentStatus(int memberId)
        {
            var transactions = _transactionDal.GetAll(t => t.MemberID == memberId && !t.IsPaid && t.TransactionType != "Bakiye Yükleme");
            if (!transactions.Any())
                return new ErrorResult("Ödenecek işlem bulunamadı.");

            var member = _memberDal.Get(m => m.MemberID == memberId);
            if (member == null)
                return new ErrorResult(Messages.MemberNotFound);

            decimal totalDebt = transactions.Sum(t => t.Amount);

            member.Balance = 0;
            member.UpdatedDate = DateTime.Now;
            _memberDal.Update(member);

            foreach (var transaction in transactions)
            {
                transaction.IsPaid = true;
                _transactionDal.Update(transaction);
            }

            return new SuccessResult("Tüm ödemeler başarıyla yapıldı.");
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "All")]
        public IDataResult<List<Transaction>> GetAll()
        {
            return new SuccessDataResult<List<Transaction>>(_transactionDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "ByMember")]
        public IDataResult<List<Transaction>> GetByMemberId(int memberId)
        {
            return new SuccessDataResult<List<Transaction>>(_transactionDal.GetAll(t => t.MemberID == memberId));
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "WithDetails")]
        public IDataResult<List<TransactionDetailDto>> GetTransactionsWithDetails()
        {
            return new SuccessDataResult<List<TransactionDetailDto>>(_transactionDal.GetTransactionsWithDetails());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "Unpaid")]
        public IDataResult<List<TransactionDetailDto>> GetUnpaidTransactions(int memberId)
        {
            var transactions = _transactionDal.GetTransactionsWithDetails();
            return new SuccessDataResult<List<TransactionDetailDto>>(
                transactions.Where(t => t.MemberID == memberId && !t.IsPaid).ToList()
            );
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult Delete(int transactionId)
        {
            var transaction = _transactionDal.Get(t => t.TransactionID == transactionId);
            if (transaction == null)
                return new ErrorResult(Messages.TransactionNotFound);

            // Sadece ödenmiş işlemler silinebilir
            if (!transaction.IsPaid)
                return new ErrorResult("Sadece ödenmiş işlemler silinebilir.");

            // Soft delete
            transaction.IsActive = false;
            transaction.DeletedDate = DateTime.Now;
            transaction.UpdatedDate = DateTime.Now;

            _transactionDal.Update(transaction);
            return new SuccessResult("İşlem başarıyla silindi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Transaction", "Monthly")]
        public IDataResult<decimal> GetMonthlyTransactionTotal(int year, int month)
        {
            var monthStart = new DateTime(year, month, 1);
            var monthEnd = monthStart.AddMonths(1).AddTicks(-1);

            var allTransactions = _transactionDal.GetTransactionsWithDetails();

            var monthlyTransactions = allTransactions
                .Where(t => t.TransactionDate >= monthStart &&
                           t.TransactionDate <= monthEnd &&
                           t.TransactionType != "Bakiye Yükleme")
                .ToList();

            var totalAmount = monthlyTransactions.Sum(t => t.TotalPrice);

            return new SuccessDataResult<decimal>(totalAmount, "Aylık işlem toplamı başarıyla getirildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Transaction", "Daily")]
        public IDataResult<decimal> GetDailyTransactionTotal(DateTime date)
        {
            var dayStart = date.Date;
            var dayEnd = dayStart.AddDays(1).AddTicks(-1);

            var allTransactions = _transactionDal.GetTransactionsWithDetails();

            var dailyTransactions = allTransactions
                .Where(t => t.TransactionDate >= dayStart &&
                           t.TransactionDate <= dayEnd &&
                           t.TransactionType != "Bakiye Yükleme")
                .ToList();

            var totalAmount = dailyTransactions.Sum(t => t.TotalPrice);

            return new SuccessDataResult<decimal>(totalAmount, "Günlük işlem toplamı başarıyla getirildi.");
        }
    }
}