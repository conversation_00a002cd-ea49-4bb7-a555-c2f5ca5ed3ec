﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace Business.Concrete
{
    public class RemainingDebtManager : IRemainingDebtService
    {
        private readonly IRemainingDebtDal _remainingDebtDal;
        private readonly IDebtPaymentDal _debtPaymentDal;
        private readonly IPaymentDal _paymentDal;


        public RemainingDebtManager(IRemainingDebtDal remainingDebtDal, IDebtPaymentDal debtPaymentDal, IPaymentDal paymentDal)
        {
            _remainingDebtDal = remainingDebtDal;
            _debtPaymentDal = debtPaymentDal;
            _paymentDal = paymentDal;
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<RemainingDebtDetailDto>> GetRemainingDebtDetails()
        {
            return new SuccessDataResult<List<RemainingDebtDetailDto>>(_remainingDebtDal.GetRemainingDebtDetails());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult AddDebtPayment(DebtPaymentDto debtPaymentDto)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var remainingDebt = _remainingDebtDal.Get(rd => rd.RemainingDebtID == debtPaymentDto.RemainingDebtID);
                    if (remainingDebt == null)
                        return new ErrorResult(Messages.PaymentNotFound);

                    if (debtPaymentDto.PaidAmount > remainingDebt.RemainingAmount)
                        return new ErrorResult(Messages.InvalidPaymentAmount);

                    // Borç ödemesini kaydet
                    var debtPayment = new DebtPayment
                    {
                        RemainingDebtID = debtPaymentDto.RemainingDebtID,
                        PaidAmount = debtPaymentDto.PaidAmount,
                        PaymentMethod = debtPaymentDto.PaymentMethod,
                        PaymentDate = DateTime.Now,
                        IsActive = true
                    };
                    _debtPaymentDal.Add(debtPayment);

                    // Kalan borç tutarını güncelle
                    decimal newRemainingAmount = remainingDebt.RemainingAmount - debtPaymentDto.PaidAmount;
                    remainingDebt.RemainingAmount = newRemainingAmount;
                    remainingDebt.LastUpdateDate = DateTime.Now;
                    _remainingDebtDal.Update(remainingDebt);

                    // Eğer borç tamamen ödendiyse Payment kaydını güncelle
                    if (newRemainingAmount == 0)
                    {
                        var payment = _paymentDal.Get(p => p.PaymentID == remainingDebt.PaymentID);
                        if (payment != null)
                        {
                            // Null kontrolü yaparak atama yap
                            payment.FinalPaymentMethod = debtPaymentDto.PaymentMethod ?? "Belirsiz";
                            payment.PaymentStatus = "Completed";

                            // Eğer OriginalPaymentMethod null ise, onu da güncelle
                            if (string.IsNullOrEmpty(payment.OriginalPaymentMethod))
                            {
                                payment.OriginalPaymentMethod = payment.PaymentMethod ?? "Borç";
                            }

                            _paymentDal.Update(payment);
                        }
                    }

                    scope.Complete();
                    return new SuccessResult(Messages.PaymentAdded);
                }
                catch (Exception ex)
                {
                    scope.Dispose();
                    return new ErrorResult($"Ödeme işlemi sırasında bir hata oluştu: {ex.Message}");
                }
            }
        }


    }
}