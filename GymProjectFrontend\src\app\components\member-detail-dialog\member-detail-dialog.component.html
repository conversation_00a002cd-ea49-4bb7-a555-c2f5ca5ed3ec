<div class="member-detail-dialog">
  <div class="dialog-header">
    <h2 class="dialog-title"><PERSON><PERSON> Detayları</h2>
    <button class="btn-close" (click)="closeDialog()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="dialog-content">
    <!-- Yükleme Göstergesi -->
    <div class="loading-container" *ngIf="isLoading">
      <app-loading-spinner></app-loading-spinner>
    </div>

    <!-- Hata Durumu -->
    <div class="error-container" *ngIf="!isLoading && !memberDetail">
      <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
      <h4>Üye bilgileri yüklenemedi</h4>
      <p>Lütfen daha sonra tekrar deneyin.</p>
    </div>

    <!-- <PERSON><PERSON>ları -->
    <div class="member-details" *ngIf="!isLoading && memberDetail">
      <!-- Üst <PERSON>il<PERSON> -->
      <div class="member-header-card">
        <div class="member-avatar">
          <div class="avatar-circle-lg" [style.background-color]="getGenderColor(memberDetail.gender)">
            <img
              *ngIf="profileImageUrl && memberDetail.userID"
              [src]="profileImageUrl"
              [alt]="memberDetail.name"
              class="profile-image"
              (error)="profileImageUrl = null"
            />
            <i
              *ngIf="!profileImageUrl || !memberDetail.userID"
              class="fas fa-user profile-icon"
            ></i>
          </div>
        </div>
        <div class="member-header-info">
          <h3>
            {{ memberDetail.name }}
            <span *ngIf="memberDetail.remainingDays && memberDetail.remainingDays > 0" class="remaining-days">
              ({{ memberDetail.remainingDays }} gün kaldı)
            </span>
          </h3>
          
          <div class="member-contact">
            <div><i class="fas fa-phone-alt"></i> {{ memberDetail.phoneNumber }}</div>
            <div *ngIf="memberDetail.email"><i class="fas fa-envelope"></i> {{ memberDetail.email }}</div>
          </div>
        </div>
      </div>

      <!-- Sekmeler -->
      <div class="tabs-container">
        <div class="tabs-header">
          <div class="tab" [class.active]="activeTab === 'personal'" (click)="setActiveTab('personal')">
            <i class="fas fa-user"></i> Kişisel Bilgiler
          </div>
          <div class="tab" [class.active]="activeTab === 'memberships'" (click)="setActiveTab('memberships')">
            <i class="fas fa-id-card"></i> Üyelikler
            <span class="badge" *ngIf="memberDetail.memberships?.length">{{ memberDetail.memberships.length }}</span>
          </div>
          <div class="tab" [class.active]="activeTab === 'payments'" (click)="setActiveTab('payments')">
            <i class="fas fa-money-bill"></i> Ödemeler
            <span class="badge" *ngIf="memberDetail.payments?.length">{{ memberDetail.payments.length }}</span>
          </div>
          <div class="tab" [class.active]="activeTab === 'entries'" (click)="setActiveTab('entries')">
            <i class="fas fa-sign-in-alt"></i> Giriş/Çıkışlar
            <span class="badge" *ngIf="memberDetail.entryExitHistory?.length">{{ memberDetail.entryExitHistory.length }}</span>
          </div>
          <div class="tab" [class.active]="activeTab === 'freezes'" (click)="setActiveTab('freezes')">
            <i class="fas fa-snowflake"></i> Dondurma Geçmişi
            <span class="badge" *ngIf="memberDetail.freezeHistory?.length">{{ memberDetail.freezeHistory.length }}</span>
          </div>
        </div>

        <!-- Kişisel Bilgiler Sekmesi -->
        <div class="tab-content" *ngIf="activeTab === 'personal'">
          <div class="info-section">
            <h4>Kişisel Bilgiler</h4>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Üye ID</div>
                <div class="info-value">{{ memberDetail.memberID }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Ad Soyad</div>
                <div class="info-value">{{ memberDetail.name }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Cinsiyet</div>
                <div class="info-value">{{ getGenderText(memberDetail.gender) }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Telefon</div>
                <div class="info-value">{{ memberDetail.phoneNumber }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">E-posta</div>
                <div class="info-value">{{ memberDetail.email || 'Belirtilmemiş' }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Doğum Tarihi</div>
                <div class="info-value">{{ memberDetail.birthDate ? (memberDetail.birthDate | date:'dd.MM.yyyy') : 'Belirtilmemiş' }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Adres</div>
                <div class="info-value">{{ memberDetail.adress || 'Belirtilmemiş' }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Bakiye</div>
                <div class="info-value">{{ memberDetail.balance | currency:'TRY':'symbol':'1.2-2' }}</div>
              </div>
              
              <div class="info-item">
                <div class="info-label">Kayıt Tarihi</div>
                <div class="info-value">{{ memberDetail.creationDate ? (memberDetail.creationDate | date:'dd.MM.yyyy HH:mm') : 'Belirtilmemiş' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Üyelikler Sekmesi -->
        <div class="tab-content" *ngIf="activeTab === 'memberships'">
          <div class="info-section">
            <h4>Üyelik Geçmişi</h4>
            <div class="table-responsive" *ngIf="memberDetail.memberships?.length">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Üyelik Türü</th>
                    <th>Branş</th>
                    <th>Başlangıç</th>
                    <th>Bitiş</th>
                    <th>Süre (Gün)</th>
                    <th>Ücret</th>
                    <th>Durum</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let membership of memberDetail.memberships">
                    <td>{{ membership.typeName }}</td>
                    <td>{{ membership.branch }}</td>
                    <td>{{ membership.startDate | date:'dd.MM.yyyy' }}</td>
                    <td>{{ membership.endDate | date:'dd.MM.yyyy' }}</td>
                    <td>{{ membership.day }}</td>
                    <td>{{ membership.price | currency:'TRY':'symbol':'1.2-2' }}</td>
                    <td>
                      <div class="status-badge" [ngClass]="getMembershipStatusClass(membership.isActive)">
                        {{ getMembershipStatusText(membership.isActive) }}
                      </div>
                      <div class="status-badge status-frozen" *ngIf="membership.isFrozen">
                        Dondurulmuş
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="no-data" *ngIf="!memberDetail.memberships?.length">
              <i class="fas fa-info-circle"></i> Üyelik kaydı bulunamadı.
            </div>
          </div>
        </div>

        <!-- Ödemeler Sekmesi -->
        <div class="tab-content" *ngIf="activeTab === 'payments'">
          <div class="info-section">
            <h4>Ödeme Geçmişi</h4>
            <div class="table-responsive" *ngIf="memberDetail.payments?.length">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Tarih</th>
                    <th>Üyelik Türü</th>
                    <th>Branş</th>
                    <th>Tutar</th>
                    <th>Ödeme Yöntemi</th>
                    <th>Durum</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let payment of memberDetail.payments">
                    <td>{{ payment.paymentDate | date:'dd.MM.yyyy HH:mm' }}</td>
                    <td>{{ payment.membershipTypeName }}</td>
                    <td>{{ payment.branch }}</td>
                    <td>{{ payment.paymentAmount | currency:'TRY':'symbol':'1.2-2' }}</td>
                    <td>{{ payment.paymentMethod }}</td>
                    <td>
                      <div class="status-badge" [ngClass]="getPaymentStatusClass(payment.paymentStatus)">
                        {{ getPaymentStatusText(payment.paymentStatus) }}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="no-data" *ngIf="!memberDetail.payments?.length">
              <i class="fas fa-info-circle"></i> Ödeme kaydı bulunamadı.
            </div>
          </div>
        </div>

        <!-- Giriş/Çıkışlar Sekmesi -->
        <div class="tab-content" *ngIf="activeTab === 'entries'">
          <div class="info-section">
            <h4>Giriş/Çıkış Geçmişi</h4>
            <div class="table-responsive" *ngIf="memberDetail.entryExitHistory?.length">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Giriş Tarihi</th>
                    <th>Çıkış Tarihi</th>
                    <th>Üyelik Türü</th>
                    <th>Şube</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let entry of memberDetail.entryExitHistory">
                    <td>{{ entry.entryDate | date:'dd.MM.yyyy HH:mm' }}</td>
                    <td>{{ entry.exitDate && !isAutoExitAt5AM(entry.exitDate) ? (entry.exitDate | date:'dd.MM.yyyy HH:mm') : 'Çıkış yapılmadı' }}</td>
                    <td>{{ entry.membershipTypeName }}</td>
                    <td>{{ entry.branch }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="no-data" *ngIf="!memberDetail.entryExitHistory?.length">
              <i class="fas fa-info-circle"></i> Giriş/çıkış kaydı bulunamadı.
            </div>
          </div>
        </div>

        <!-- Dondurma Geçmişi Sekmesi -->
        <div class="tab-content" *ngIf="activeTab === 'freezes'">
          <div class="info-section">
            <h4>Üyelik Dondurma Geçmişi</h4>
            <div class="table-responsive" *ngIf="memberDetail.freezeHistory?.length">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Başlangıç</th>
                    <th>Planlanan Bitiş</th>
                    <th>Gerçek Bitiş</th>
                    <th>Toplam Gün</th>
                    <th>Kullanılan Gün</th>
                    <th>Üyelik Türü</th>
                    <th>Şube</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let freeze of memberDetail.freezeHistory">
                    <td>{{ freeze.startDate | date:'dd.MM.yyyy' }}</td>
                    <td>{{ freeze.plannedEndDate | date:'dd.MM.yyyy' }}</td>
                    <td>{{ freeze.actualEndDate ? (freeze.actualEndDate | date:'dd.MM.yyyy') : 'Devam ediyor' }}</td>
                    <td>{{ freeze.freezeDays }}</td>
                    <td>{{ freeze.usedDays || 0 }}</td>
                    <td>{{ freeze.membershipTypeName }}</td>
                    <td>{{ freeze.branch }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="no-data" *ngIf="!memberDetail.freezeHistory?.length">
              <i class="fas fa-info-circle"></i> Dondurma kaydı bulunamadı.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


</div>
