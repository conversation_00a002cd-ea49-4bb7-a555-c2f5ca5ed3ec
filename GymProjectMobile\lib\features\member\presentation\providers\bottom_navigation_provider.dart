/// Bottom Navigation Provider - GymKod Pro Mobile
///
/// Bu provider bottom navigation bar'ın aktif tab durumunu yönetir.
/// Riverpod state management kullanır.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/services/logging_service.dart';

/// Bottom Navigation State
class BottomNavigationState {
  final int currentIndex;
  final List<BottomNavigationItem> items;

  const BottomNavigationState({
    required this.currentIndex,
    required this.items,
  });

  BottomNavigationState copyWith({
    int? currentIndex,
    List<BottomNavigationItem>? items,
  }) {
    return BottomNavigationState(
      currentIndex: currentIndex ?? this.currentIndex,
      items: items ?? this.items,
    );
  }
}

/// Bottom Navigation Item
class BottomNavigationItem {
  final String label;
  final String icon;
  final String route;
  final bool isActive;

  const BottomNavigationItem({
    required this.label,
    required this.icon,
    required this.route,
    this.isActive = false,
  });

  BottomNavigationItem copyWith({
    String? label,
    String? icon,
    String? route,
    bool? isActive,
  }) {
    return BottomNavigationItem(
      label: label ?? this.label,
      icon: icon ?? this.icon,
      route: route ?? this.route,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// Bottom Navigation Notifier
class BottomNavigationNotifier extends StateNotifier<BottomNavigationState> {
  BottomNavigationNotifier() : super(_initialState);

  static const _initialState = BottomNavigationState(
    currentIndex: 0,
    items: [
      BottomNavigationItem(
        label: 'QR Kodum',
        icon: 'qr_code',
        route: '/member-main', // Member main layout içinde QR sayfası (index 0)
        isActive: true,
      ),
      BottomNavigationItem(
        label: 'Antrenman',
        icon: 'fitness_center',
        route: '/member-main', // Member main layout içinde antrenman sayfası (index 1)
      ),
      BottomNavigationItem(
        label: 'Profil',
        icon: 'person',
        route: '/member-main', // Member main layout içinde profil sayfası (index 2)
      ),
    ],
  );

  /// Tab değiştir
  void changeTab(int index) {
    if (index < 0 || index >= state.items.length) {
      LoggingService.error('Invalid tab index: $index', tag: 'BOTTOM_NAV');
      return;
    }

    if (state.currentIndex == index) {
      LoggingService.info('Tab already selected: $index', tag: 'BOTTOM_NAV');
      return;
    }

    LoggingService.navigationLog(
      'Bottom navigation tab changed',
      state.items[index].route,
      details: 'From index ${state.currentIndex} to $index',
    );

    // Yeni state oluştur
    final updatedItems = state.items.asMap().entries.map((entry) {
      final itemIndex = entry.key;
      final item = entry.value;
      return item.copyWith(isActive: itemIndex == index);
    }).toList();

    state = state.copyWith(
      currentIndex: index,
      items: updatedItems,
    );
  }

  /// Aktif tab'ı route'a göre ayarla
  void setActiveTabByRoute(String route) {
    final index = state.items.indexWhere((item) => item.route == route);
    if (index != -1) {
      changeTab(index);
    } else {
      LoggingService.error('Route not found in bottom navigation: $route', tag: 'BOTTOM_NAV');
    }
  }

  /// Aktif tab'ın route'unu al
  String get currentRoute {
    if (state.currentIndex >= 0 && state.currentIndex < state.items.length) {
      return state.items[state.currentIndex].route;
    }
    return state.items.first.route;
  }

  /// Aktif tab'ın label'ını al
  String get currentLabel {
    if (state.currentIndex >= 0 && state.currentIndex < state.items.length) {
      return state.items[state.currentIndex].label;
    }
    return state.items.first.label;
  }
}

/// Bottom Navigation Provider
final bottomNavigationProvider = StateNotifierProvider<BottomNavigationNotifier, BottomNavigationState>((ref) {
  return BottomNavigationNotifier();
});

/// Aktif tab index provider (computed)
final currentTabIndexProvider = Provider<int>((ref) {
  return ref.watch(bottomNavigationProvider).currentIndex;
});

/// Aktif tab route provider (computed)
final currentTabRouteProvider = Provider<String>((ref) {
  final notifier = ref.read(bottomNavigationProvider.notifier);
  return notifier.currentRoute;
});

/// Aktif tab label provider (computed)
final currentTabLabelProvider = Provider<String>((ref) {
  final notifier = ref.read(bottomNavigationProvider.notifier);
  return notifier.currentLabel;
});
