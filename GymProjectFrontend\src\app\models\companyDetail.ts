export interface CompanyDetail{
    companyUserId:number;
    companyId:number;
    companyAdressId:number;
    userCompanyId:number;
    companyUserName:string;
    cityName:string;
    townName:string;
    companyUserPhoneNumber:string
    companyPhoneNumber:string;
    companyUserEmail:string;
    companyAdress:string;
    companyName:string;
    isActive:boolean;

    // Dashboard için eklenen opsiyonel alanlar
    remainingDays?: number;
    licenseStatus?: string;
}