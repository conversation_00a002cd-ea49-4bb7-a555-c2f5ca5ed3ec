using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Microsoft.AspNetCore.Http;

namespace Business.Concrete
{
    public class ProfileManager : IProfileService
    {
        IUserDal _userDal;
        IFileService _fileService;
        IAdvancedRateLimitService _advancedRateLimitService;

        public ProfileManager(IUserDal userDal, IFileService fileService, IAdvancedRateLimitService advancedRateLimitService)
        {
            _userDal = userDal;
            _fileService = fileService;
            _advancedRateLimitService = advancedRateLimitService;
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IDataResult<string> UploadProfileImage(IFormFile file, int userId)
        {
            // Rate limiting kontrolü
            var rateLimitCheck = _advancedRateLimitService.CheckProfileImageUploadAttempt(userId);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<string>(rateLimitCheck.Message);
            }

            var user = _userDal.Get(u => u.UserID == userId && u.IsActive);
            if (user == null)
            {
                return new ErrorDataResult<string>(Messages.UserNotFound);
            }

            var uploadResult = _fileService.UploadProfileImage(file, userId);
            if (!uploadResult.Success)
            {
                return uploadResult;
            }

            // Kullanıcının profil resim yolunu güncelle
            user.ProfileImagePath = uploadResult.Data;
            _userDal.Update(user);

            // Başarılı upload kaydı
            _advancedRateLimitService.RecordProfileImageUpload(userId);

            return new SuccessDataResult<string>(uploadResult.Data, "Profil fotoğrafı başarıyla yüklendi.");
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult DeleteProfileImage(int userId)
        {
            var user = _userDal.Get(u => u.UserID == userId && u.IsActive);
            if (user == null)
            {
                return new ErrorResult(Messages.UserNotFound);
            }

            var deleteResult = _fileService.DeleteProfileImage(userId);
            if (!deleteResult.Success)
            {
                return deleteResult;
            }

            // Kullanıcının profil resim yolunu temizle
            user.ProfileImagePath = null;
            _userDal.Update(user);

            return new SuccessResult("Profil fotoğrafı başarıyla silindi.");
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult UpdateProfileImagePath(int userId, string imagePath)
        {
            var user = _userDal.Get(u => u.UserID == userId && u.IsActive);
            if (user == null)
            {
                return new ErrorResult(Messages.UserNotFound);
            }

            user.ProfileImagePath = imagePath;
            _userDal.Update(user);

            return new SuccessResult("Profil fotoğrafı yolu güncellendi.");
        }
    }
}
