Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8BF0) msys-2.0.dll+0x1FE8E
0007FFFF9CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x67F9
0007FFFF9CF0  000210046832 (000210286019, 0007FFFF9BA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9CF0  000210068E24 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9FD0  00021006A225 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFED9C00000 ntdll.dll
7FFED8A20000 KERNEL32.DLL
7FFED6D50000 KERNELBASE.dll
7FFED8820000 USER32.dll
7FFED7840000 win32u.dll
7FFED8560000 GDI32.dll
7FFED7370000 gdi32full.dll
7FFED7140000 msvcp_win.dll
7FFED76F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFED8170000 advapi32.dll
7FFED8430000 msvcrt.dll
7FFED8CF0000 sechost.dll
7FFED8590000 RPCRT4.dll
7FFED6470000 CRYPTBASE.DLL
7FFED7900000 bcryptPrimitives.dll
7FFED83F0000 IMM32.DLL
