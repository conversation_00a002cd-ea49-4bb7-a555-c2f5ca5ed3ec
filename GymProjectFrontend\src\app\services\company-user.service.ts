import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { CompanyUser } from '../models/companyUser';
import { ResponseModel } from '../models/responseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { BaseApiService } from './baseApiService';
import { CompanyUserDetail } from '../models/companyUserDetails';
import { CompanyUserFullDetail } from '../models/companyUserFullDetail';
import { PaginatedResult } from '../models/paginatedResult';
import { CompanyUserFullUpdate } from '../models/companyUserFullUpdate';
import { DeletedCompanyUser } from '../models/deletedCompanyUser';

@Injectable({
  providedIn: 'root',
})
export class CompanyUserService extends BaseApiService{
  constructor(private httpClient:HttpClient) { 
    super();
  }
  getCompanyUsers(): Observable<ListResponseModel<CompanyUser>> {
    let newPath = this.apiUrl + 'companyUser/getall';
    return this.httpClient.get<ListResponseModel<CompanyUser>>(newPath);
  }
  
    add(companyUser: CompanyUser): Observable<ResponseModel> {
      return this.httpClient.post<ResponseModel>(
        this.apiUrl + 'companyuser/add',
        companyUser
      );
    }
    getCompanyUserDetails(): Observable<ListResponseModel<CompanyUserDetail>> {
      let newPath = this.apiUrl + 'companyUser/getcompanyuserdetails';
      return this.httpClient.get<ListResponseModel<CompanyUserDetail>>(newPath);
    }
    update(company: CompanyUser): Observable<ResponseModel> {
      return this.httpClient.post<ResponseModel>(
        this.apiUrl + 'companyuser/update',
        company
      );
    }
  
    delete(companyUserId: number): Observable<ResponseModel> {
      return this.httpClient.delete<ResponseModel>(
        this.apiUrl + 'companyuser/delete?id=' + companyUserId
      );
    }

    // Yeni eklenen metodlar
    getById(id: number): Observable<SingleResponseModel<CompanyUser>> {
      return this.httpClient.get<SingleResponseModel<CompanyUser>>(
        `${this.apiUrl}companyuser/getbyid?id=${id}`
      );
    }

    getFullDetails(id: number): Observable<SingleResponseModel<CompanyUserFullDetail>> {
      return this.httpClient.get<SingleResponseModel<CompanyUserFullDetail>>(
        `${this.apiUrl}companyuser/getfulldetails?id=${id}`
      );
    }

    getPaginated(pageNumber: number = 1, pageSize: number = 10, searchTerm: string = ''): Observable<SingleResponseModel<PaginatedResult<CompanyUserFullDetail>>> {
      let params = new HttpParams()
        .set('pageNumber', pageNumber.toString())
        .set('pageSize', pageSize.toString());

      if (searchTerm) {
        params = params.set('searchTerm', searchTerm);
      }

      return this.httpClient.get<SingleResponseModel<PaginatedResult<CompanyUserFullDetail>>>(
        `${this.apiUrl}companyuser/getpaginated`,
        { params }
      );
    }

    updateFull(updateDto: CompanyUserFullUpdate): Observable<ResponseModel> {
      return this.httpClient.post<ResponseModel>(
        `${this.apiUrl}companyuser/updatefull`,
        updateDto
      );
    }

    // Salon soft delete
    softDelete(companyUserId: number): Observable<ResponseModel> {
      return this.httpClient.delete<ResponseModel>(
        `${this.apiUrl}companyuser/soft-delete?id=${companyUserId}`
      );
    }

    // Silinen salonları getir
    getDeletedCompanyUsers(): Observable<ListResponseModel<DeletedCompanyUser>> {
      return this.httpClient.get<ListResponseModel<DeletedCompanyUser>>(
        `${this.apiUrl}companyuser/get-deleted`
      );
    }

    // Salon geri yükle
    restoreCompanyUser(companyUserId: number): Observable<ResponseModel> {
      return this.httpClient.post<ResponseModel>(
        `${this.apiUrl}companyuser/restore?id=${companyUserId}`,
        {}
      );
    }
}
