/// Auth Provider - GymKod Pro Mobile
///
/// Bu provider Riverpod ile auth state management'ı yapar.
/// Referans: Angular frontend'deki auth service state management
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/models.dart';
import '../../../../core/services/services.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';

/// Auth State
class AuthState {
  final bool isAuthenticated;
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isInitialized;

  /// <PERSON><PERSON>re <PERSON>ğ<PERSON>ştirme zorunluluğu (Angular frontend pattern)
  final bool requirePasswordChange;

  const AuthState({
    this.isAuthenticated = false,
    this.user,
    this.isLoading = false,
    this.error,
    this.isInitialized = false,
    this.requirePasswordChange = false,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isInitialized,
    bool? requirePasswordChange,
    bool clearUser = false,
    bool clearError = false,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: clearUser ? null : (user ?? this.user),
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      isInitialized: isInitialized ?? this.isInitialized,
      requirePasswordChange: requirePasswordChange ?? this.requirePasswordChange,
    );
  }

  @override
  String toString() {
    return 'AuthState(isAuthenticated: $isAuthenticated, user: ${user?.name}, isLoading: $isLoading, error: $error, isInitialized: $isInitialized, requirePasswordChange: $requirePasswordChange)';
  }
}

/// Auth Repository Provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl();
});

/// Auth State Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  final DeviceService _deviceService;
  final StorageService _storageService;
  final UnifiedTokenManager _unifiedTokenManager = UnifiedTokenManager();

  AuthNotifier(this._authRepository, this._deviceService, this._storageService) : super(const AuthState()) {
    _initialize();
  }

  /// Auth state'i initialize et - Geliştirilmiş token kontrolü
  Future<void> _initialize() async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Initializing');

      state = state.copyWith(isLoading: true);

      // Önce token'ların varlığını kontrol et
      final accessToken = await _storageService.getAccessToken();
      final refreshToken = await _storageService.getRefreshToken();

      LoggingService.authLog('Initialize token check',
        details: 'AccessToken: ${accessToken != null ? "exists" : "null"}, RefreshToken: ${refreshToken != null ? "exists" : "null"}');

      if (accessToken == null || refreshToken == null) {
        // Token yoksa direkt not authenticated
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          isInitialized: true,
          clearUser: true,
          clearError: true,
        );
        LoggingService.stateLog('AuthNotifier', 'Initialized as not authenticated - no tokens');
        return;
      }

      // Token'lar varsa UnifiedTokenManager ile app start validation yap
      final tokenValidationResult = await _unifiedTokenManager.validateTokenOnAppStart();
      final isAuthenticated = tokenValidationResult;

      if (isAuthenticated) {
        // Kullanıcı bilgilerini al
        final user = await _authRepository.getCurrentUser();
        final requirePasswordChange = await _storageService.getRequirePasswordChange();

        if (user != null) {
          state = state.copyWith(
            isAuthenticated: true,
            user: user,
            isLoading: false,
            isInitialized: true,
            requirePasswordChange: requirePasswordChange,
            clearError: true,
          );

          // Unified token manager'ı başlat
          await _startUnifiedTokenManager();

          LoggingService.stateLog('AuthNotifier', 'Initialized as authenticated',
            state: 'User: ${user.name}, RequirePasswordChange: $requirePasswordChange');
        } else {
          // User bilgisi alınamazsa logout
          await _storageService.clearAuthTokens();
          state = state.copyWith(
            isAuthenticated: false,
            isLoading: false,
            isInitialized: true,
            clearUser: true,
            clearError: true,
          );
          LoggingService.stateLog('AuthNotifier', 'Initialized as not authenticated - no user data');
        }
      } else {
        // Authentication başarısızsa token'ları temizle
        await _storageService.clearAuthTokens();
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          isInitialized: true,
          clearUser: true,
          clearError: true,
        );
        LoggingService.stateLog('AuthNotifier', 'Initialized as not authenticated - auth failed');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Initialize');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        isInitialized: true,
        error: 'Başlatma sırasında hata oluştu',
        clearUser: true,
      );
    }
  }

  /// Kullanıcı girişi
  Future<bool> login({
    required String email,
    required String password,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Login attempt', state: email);

      state = state.copyWith(isLoading: true, clearError: true);

      // Device info al ve storage'a kaydet
      final deviceInfoObj = await _deviceService.getDeviceInfo();
      await _storageService.saveDeviceInfo(deviceInfoObj);
      final deviceInfo = deviceInfoObj.toDeviceInfoString();

      // Login API çağrısı
      final result = await _authRepository.login(
        email: email,
        password: password,
        deviceInfo: deviceInfo,
      );

      if (result.isSuccess && result.data != null) {
        // User data'yı al
        final user = await _authRepository.getCurrentUser();

        // Şifre değiştirme zorunluluğunu kontrol et (Angular frontend pattern)
        final requirePasswordChange = result.extraData?['requirePasswordChange'] == true;

        // Production'da detaylı debug log kaldırıldı

        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          requirePasswordChange: requirePasswordChange,
          clearError: true,
        );

        // Unified token manager'ı başlat
        await _startUnifiedTokenManager();

        LoggingService.stateLog('AuthNotifier', 'Login successful', state: 'User: ${user?.name}, RequirePasswordChange: $requirePasswordChange');
        return true;
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          error: result.message,
          clearUser: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Login failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Login');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: 'Giriş işlemi sırasında hata oluştu',
        clearUser: true,
      );

      return false;
    }
  }

  /// Üye kaydı
  Future<bool> registerMember({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Member register attempt', state: email);

      state = state.copyWith(isLoading: true, clearError: true);

      // Device info al
      final deviceInfo = await _deviceService.getDeviceInfoString();

      // Register API çağrısı
      final result = await _authRepository.registerMember(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        deviceInfo: deviceInfo,
      );

      if (result.isSuccess && result.data != null) {
        // User data'yı al
        final user = await _authRepository.getCurrentUser();

        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          clearError: true,
        );

        // Unified token manager'ı başlat
        await _startUnifiedTokenManager();

        LoggingService.stateLog('AuthNotifier', 'Member register successful', state: user?.name);
        return true;
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          error: result.message,
          clearUser: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Member register failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Member Register');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: 'Kayıt işlemi sırasında hata oluştu',
        clearUser: true,
      );

      return false;
    }
  }

  /// Admin/Owner kaydı
  Future<bool> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Admin register attempt', state: email);

      state = state.copyWith(isLoading: true, clearError: true);

      // Device info al
      final deviceInfo = await _deviceService.getDeviceInfoString();

      // Register API çağrısı
      final result = await _authRepository.register(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        deviceInfo: deviceInfo,
      );

      if (result.isSuccess && result.data != null) {
        // User data'yı al
        final user = await _authRepository.getCurrentUser();

        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
          clearError: true,
        );

        // Unified token manager'ı başlat
        await _startUnifiedTokenManager();

        LoggingService.stateLog('AuthNotifier', 'Admin register successful', state: user?.name);
        return true;
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
          error: result.message,
          clearUser: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Admin register failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Admin Register');

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: 'Kayıt işlemi sırasında hata oluştu',
        clearUser: true,
      );

      return false;
    }
  }

  /// Çıkış yapma
  Future<void> logout({String? reason}) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Logout${reason != null ? ' - $reason' : ''}');

      state = state.copyWith(isLoading: true);

      // Repository'den logout
      await _authRepository.logout();

      // Unified token manager'ı durdur
      _stopUnifiedTokenManager();

      // Özel logout mesajı varsa error olarak set et
      String? logoutMessage;
      if (reason != null && reason.contains('başka bir cihazdan giriş yapılmış')) {
        logoutMessage = 'Hesabınıza başka bir cihazdan giriş yapıldı. Güvenlik nedeniyle oturumunuz sonlandırıldı.';
      }

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        clearUser: true,
        error: logoutMessage,
      );

      LoggingService.stateLog('AuthNotifier', 'Logout successful');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Logout');

      // Hata olsa bile logout yap
      _stopUnifiedTokenManager();

      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        clearUser: true,
        clearError: true,
      );
    }
  }

  /// Profil bilgilerini yenile
  Future<void> refreshProfile() async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Refresh profile');

      if (!state.isAuthenticated) return;

      final result = await _authRepository.getProfile();

      if (result.isSuccess && result.data != null) {
        state = state.copyWith(
          user: result.data,
          clearError: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Profile refreshed', state: result.data?.name);
      } else {
        LoggingService.stateLog('AuthNotifier', 'Profile refresh failed', state: result.message);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Refresh Profile');
    }
  }

  /// Şifre değiştirme (Angular frontend pattern)
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      LoggingService.stateLog('AuthNotifier', 'Change password attempt');

      state = state.copyWith(isLoading: true, clearError: true);

      // Change password API çağrısı
      final result = await _authRepository.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.isSuccess) {
        // Şifre değiştirme zorunluluğunu kaldır
        state = state.copyWith(
          isLoading: false,
          requirePasswordChange: false,
          clearError: true,
        );

        LoggingService.stateLog('AuthNotifier', 'Password changed successfully');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );

        LoggingService.stateLog('AuthNotifier', 'Password change failed', state: result.message);
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier Change Password');

      state = state.copyWith(
        isLoading: false,
        error: 'Şifre değiştirme işlemi sırasında hata oluştu',
      );

      return false;
    }
  }

  /// Error'ı temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Auth durumunu kontrol et
  Future<void> checkAuthStatus() async {
    try {
      final isAuthenticated = await _authRepository.isAuthenticated();

      if (isAuthenticated != state.isAuthenticated) {
        if (isAuthenticated) {
          final user = await _authRepository.getCurrentUser();
          state = state.copyWith(
            isAuthenticated: true,
            user: user,
          );
          // Unified token manager'ı başlat
          await _startUnifiedTokenManager();
        } else {
          state = state.copyWith(
            isAuthenticated: false,
            clearUser: true,
          );
          // Unified token manager'ı durdur
          _stopUnifiedTokenManager();
        }
      }
    } catch (e) {
      LoggingService.stateLog('AuthNotifier', 'Auth status check error', state: e.toString());
    }
  }

  /// Token'ları validate et ve gerekirse refresh et (App lifecycle için)
  Future<bool> validateAndRefreshTokens() async {
    try {
      LoggingService.authLog('Starting token validation and refresh check');

      if (!state.isAuthenticated) {
        LoggingService.authLog('User not authenticated, skipping token validation');
        return false;
      }

      // UnifiedTokenManager'a app resume kontrolü yaptır
      final result = await _unifiedTokenManager.handleAppResume();

      if (result) {
        LoggingService.authLog('Token validation successful');
        // User data'yı güncelle
        await _refreshUserDataFromToken();
        return true;
      } else {
        LoggingService.authLog('Token validation failed');
        await logout();
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier validateAndRefreshTokens');
      await logout();
      return false;
    }
  }



  /// Unified token manager'ı başlat
  Future<void> _startUnifiedTokenManager() async {
    try {
      LoggingService.authLog('Starting unified token manager');

      await _unifiedTokenManager.initialize(
        onTokenRefreshed: () {
          LoggingService.authLog('Token refreshed by unified manager - updating user data');
          _refreshUserDataFromToken();
        },
        onRefreshFailed: (String? reason) {
          LoggingService.authLog('Unified token manager refresh failed - logging out user');
          logout(reason: reason);
        },
      );

      LoggingService.authLog('Unified token manager started successfully');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier _startUnifiedTokenManager');
    }
  }

  /// Unified token manager'ı durdur
  void _stopUnifiedTokenManager() {
    try {
      LoggingService.authLog('Stopping unified token manager');

      _unifiedTokenManager.stop();

      LoggingService.authLog('Unified token manager stopped');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier _stopUnifiedTokenManager');
    }
  }

  /// Token'dan user data'yı yenile
  Future<void> _refreshUserDataFromToken() async {
    try {
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        state = state.copyWith(user: user);
        LoggingService.authLog('User data refreshed from token', details: user.name);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier _refreshUserDataFromToken');
    }
  }

  /// Enhanced Debug: Token durumunu ve service'leri kontrol et
  Future<Map<String, dynamic>> getTokenDebugInfo() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      final refreshToken = await _storageService.getRefreshToken();
      final deviceInfo = await _storageService.getDeviceInfo();

      Map<String, dynamic> debugInfo = {
        'hasAccessToken': accessToken != null,
        'hasRefreshToken': refreshToken != null,
        'hasDeviceInfo': deviceInfo != null,
        'isAuthenticated': state.isAuthenticated,
        'isInitialized': state.isInitialized,
        'user': state.user?.name,
      };

      if (accessToken != null) {
        final jwtService = JwtService();
        debugInfo['accessTokenValid'] = jwtService.isTokenValid(accessToken);
        final remainingTime = jwtService.getTokenRemainingTime(accessToken);
        debugInfo['tokenRemainingMinutes'] = remainingTime?.inMinutes;
        debugInfo['tokenRemainingSeconds'] = remainingTime?.inSeconds;
        debugInfo['shouldRefreshToken'] = jwtService.shouldRefreshToken(accessToken);
        debugInfo['isTokenAboutToExpire'] = jwtService.isTokenAboutToExpire(accessToken);
      }

      if (deviceInfo != null) {
        debugInfo['deviceInfoString'] = deviceInfo.toDeviceInfoString();
      }

      // Service durumları
      debugInfo['unifiedTokenManagerActive'] = _unifiedTokenManager.isActive;
      debugInfo['unifiedTokenManagerRefreshing'] = _unifiedTokenManager.isRefreshing;

      LoggingService.authLog('Enhanced Token Debug Info', details: debugInfo.toString());
      return debugInfo;
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier getTokenDebugInfo');
      return {'error': e.toString()};
    }
  }

  /// Manuel token refresh tetikle (debug için)
  Future<bool> triggerManualTokenRefresh() async {
    try {
      LoggingService.authLog('Manual token refresh triggered');
      return await _unifiedTokenManager.triggerManualRefresh();
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthNotifier triggerManualTokenRefresh');
      return false;
    }
  }
}

/// Auth State Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authRepository = ref.read(authRepositoryProvider);
  final deviceService = DeviceService();
  final storageService = StorageService();
  return AuthNotifier(authRepository, deviceService, storageService);
});

/// Auth State Getters
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});

final authInitializedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isInitialized;
});

final requirePasswordChangeProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).requirePasswordChange;
});

/// Helper classes for enhanced token validation

/// Token existence check result
class TokenExistenceResult {
  final bool hasTokens;
  final String? accessToken;
  final String? refreshToken;

  const TokenExistenceResult({
    required this.hasTokens,
    this.accessToken,
    this.refreshToken,
  });
}

/// Token validity check result
class TokenValidityResult {
  final bool isValid;
  final String? reason;
  final Duration? remainingTime;

  const TokenValidityResult({
    required this.isValid,
    this.reason,
    this.remainingTime,
  });
}

/// Token refresh attempt result
class TokenRefreshResult {
  final bool success;
  final String message;

  const TokenRefreshResult({
    required this.success,
    required this.message,
  });
}
