/// Auth Repository Implementation - GymKod Pro Mobile
///
/// Bu class auth repository interface'in implementation'ıdır.
/// Referans: Angular frontend'deki auth service implementation
library;

import 'package:dio/dio.dart';
import '../../../../core/models/models.dart';
import '../../../../core/services/services.dart';
import '../../domain/repositories/auth_repository.dart';

/// Auth Repository Implementation
/// Clean Architecture Data Layer
class AuthRepositoryImpl implements AuthRepository {
  final ApiService _apiService;
  final StorageService _storageService;
  final JwtService _jwtService;


  AuthRepositoryImpl({
    ApiService? apiService,
    StorageService? storageService,
    JwtService? jwtService,
  })  : _apiService = apiService ?? ApiService(),
        _storageService = storageService ?? StorageService(),
        _jwtService = jwtService ?? JwtService();

  @override
  Future<ApiResponse<AuthData>> login({
    required String email,
    required String password,
    required String deviceInfo,
  }) async {
    try {
      LoggingService.authLog('Login attempt', details: 'Email: $email');

      final loginRequest = LoginRequest(
        loginDto: LoginDto(
          email: email,
          password: password,
        ),
        deviceInfo: deviceInfo,
      );

      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/login',
        data: loginRequest.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        // Raw response'ı logla (debug için)
        LoggingService.authLog('Raw login response', details: 'Response: ${response.data}');

        final authResponse = AuthResponse.fromJson(response.data!);

        LoggingService.authLog('Login response parsed', details: 'Success: ${authResponse.success}, Message: ${authResponse.message}, RequirePasswordChange: ${authResponse.requirePasswordChange}');

        if (authResponse.success && authResponse.data != null) {
          // Token'ları kaydet
          await _storageService.saveAccessToken(authResponse.data!.token);
          await _storageService.saveRefreshToken(authResponse.data!.refreshToken);

          // Şifre değiştirme zorunluluğunu kaydet (Angular frontend pattern)
          if (authResponse.requirePasswordChange == true) {
            await _storageService.setRequirePasswordChange(true);
            LoggingService.authLog('Password change required', details: 'User must change password');
          } else {
            await _storageService.setRequirePasswordChange(false);
          }

          // User data'yı decode et ve kaydet
          final userModel = _jwtService.decodeToken(authResponse.data!.token);
          if (userModel != null) {
            await _storageService.saveUserData(userModel);
          }

          LoggingService.authLog('Login successful', details: 'User: ${userModel?.name}, RequirePasswordChange: ${authResponse.requirePasswordChange}');

          return ApiResponse.success(
            message: authResponse.message,
            data: authResponse.data,
            // requirePasswordChange bilgisini response'a ekle
            extraData: {'requirePasswordChange': authResponse.requirePasswordChange ?? false},
          );
        } else {
          LoggingService.authLog('Login failed', details: authResponse.message);
          return ApiResponse.error(message: authResponse.message);
        }
      } else {
        LoggingService.authLog('Login failed', details: 'Invalid response');
        return ApiResponse.error(message: 'Giriş işlemi başarısız');
      }
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e);
      LoggingService.authLog('Login error', details: errorMessage);
      return ApiResponse.error(message: errorMessage);
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'Login');
      return ApiResponse.error(message: 'Giriş işlemi sırasında bir hata oluştu');
    }
  }

  @override
  Future<ApiResponse<AuthData>> registerMember({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String deviceInfo,
  }) async {
    try {
      LoggingService.authLog('Member register attempt', details: 'Email: $email');

      final registerRequest = MemberRegisterRequest(
        registerDto: MemberRegisterDto(
          firstName: firstName,
          lastName: lastName,
          email: email,
          password: password,
        ),
        deviceInfo: deviceInfo,
      );

      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/register-member',
        data: registerRequest.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);

        if (authResponse.success && authResponse.data != null) {
          // Token'ları kaydet
          await _storageService.saveAccessToken(authResponse.data!.token);
          await _storageService.saveRefreshToken(authResponse.data!.refreshToken);

          // User data'yı decode et ve kaydet
          final userModel = _jwtService.decodeToken(authResponse.data!.token);
          if (userModel != null) {
            await _storageService.saveUserData(userModel);
          }

          LoggingService.authLog('Member register successful', details: 'User: ${userModel?.name}');

          return ApiResponse.success(
            message: authResponse.message,
            data: authResponse.data,
          );
        } else {
          LoggingService.authLog('Member register failed', details: authResponse.message);
          return ApiResponse.error(message: authResponse.message);
        }
      } else {
        LoggingService.authLog('Member register failed', details: 'Invalid response');
        return ApiResponse.error(message: 'Kayıt işlemi başarısız');
      }
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e);
      LoggingService.authLog('Member register error', details: errorMessage);
      return ApiResponse.error(message: errorMessage);
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'Member Register');
      return ApiResponse.error(message: 'Kayıt işlemi sırasında bir hata oluştu');
    }
  }

  @override
  Future<ApiResponse<AuthData>> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String deviceInfo,
  }) async {
    try {
      LoggingService.authLog('Admin register attempt', details: 'Email: $email');

      final registerRequest = RegisterRequest(
        registerDto: RegisterDto(
          firstName: firstName,
          lastName: lastName,
          email: email,
          password: password,
        ),
        deviceInfo: deviceInfo,
      );

      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/register',
        data: registerRequest.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);

        if (authResponse.success && authResponse.data != null) {
          // Token'ları kaydet
          await _storageService.saveAccessToken(authResponse.data!.token);
          await _storageService.saveRefreshToken(authResponse.data!.refreshToken);

          // User data'yı decode et ve kaydet
          final userModel = _jwtService.decodeToken(authResponse.data!.token);
          if (userModel != null) {
            await _storageService.saveUserData(userModel);
          }

          LoggingService.authLog('Admin register successful', details: 'User: ${userModel?.name}');

          return ApiResponse.success(
            message: authResponse.message,
            data: authResponse.data,
          );
        } else {
          LoggingService.authLog('Admin register failed', details: authResponse.message);
          return ApiResponse.error(message: authResponse.message);
        }
      } else {
        LoggingService.authLog('Admin register failed', details: 'Invalid response');
        return ApiResponse.error(message: 'Kayıt işlemi başarısız');
      }
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e);
      LoggingService.authLog('Admin register error', details: errorMessage);
      return ApiResponse.error(message: errorMessage);
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'Admin Register');
      return ApiResponse.error(message: 'Kayıt işlemi sırasında bir hata oluştu');
    }
  }

  @override
  Future<ApiResponse<AuthData>> refreshToken({
    required String refreshToken,
    required String deviceInfo,
  }) async {
    try {
      LoggingService.authLog('Token refresh attempt');

      final refreshRequest = RefreshTokenRequest(
        refreshToken: refreshToken,
        deviceInfo: deviceInfo,
      );

      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/refresh-token',
        data: refreshRequest.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);

        if (authResponse.success && authResponse.data != null) {
          // Token'ları kaydet
          await _storageService.saveAccessToken(authResponse.data!.token);
          await _storageService.saveRefreshToken(authResponse.data!.refreshToken);

          // User data'yı decode et ve kaydet
          final userModel = _jwtService.decodeToken(authResponse.data!.token);
          if (userModel != null) {
            await _storageService.saveUserData(userModel);
          }

          LoggingService.authLog('Token refresh successful');

          return ApiResponse.success(
            message: authResponse.message,
            data: authResponse.data,
          );
        } else {
          LoggingService.authLog('Token refresh failed', details: authResponse.message);
          return ApiResponse.error(message: authResponse.message);
        }
      } else {
        LoggingService.authLog('Token refresh failed', details: 'Invalid response');
        return ApiResponse.error(message: 'Token yenileme başarısız');
      }
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e);
      LoggingService.authLog('Token refresh error', details: errorMessage);
      return ApiResponse.error(message: errorMessage);
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'Token Refresh');
      return ApiResponse.error(message: 'Token yenileme sırasında bir hata oluştu');
    }
  }

  @override
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      LoggingService.authLog('Change password attempt');

      final changePasswordRequest = ChangePasswordRequest(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/change-password',
        data: changePasswordRequest.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data!,
          (json) => {},
        );

        // Şifre değiştirme başarılıysa requirePasswordChange flag'ini temizle (Angular frontend pattern)
        if (apiResponse.success) {
          await _storageService.clearRequirePasswordChange();
        }

        LoggingService.authLog(
          apiResponse.success ? 'Change password successful' : 'Change password failed',
          details: apiResponse.message,
        );

        return apiResponse;
      } else {
        LoggingService.authLog('Change password failed', details: 'Invalid response');
        return ApiResponse.error(message: 'Şifre değiştirme başarısız');
      }
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e);
      LoggingService.authLog('Change password error', details: errorMessage);
      return ApiResponse.error(message: errorMessage);
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'Change Password');
      return ApiResponse.error(message: 'Şifre değiştirme sırasında bir hata oluştu');
    }
  }

  @override
  Future<ApiResponse<UserModel>> getProfile() async {
    try {
      LoggingService.authLog('Get profile attempt');

      final response = await _apiService.get<Map<String, dynamic>>('/user/profile');

      if (response.statusCode == 200 && response.data != null) {
        // Backend response format: { success: true, data: { user: {...}, member: {...} } }
        final responseData = response.data!;
        final success = responseData['success'] as bool? ?? false;

        if (success) {
          final data = responseData['data'] as Map<String, dynamic>?;
          final userData = data?['user'] as Map<String, dynamic>?;

          if (userData != null) {
            // Mevcut kullanıcı bilgilerini al
            final currentUser = await getCurrentUser();

            if (currentUser != null) {
              // Backend'den gelen güncel bilgilerle mevcut user model'i güncelle
              final userModel = currentUser.copyWith(
                name: '${userData['firstName']} ${userData['lastName']}',
                email: userData['email'] as String,
              );

              // Local storage'ı güncelle
              await _storageService.saveUserData(userModel);

              LoggingService.authLog('Get profile successful', details: 'User: ${userModel.name}');

              return ApiResponse.success(
                message: 'Profil bilgileri alındı',
                data: userModel,
              );
            } else {
              LoggingService.authLog('Get profile failed', details: 'No current user found');
              return ApiResponse.error(message: 'Profil bilgileri alınamadı');
            }
          } else {
            LoggingService.authLog('Get profile failed', details: 'No user data in response');
            return ApiResponse.error(message: 'Profil bilgileri alınamadı');
          }
        } else {
          LoggingService.authLog('Get profile failed', details: 'Backend returned success: false');
          return ApiResponse.error(message: 'Profil bilgileri alınamadı');
        }
      } else {
        LoggingService.authLog('Get profile failed', details: 'Invalid response status: ${response.statusCode}');
        return ApiResponse.error(message: 'Profil bilgileri alınamadı');
      }
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e);
      LoggingService.authLog('Get profile error', details: errorMessage);
      return ApiResponse.error(message: errorMessage);
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'Get Profile');
      return ApiResponse.error(message: 'Profil bilgileri alınırken bir hata oluştu');
    }
  }

  @override
  Future<void> logout() async {
    try {
      LoggingService.authLog('Logout');

      // Local storage'ı temizle
      await _storageService.clearAuthTokens();

      LoggingService.authLog('Logout successful');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'Logout');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      LoggingService.authLog('Checking authentication status');

      // Storage işlemlerini paralel yap (performans optimizasyonu)
      final futures = await Future.wait([
        _storageService.getAccessToken(),
        _storageService.getRefreshToken(),
      ]);

      final accessToken = futures[0];
      final refreshToken = futures[1];

      LoggingService.authLog('Token check', details: 'AccessToken: ${accessToken != null ? "exists" : "null"}, RefreshToken: ${refreshToken != null ? "exists" : "null"}');

      if (accessToken == null || accessToken.isEmpty ||
          refreshToken == null || refreshToken.isEmpty) {
        LoggingService.authLog('No tokens found');
        return false;
      }

      // Access token geçerliliğini kontrol et
      final isAccessTokenValid = _jwtService.isTokenValid(accessToken);
      LoggingService.authLog('Access token validation', details: 'Valid: $isAccessTokenValid');

      if (isAccessTokenValid) {
        return true;
      }

      // Access token geçersizse refresh token ile yenilemeyi dene
      LoggingService.authLog('Access token expired, attempting refresh');

      // Device info al - önce storage'dan, yoksa yeni oluştur
      DeviceInfo? deviceInfo = await _storageService.getDeviceInfo();
      if (deviceInfo == null) {
        final deviceService = DeviceService();
        deviceInfo = await deviceService.getDeviceInfo();
        await _storageService.saveDeviceInfo(deviceInfo);
      }
      final deviceInfoString = deviceInfo.toDeviceInfoString();

      final refreshResult = await this.refreshToken(
        refreshToken: refreshToken,
        deviceInfo: deviceInfoString,
      );

      if (refreshResult.success && refreshResult.data != null) {
        LoggingService.authLog('Token refreshed successfully during auth check');
        return true;
      } else {
        LoggingService.authLog('Token refresh failed during auth check', details: refreshResult.message);
        // Refresh token da geçersizse token'ları temizle
        await _storageService.clearAuthTokens();
        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'AuthRepository isAuthenticated');
      return false;
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      // Önce storage'dan al
      final userModel = await _storageService.getUserData();
      if (userModel != null) {
        return userModel;
      }

      // Storage'da yoksa token'dan decode et
      final accessToken = await _storageService.getAccessToken();
      if (accessToken != null && accessToken.isNotEmpty) {
        return _jwtService.decodeToken(accessToken);
      }

      return null;
    } catch (e) {
      LoggingService.authLog('Get current user error', details: e.toString());
      return null;
    }
  }

  @override
  Future<bool> isTokenValid() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      return _jwtService.isTokenValid(accessToken);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> shouldRefreshToken() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      return _jwtService.shouldRefreshToken(accessToken);
    } catch (e) {
      return true;
    }
  }

  /// Dio error'ını user-friendly message'a çevir
  String _handleDioError(DioException error) {
    if (error.response?.data != null) {
      final data = error.response!.data;
      if (data is Map<String, dynamic> && data['message'] != null) {
        return data['message'].toString();
      }
    }

    final statusCode = error.response?.statusCode;
    if (statusCode != null) {
      switch (statusCode) {
        case 400:
          return 'Geçersiz istek';
        case 401:
          return 'Oturum süreniz doldu, lütfen tekrar giriş yapın';
        case 403:
          return 'Bu işlem için yetkiniz bulunmuyor';
        case 404:
          return 'İstenen kaynak bulunamadı';
        case 429:
          return 'Çok fazla istek gönderildi, lütfen bekleyin';
        case >= 500:
          return 'Sunucu hatası oluştu';
        default:
          return 'Bir hata oluştu';
      }
    } else {
      return 'Bir hata oluştu';
    }
  }
}
