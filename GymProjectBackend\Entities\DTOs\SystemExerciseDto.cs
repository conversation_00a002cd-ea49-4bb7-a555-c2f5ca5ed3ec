using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class SystemExerciseDto : IDto
    {
        public int SystemExerciseID { get; set; }
        public int ExerciseCategoryID { get; set; }
        public string CategoryName { get; set; }
        public string ExerciseName { get; set; }
        public string? Description { get; set; }
        public string? Instructions { get; set; }
        public string? MuscleGroups { get; set; }
        public string? Equipment { get; set; }
        public byte? DifficultyLevel { get; set; }
        public string? DifficultyLevelText { get; set; } // "Başlangıç", "Orta", "İleri"
        public bool? IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
    }

    public class SystemExerciseAddDto : IDto
    {
        public int ExerciseCategoryID { get; set; }
        public string ExerciseName { get; set; }
        public string? Description { get; set; }
        public string? Instructions { get; set; }
        public string? MuscleGroups { get; set; }
        public string? Equipment { get; set; }
        public byte? DifficultyLevel { get; set; }
    }

    public class SystemExerciseUpdateDto : IDto
    {
        public int SystemExerciseID { get; set; }
        public int ExerciseCategoryID { get; set; }
        public string ExerciseName { get; set; }
        public string? Description { get; set; }
        public string? Instructions { get; set; }
        public string? MuscleGroups { get; set; }
        public string? Equipment { get; set; }
        public byte? DifficultyLevel { get; set; }
        public bool? IsActive { get; set; }
    }

    public class SystemExerciseFilterDto : IDto
    {
        public int? ExerciseCategoryID { get; set; }
        public string? SearchTerm { get; set; }
        public byte? DifficultyLevel { get; set; }
        public string? Equipment { get; set; }
        public string? ExerciseType { get; set; } // "System" veya "Company"
        public bool? IsActive { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
