﻿using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;

namespace DataAccess.Abstract
{
    public interface IUserLicenseDal : IEntityRepository<UserLicense>
    {
        List<UserLicenseDto> GetUserLicenseDetails();
        PaginatedUserLicenseDto GetUserLicenseDetailsPaginated(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax);
        PaginatedUserLicenseDto GetExpiredAndPassiveLicenses(int page, int pageSize, string searchTerm);
        List<UserLicenseDto> GetActiveUserLicensesByUserId(int userId);
        UserLicenseDto GetUserLicenseDetail(int userLicenseId);
    }

}
