import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { BaseApiService } from './baseApiService';
import { User } from '../models/user';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UserService extends BaseApiService {

  constructor(private httpClient:HttpClient) {
    super();
  }
  getAll(): Observable<ListResponseModel<User>> {
    let newPath = this.apiUrl + 'user/getall';
    return this.httpClient.get<ListResponseModel<User>>(newPath);
  }

  getUserProfile(): Observable<any> {
    let newPath = this.apiUrl + 'user/profile';
    return this.httpClient.get<any>(newPath).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error(response.message || 'Profil bilgileri alınamadı');
      })
    );
  }

  /**
   * Member rolü olmayan kullanıcıları getirir (Küçük sistemler için)
   */
  getNonMembers(): Observable<ListResponseModel<User>> {
    let newPath = this.apiUrl + 'user/getnonmembers';
    return this.httpClient.get<ListResponseModel<User>>(newPath);
  }

  /**
   * Member rolü olmayan kullanıcıları sayfalı olarak getirir (10K+ kullanıcı için)
   */
  getNonMembersPaginated(page: number = 1, pageSize: number = 20, searchTerm: string = ''): Observable<any> {
    let newPath = this.apiUrl + `user/getnonmemberspaginated?page=${page}&pageSize=${pageSize}&searchTerm=${encodeURIComponent(searchTerm)}`;
    return this.httpClient.get<any>(newPath);
  }

  /**
   * Member rolü olmayan kullanıcı sayısını getirir
   */
  getNonMembersCount(searchTerm: string = ''): Observable<any> {
    let newPath = this.apiUrl + `user/getnonmemberscount?searchTerm=${encodeURIComponent(searchTerm)}`;
    return this.httpClient.get<any>(newPath);
  }

}
