/* Modern Exercise Day Modal Styles */

/* Modal Container Override */
::ng-deep .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: var(--border-radius-lg) !important;
  overflow: hidden !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
}

::ng-deep .mat-mdc-dialog-content {
  padding: 0 !important;
  margin: 0 !important;
  max-height: none !important;
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

/* Modal Form Container */
.modal-form-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* Flexbox için gerekli */
}

/* Modal Header */
.modern-modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* <PERSON><PERSON>'ın küçülmesini engelle */
}

.modal-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.modal-title {
  margin: 0;
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.25rem;
}

.modern-btn-close {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed) var(--transition-timing);
}

.modern-btn-close:hover {
  background-color: var(--danger-light);
  color: var(--danger);
  transform: scale(1.1);
}

/* Modal Body */
.modern-modal-body {
  padding: 2rem;
  background-color: var(--bg-primary);
  flex: 1; /* Kalan alanı kapla */
  overflow-y: auto; /* Scroll sadece body'de */
  min-height: 0; /* Flexbox için gerekli */
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.modern-modal-body::-webkit-scrollbar {
  width: 8px;
}

.modern-modal-body::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
}

.modern-modal-body::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: var(--border-radius-md);
}

.modern-modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Scroll fade effect */
.modern-modal-body::before {
  content: '';
  position: sticky;
  top: 0;
  height: 20px;
  background: linear-gradient(to bottom, var(--bg-primary), transparent);
  z-index: 1;
  pointer-events: none;
}

.modern-modal-body::after {
  content: '';
  position: sticky;
  bottom: 0;
  height: 20px;
  background: linear-gradient(to top, var(--bg-primary), transparent);
  z-index: 1;
  pointer-events: none;
}



/* Add Exercise Button */
.add-exercise-section {
  margin-bottom: 1rem;
}

/* Sticky Add Exercise Button */
.add-exercise-section-sticky {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--bg-primary);
  padding: 0.75rem 0;
  margin-bottom: 0.75rem;
  /* Shadow for separation */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* Backdrop blur effect */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.add-exercise-btn {
  width: 100%;
  background: none;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1rem;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  background-color: var(--bg-secondary);
}

.add-exercise-btn:hover {
  border-color: var(--primary);
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.add-exercise-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.add-exercise-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.add-exercise-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.add-exercise-subtitle {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

/* Exercises Container */
.exercises-container {
  margin-bottom: 1rem;
}

.exercises-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.completion-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.completion-bar {
  width: 100px;
  height: 6px;
  background-color: var(--bg-tertiary);
  border-radius: var(--border-radius-pill);
  overflow: hidden;
}

.completion-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--success), var(--primary));
  border-radius: var(--border-radius-pill);
  transition: width 0.6s ease;
}

/* Modern Exercise Cards */
.modern-exercise-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-secondary);
  margin-bottom: 1rem;
  transition: all var(--transition-speed) var(--transition-timing);
  overflow: hidden;
}

.modern-exercise-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.modern-exercise-card.complete {
  border-color: var(--success);
  background-color: var(--success-light);
}

.modern-exercise-card.incomplete {
  border-color: var(--warning);
}

/* Exercise Card Header */
.exercise-card-header {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exercise-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.exercise-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.exercise-number .number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.exercise-title {
  flex: 1;
}

.exercise-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.exercise-details {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.exercise-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  font-size: 0.875rem;
}

.move-btn {
  background: var(--info-light);
  color: var(--info);
}

.move-btn:hover:not(:disabled) {
  background: var(--info);
  color: white;
  transform: scale(1.1);
}

.move-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-btn {
  background: var(--danger-light);
  color: var(--danger);
}

.delete-btn:hover {
  background: var(--danger);
  color: white;
  transform: scale(1.1);
}

/* Exercise Card Body */
.exercise-card-body {
  padding: 1rem;
}

.field-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.375rem;
  font-size: 0.8rem;
}

.field-error {
  color: var(--danger);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Exercise Selection Field */
.exercise-selection-field {
  margin-bottom: 1rem;
}

.exercise-selector {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 0.75rem;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  background-color: var(--bg-primary);
}

.exercise-selector:hover {
  border-color: var(--primary);
  background-color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.exercise-selector.selected {
  border-color: var(--success);
  background-color: var(--success-light);
}

.exercise-selector.is-invalid {
  border-color: var(--danger);
}

.selector-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selector-text .placeholder {
  color: var(--text-secondary);
  font-style: italic;
}

.selector-text .selected-exercise {
  color: var(--text-primary);
  font-weight: 600;
}

.selector-icon {
  color: var(--primary);
  font-size: 1.125rem;
}

/* Sets and Reps Group */
.sets-reps-group {
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  padding: 0.75rem;
}

.number-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background: var(--bg-primary);
}

.number-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--primary-light);
  color: var(--primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed) var(--transition-timing);
}

.number-btn:hover:not(:disabled) {
  background: var(--primary);
  color: white;
}

.number-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.number-input {
  flex: 1;
  border: none;
  padding: 0.375rem;
  text-align: center;
  font-weight: 600;
  background: transparent;
  color: var(--text-primary);
}

.number-input:focus {
  outline: none;
}

/* Notes Field */
.notes-field {
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  padding: 0.75rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  border: 2px dashed var(--border-color);
}

.empty-state-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--primary-light);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1rem;
}

.empty-state-text h6 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-state-text p {
  color: var(--text-secondary);
  margin: 0;
}

/* Scroll to Top Button */
.scroll-to-top-container {
  position: sticky;
  bottom: 1rem;
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
  pointer-events: none; /* Container'a tıklanmasın */
}

.scroll-to-top-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: var(--primary);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-speed) var(--transition-timing);
  pointer-events: auto; /* Butona tıklanabilsin */
  z-index: 5;
}

.scroll-to-top-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.scroll-to-top-btn:active {
  transform: translateY(0) scale(0.95);
}

/* Modern Modal Footer - Sabit pozisyon */
.modern-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  flex-shrink: 0; /* Footer'ın küçülmesini engelle */
  /* Shadow for separation */
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  /* Backdrop blur effect */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

/* Footer info styling */
.footer-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-info small {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-pill);
  border: 1px solid var(--border-color);
  font-weight: 500;
}

/* Floating action buttons */
.footer-actions .modern-btn {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all var(--transition-speed) var(--transition-timing);
}

.footer-actions .modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Modal container adjustments */
  ::ng-deep .mat-mdc-dialog-container {
    max-height: 95vh !important;
    margin: 1rem !important;
  }

  .modern-modal-header,
  .modern-modal-body,
  .modern-modal-footer {
    padding: 0.75rem;
  }

  .modal-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .modal-title {
    font-size: 1.1rem;
  }



  .exercise-card-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .exercise-info {
    width: 100%;
  }

  .exercise-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .exercise-card-body {
    padding: 0.75rem;
  }

  /* Mobile footer - stack vertically but keep visible */
  .footer-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .footer-info {
    order: 2; /* Info altına */
    justify-content: center;
  }

  .footer-actions {
    order: 1; /* Butonlar üste */
    flex-direction: row; /* Yan yana tut */
    gap: 0.75rem;
  }

  .footer-actions .modern-btn {
    flex: 1; /* Eşit genişlik */
    min-height: 48px; /* Touch friendly */
  }

  /* Scroll area adjustment for mobile */
  .modern-modal-body {
    padding: 1rem;
  }

  /* Smaller cards on mobile */
  .modern-exercise-card {
    margin-bottom: 0.75rem;
  }

  .add-exercise-section-sticky {
    padding: 0.5rem 0;
    margin-bottom: 0.5rem;
  }

  .add-exercise-btn {
    padding: 0.75rem;
  }

  .add-exercise-content {
    flex-direction: column;
    gap: 0.5rem;
  }

  .add-exercise-icon {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }
}

/* Dark Mode Enhancements */
[data-theme="dark"] .modern-modal-header {
  background: linear-gradient(135deg, var(--bg-tertiary), rgba(255, 255, 255, 0.05));
}

[data-theme="dark"] .modern-modal-footer {
  background: linear-gradient(135deg, var(--bg-tertiary), rgba(255, 255, 255, 0.05));
}



[data-theme="dark"] .modern-exercise-card {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .exercise-card-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), var(--bg-tertiary));
}

[data-theme="dark"] .add-exercise-section-sticky {
  background: var(--bg-primary);
}

[data-theme="dark"] .add-exercise-btn {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .add-exercise-btn:hover {
  background-color: var(--primary-light);
}

[data-theme="dark"] .exercise-selector {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .exercise-selector:hover {
  background-color: var(--primary-light);
}

[data-theme="dark"] .sets-reps-group,
[data-theme="dark"] .notes-field {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .number-input-wrapper {
  background-color: var(--bg-secondary);
}

[data-theme="dark"] .empty-state {
  background-color: var(--bg-tertiary);
}

/* Animation Enhancements */
.modern-exercise-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.completion-progress {
  animation: progressFill 0.8s ease-out;
}

@keyframes progressFill {
  from {
    width: 0;
  }
  to {
    width: var(--progress-width, 0%);
  }
}

/* Validation States */
.is-invalid {
  border-color: var(--danger) !important;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}
